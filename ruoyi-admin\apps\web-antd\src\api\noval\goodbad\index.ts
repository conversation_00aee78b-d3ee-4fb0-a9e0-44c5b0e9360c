import type { Goodbad<PERSON>, GoodbadForm, GoodbadQuery } from './model';

import type { PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

/**
 * 查询评论的点赞与踩列表
 * @param params
 * @returns 评论的点赞与踩列表
 */
export function goodbadList(params?: GoodbadQuery) {
  return requestClient.get<PageResult<GoodbadVO>>('/noval/goodbad/list', {
    params,
  });
}

/**
 * 新增评论的点赞与踩
 * 后端使用PUT接口处理新增和更新
 * @param data
 * @returns void
 */
export function goodbadAdd(data: GoodbadForm) {
  return requestClient.putWithMsg<void>('/noval/goodbad', data);
}

/**
 * 更新评论的点赞与踩
 * @param data
 * @returns void
 */
export function goodbadUpdate(data: GoodbadForm) {
  return requestClient.putWithMsg<void>('/noval/goodbad', data);
}

/**
 * 查询用户对特定评论的点赞/踩记录
 * 通过list接口查询特定用户和评论的记录
 * @param commerntId 评论ID
 * @param whoId 用户ID
 * @returns 点赞/踩记录
 */
export async function goodbadInfo(
  commerntId: number | string,
  whoId: number | string,
) {
  const response = await requestClient.get<PageResult<GoodbadVO>>(
    '/noval/goodbad/list',
    {
      params: {
        commerntId,
        whoId,
        pageNum: 1,
        pageSize: 1,
      },
    },
  );
  // 返回第一条记录，如果没有记录则返回null
  return response.rows && response.rows.length > 0 ? response.rows[0] : null;
}

/**
 * 统计评论的点赞和点踩数量
 * @param commerntId 评论ID
 * @returns { likeCount: number, dislikeCount: number }
 */
export async function goodbadStats(commerntId: number | string) {
  try {
    const response = await requestClient.get<PageResult<GoodbadVO>>(
      '/noval/goodbad/list',
      {
        params: {
          commerntId,
          pageNum: 1,
          pageSize: 1000, // 获取所有记录
        },
      },
    );

    const records = response.rows || [];

    // 统计点赞数（goodBad = 1）和点踩数（goodBad = 0）
    const likeCount = records.filter(record => record.goodBad === 1).length;
    const dislikeCount = records.filter(record => record.goodBad === 0).length;

    return { likeCount, dislikeCount };
  } catch (error) {
    console.error('统计点赞数据失败:', error);
    return { likeCount: 0, dislikeCount: 0 };
  }
}

/**
 * 批量统计多个评论的点赞和点踩数量
 * @param commerntIds 评论ID数组
 * @returns Map<commentId, { likeCount: number, dislikeCount: number }>
 */
export async function goodbadBatchStats(commerntIds: (number | string)[]) {
  const statsMap = new Map<number | string, { likeCount: number, dislikeCount: number }>();

  try {
    // 获取所有相关的点赞记录
    const response = await requestClient.get<PageResult<GoodbadVO>>(
      '/noval/goodbad/list',
      {
        params: {
          pageNum: 1,
          pageSize: 10000, // 获取大量记录
        },
      },
    );

    const allRecords = response.rows || [];

    // 为每个评论ID统计数据
    commerntIds.forEach(commentId => {
      const commentRecords = allRecords.filter(record =>
        record.commerntId.toString() === commentId.toString()
      );

      const likeCount = commentRecords.filter(record => record.goodBad === 1).length;
      const dislikeCount = commentRecords.filter(record => record.goodBad === 0).length;

      statsMap.set(commentId, { likeCount, dislikeCount });
    });

    return statsMap;
  } catch (error) {
    console.error('批量统计点赞数据失败:', error);
    // 返回默认值
    commerntIds.forEach(commentId => {
      statsMap.set(commentId, { likeCount: 0, dislikeCount: 0 });
    });
    return statsMap;
  }
}

/**
 * 删除评论的点赞与踩
 * 先查询记录ID，然后删除
 * @param commerntId 评论ID
 * @param whoId 用户ID
 * @returns void
 */
export async function goodbadRemove(
  commerntId: number | string,
  whoId: number | string,
) {
  // 先查询记录获取ID
  const record = await goodbadInfo(commerntId, whoId);
  if (!record) {
    throw new Error('未找到要删除的记录');
  }

  // 使用记录ID删除，后端接口需要ID数组
  const recordId = record.id || record.commerntId;
  return requestClient.deleteWithMsg<void>(`/noval/goodbad/${recordId}`);
}
