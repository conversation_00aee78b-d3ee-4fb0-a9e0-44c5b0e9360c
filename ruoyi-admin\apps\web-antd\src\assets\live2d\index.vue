<template>
  <div class="index">
    <div class="waifu">

      <!-- 提示框 -->
      <div class="waifu-tips"></div>

      <!-- 看板娘画布 -->
      <canvas id="live2d" class="live2d"/>

      <!-- 工具栏 -->
      <div class="waifu-tool">
        <p class="fui-home">
          <i class="el-icon-s-home"/>
        </p>
        <p class="fui-chat">
          <i class="el-icon-upload"/>
        </p>
        <p class="fui-eye">
          <i class="el-icon-share"/>
        </p>
        <p class="fui-user">
          <i class="el-icon-warning"/>
        </p>
        <p class="fui-photo">
          <i class="el-icon-camera-solid"/>
        </p>
        <p class="fui-info-circle">
          <i class="el-icon-s-comment"/>
        </p>
        <p class="fui-cross">
          <i class="el-icon-error"/>
        </p>
      </div>

    </div>
  </div>
</template>

<script>
  import waifuTipsJson from './waifu-tips.json'
  import './waifu.css'

  export default {
    name: 'PhyLive2d',
    components: {
    },
    data() {
      return {
      }
    },
    async mounted () {
      // 旧版 Live2D 系统已禁用
      console.log('旧版 Live2D 系统已禁用，请使用新版 Live2D 组件')
      return

      try {
        console.log('Live2D component mounting...')
        console.log('Browser:', navigator.userAgent)

        // 首先加载jQuery
        if (typeof window.$ === 'undefined') {
          console.log('Loading jQuery...')
          // 动态导入jQuery
          const $ = await import('jquery')
          window.$ = $.default
          window.jQuery = $.default
          console.log('jQuery loaded successfully')
        } else {
          console.log('jQuery already available')
        }

        // 加载Live2D
        this.loadLive2D()
      } catch (error) {
        console.error('Live2D initialization failed:', error)
      }
    },
    methods: {
      async loadLive2D() {
        try {
          // 检查WebGL支持
          if (!this.checkWebGLSupport()) {
            console.error('WebGL is not supported in this browser')
            return
          }

          // 动态导入模块
          const live2dModule = await import('./live2d.js?url')
          const waifuTipsModule = await import('./waifu-tips.js?url')

          // 创建并加载live2d.js脚本
          const live2dScript = document.createElement('script')
          live2dScript.src = live2dModule.default
          live2dScript.crossOrigin = 'anonymous'  // 添加跨域支持

          live2dScript.onerror = (error) => {
            console.error('Failed to load live2d.js:', error)
          }

          document.head.appendChild(live2dScript)

          // 等待live2d.js加载完成后再加载waifu-tips.js
          live2dScript.onload = () => {
            const waifuScript = document.createElement('script')
            waifuScript.src = waifuTipsModule.default
            waifuScript.crossOrigin = 'anonymous'  // 添加跨域支持

            waifuScript.onerror = (error) => {
              console.error('Failed to load waifu-tips.js:', error)
            }

            waifuScript.onload = () => {
              // 确保全局变量可用
              if (typeof window.live2d_settings === 'undefined') {
                window.live2d_settings = {}
              }

              window.live2d_settings['modelId'] = 5;                  // 默认模型 ID
              window.live2d_settings['modelTexturesId'] = 1;          // 默认材质 ID

              // 延迟初始化，确保DOM完全加载
              setTimeout(() => {
                if (typeof window.initModel === 'function') {
                  console.log('Initializing Live2D model...')
                  window.initModel(waifuTipsJson)

                  // 检查canvas元素
                  setTimeout(() => {
                    const canvas = document.getElementById('live2d')
                    const waifuContainer = document.querySelector('.waifu')
                    console.log('Canvas element:', canvas)
                    console.log('Waifu container:', waifuContainer)
                    if (canvas) {
                      console.log('Canvas dimensions:', canvas.width, 'x', canvas.height)
                      console.log('Canvas style:', canvas.style.cssText)
                    }
                  }, 1000)
                } else {
                  console.error('initModel function not found')
                }
              }, 100)
            }
            document.head.appendChild(waifuScript)
          }

        } catch (error) {
          console.error('Live2D scripts loading failed:', error)
        }
      },

      checkWebGLSupport() {
        try {
          const canvas = document.createElement('canvas')
          const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
          return !!gl
        } catch (e) {
          return false
        }
      }
    }
  }
</script>

<style scoped>
  .waifu-tool p i {
    cursor: pointer;
  }

  /* 确保Live2D容器可见 */
  .index {
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9999;
    pointer-events: none;
  }

  .waifu {
    pointer-events: auto;
  }

  /* Live2D看板娘样式 */
  .waifu {
    /* 正常显示，无调试边框 */
  }
</style>
