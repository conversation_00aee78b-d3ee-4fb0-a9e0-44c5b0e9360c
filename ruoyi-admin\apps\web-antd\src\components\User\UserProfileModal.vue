<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';
import { Modal, Avatar, Button, Space, List, Empty, message } from 'ant-design-vue';
import { UserOutlined, PlusOutlined, MessageOutlined, EyeOutlined, CheckOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import { commentList } from '#/api/noval/comment';
import { attentionList, attentionAdd, attentionRemove } from '#/api/noval/attention';
import { findUserInfo } from '#/api/system/user';
import type { CommentVO } from '#/api/noval/comment/model';

interface Props {
  visible: boolean;
  userId: string | number;
  userName?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
  (e: 'startChat', userId: string | number, userName: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const router = useRouter();
const userStore = useUserStore();
const currentUser = computed(() => userStore.userInfo);

const userInfo = ref<{
  userName: string;
  userAvatar: string;
  nickName?: string;
}>({
  userName: props.userName || '未知用户',
  userAvatar: '',
});

const userPosts = ref<CommentVO[]>([]);
const loading = ref(false);
const isFollowing = ref(false);
const followLoading = ref(false);

// 默认头像
const defaultAvatar = 'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp';

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  if (userAvatar && userAvatar.trim() && userAvatar !== 'null' && userAvatar !== 'undefined') {
    return userAvatar.trim();
  }
  return defaultAvatar;
}

// 获取用户详细信息
async function getUserDetails() {
  try {
    const userDetails = await findUserInfo(props.userId);
    userInfo.value = {
      userName: userDetails.user?.nickName || userDetails.user?.userName || '未知用户',
      userAvatar: getValidAvatarUrl(userDetails.user?.avatar),
      nickName: userDetails.user?.nickName,
    };
  } catch (error) {
    console.error('获取用户详情失败:', error);
  }
}

// 获取用户帖子
async function getUserPosts() {
  loading.value = true;
  try {
    const response = await commentList({
      pageNum: 1,
      pageSize: 20,
      commerntType: 0, // 只获取帖子
      myId: props.userId,
    });
    userPosts.value = response.rows || [];
  } catch (error) {
    console.error('获取用户帖子失败:', error);
    userPosts.value = [];
  } finally {
    loading.value = false;
  }
}

// 检查是否已关注
async function checkFollowStatus() {
  try {
    const result = await attentionList({
      pageNum: 1,
      pageSize: 1,
      id: currentUser.value?.userId,
      otherId: props.userId,
    });
    isFollowing.value = result.rows && result.rows.length > 0;
  } catch (error) {
    console.error('检查关注状态失败:', error);
    isFollowing.value = false;
  }
}

// 关注/取消关注用户
async function toggleFollow() {
  if (followLoading.value) return;

  followLoading.value = true;
  try {
    if (isFollowing.value) {
      // 取消关注 - 需要先查找记录ID
      const result = await attentionList({
        pageNum: 1,
        pageSize: 1,
        id: currentUser.value?.userId,
        otherId: props.userId,
      });

      if (result.rows && result.rows.length > 0) {
        await attentionRemove(result.rows[0].id);
        isFollowing.value = false;
        // 只显示一次成功消息
        if (!document.querySelector('.ant-message')) {
          message.success('取消关注成功');
        }
      }
    } else {
      // 关注
      await attentionAdd({
        id: currentUser.value?.userId,
        otherId: props.userId,
      });
      isFollowing.value = true;
      // 只显示一次成功消息
      if (!document.querySelector('.ant-message')) {
        message.success('关注成功');
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    followLoading.value = false;
  }
}

// 开始私聊
function startChat() {
  emit('startChat', props.userId, userInfo.value.userName);
}

// 查看帖子详情
function viewPostDetail(postId: number | string) {
  emit('close');
  router.push({
    path: '/CommentForYh/comment-section',
    query: {
      postId: postId.toString(),
      view: 'detail',
    },
  });
}

// 获取帖子标题
function getPostTitle(content: string) {
  const lines = content.split('\n');
  return lines[0] || '无标题';
}

// 获取帖子预览
function getPostPreview(content: string) {
  const lines = content.split('\n');
  const contentLines = lines.slice(1).join('\n').trim();
  return contentLines.length > 100 ? `${contentLines.slice(0, 100)}...` : contentLines;
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false);
  emit('close');
}

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.userId) {
    getUserDetails();
    getUserPosts();
    checkFollowStatus();
  }
});

// 监听userId变化
watch(() => props.userId, (newUserId) => {
  if (newUserId && props.visible) {
    getUserDetails();
    getUserPosts();
    checkFollowStatus();
  }
});
</script>

<template>
  <Modal
    :visible="visible"
    :title="null"
    width="700px"
    :footer="null"
    @cancel="handleClose"
    class="user-profile-modal p3r-modal"
  >
    <div class="p3r-user-profile">
      <!-- 用户信息头部 -->
      <div class="user-header">
        <div class="user-avatar-section">
          <Avatar
            :src="userInfo.userAvatar"
            :size="80"
            class="user-avatar"
          >
            <template #icon>
              <UserOutlined />
            </template>
          </Avatar>
        </div>

        <div class="user-info-section">
          <h2 class="user-name">{{ userInfo.userName }}</h2>
          <p v-if="userInfo.nickName && userInfo.nickName !== userInfo.userName" class="user-nickname">
            {{ userInfo.nickName }}
          </p>

          <div class="user-actions">
            <Space>
              <Button
                :type="isFollowing ? 'default' : 'primary'"
                :icon="h(isFollowing ? CheckOutlined : PlusOutlined)"
                :loading="followLoading"
                @click="toggleFollow"
                class="follow-btn"
                :class="{ 'following': isFollowing }"
              >
                {{ isFollowing ? '已关注' : '关注' }}
              </Button>
              <Button
                type="default"
                :icon="h(MessageOutlined)"
                @click="startChat"
                class="chat-btn"
              >
                私聊
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <!-- 用户帖子列表 -->
      <div class="user-posts-section">
        <h3 class="section-title">TA的帖子</h3>

        <div v-if="loading" class="loading-section">
          <div class="text-center py-8">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>
        </div>

        <div v-else-if="userPosts.length === 0" class="empty-section">
          <Empty
            description="还没有发表帖子"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </div>

        <div v-else class="posts-list">
          <List :data-source="userPosts" item-layout="vertical" size="small">
            <template #renderItem="{ item: post }">
              <List.Item
                class="post-item"
                @click="viewPostDetail(post.commerntId)"
              >
                <div class="post-content">
                  <h4 class="post-title">
                    {{ getPostTitle(post.content || '') }}
                  </h4>
                  <div class="post-preview">
                    {{ getPostPreview(post.content || '') }}
                  </div>
                  <div class="post-meta">
                    <span class="post-time">
                      {{ formatDateTime(post.createTime) }}
                    </span>
                    <span class="post-stats">
                      <EyeOutlined /> {{ post.look || 0 }}
                    </span>
                  </div>
                </div>
              </List.Item>
            </template>
          </List>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
/* P3R风格样式 */
.p3r-modal :deep(.ant-modal-content) {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 30px rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(20px);
}

.p3r-modal :deep(.ant-modal-header) {
  background: transparent;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.p3r-modal :deep(.ant-modal-close) {
  color: #64748b;
}

.p3r-modal :deep(.ant-modal-close:hover) {
  color: #3b82f6;
}

.p3r-user-profile {
  color: #e2e8f0;
  font-family: 'Rajdhani', sans-serif;
}

.user-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(30, 41, 59, 0.3) 100%);
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.user-avatar {
  border: 3px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.user-info-section {
  flex: 1;
}

.user-name {
  color: #3b82f6;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  font-family: 'Orbitron', sans-serif;
}

.user-nickname {
  color: #94a3b8;
  font-size: 14px;
  margin: 0 0 16px 0;
}

.user-actions {
  margin-top: 16px;
}

.follow-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.follow-btn:hover {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.follow-btn.following {
  background: linear-gradient(135deg, #64748b, #475569);
  border-color: rgba(100, 116, 139, 0.5);
}

.follow-btn.following:hover {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: rgba(239, 68, 68, 0.5);
}

.chat-btn {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  font-weight: 600;
  transition: all 0.3s ease;
}

.chat-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.6);
  color: #60a5fa;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
}

.user-posts-section {
  padding: 0 24px 24px;
}

.section-title {
  color: #3b82f6;
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 16px 0;
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.loading-section {
  text-align: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  color: #64748b;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-section {
  text-align: center;
  padding: 40px 0;
}

.posts-list {
  max-height: 400px;
  overflow-y: auto;
}

.post-item {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.post-title {
  color: #3b82f6;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.post-preview {
  color: #94a3b8;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #64748b;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 滚动条样式 */
.posts-list::-webkit-scrollbar {
  width: 6px;
}

.posts-list::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 3px;
}

.posts-list::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

.posts-list::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}
</style>
