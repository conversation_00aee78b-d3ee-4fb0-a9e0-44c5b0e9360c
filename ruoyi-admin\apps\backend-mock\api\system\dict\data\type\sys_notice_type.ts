export default eventHandler(async (event) => {
  return useResponseSuccess([
    {
      dictCode: 1,
      dictLabel: '通知',
      dictValue: '1',
      dictType: 'sys_notice_type',
      dictSort: 1,
      status: '0',
      remark: '系统通知',
      createTime: '2024-01-01 00:00:00',
    },
    {
      dictCode: 2,
      dictLabel: '公告',
      dictValue: '2',
      dictType: 'sys_notice_type',
      dictSort: 2,
      status: '0',
      remark: '系统公告',
      createTime: '2024-01-01 00:00:00',
    },
  ]);
});
