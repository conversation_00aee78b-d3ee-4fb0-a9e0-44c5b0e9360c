// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/auth/codes': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/codes').default>>>>
    }
    '/api/auth/login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/login.post').default>>>>
    }
    '/api/auth/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/logout.post').default>>>>
    }
    '/api/auth/refresh': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/refresh.post').default>>>>
    }
    '/api/menu/all': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/menu/all').default>>>>
    }
    '/api/noval/manage/data': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/manage/data').default>>>>
    }
    '/api/noval/manage': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/manage/index.post').default>>>>
    }
    '/api/noval/news/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/[id].get').default>>>>
    }
    '/api/noval/news/data': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/data').default>>>>
    }
    '/api/noval/news': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/index.post').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/index.put').default>>>>
    }
    '/api/noval/news/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/list').default>>>>
    }
    '/api/noval/news/list2': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/noval/news/list2.get').default>>>>
    }
    '/api/proxy': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/proxy').default>>>>
    }
    '/api/resource/oss/upload': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/resource/oss/upload.post').default>>>>
    }
    '/api/status': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/status').default>>>>
    }
    '/api/system/dept/': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/.post').default>>>>
    }
    '/api/system/dept/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/[id].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/[id].put').default>>>>
    }
    '/api/system/dept/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/list').default>>>>
    }
    '/api/system/dict/data/type/new_type': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dict/data/type/new_type').default>>>>
    }
    '/api/system/dict/data/type/sys_notice_type': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dict/data/type/sys_notice_type').default>>>>
    }
    '/api/system/dict/data/type/sys_noval_state': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dict/data/type/sys_noval_state').default>>>>
    }
    '/api/system/menu/getRouters': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/getRouters').default>>>>
    }
    '/api/system/menu/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/list').default>>>>
    }
    '/api/system/menu/name-exists': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/name-exists').default>>>>
    }
    '/api/system/menu/path-exists': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/path-exists').default>>>>
    }
    '/api/system/notice/:noticeId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/notice/[noticeId].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/notice/[noticeId].get').default>>>>
    }
    '/api/system/notice/data': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/notice/data').default>>>>
    }
    '/api/system/notice': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/notice/index.post').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/notice/index.put').default>>>>
    }
    '/api/system/notice/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/notice/list').default>>>>
    }
    '/api/system/role/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/role/list').default>>>>
    }
    '/api/system/user/nochang/:userId': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/user/nochang/[userId].get').default>>>>
    }
    '/api/table/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/table/list').default>>>>
    }
    '/api/test': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/test.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/test.post').default>>>>
    }
    '/api/user/info': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/user/info').default>>>>
    }
    '/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/[...]').default>>>>
    }
    '/noval/NovalForYh/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/noval/NovalForYh/[...]').default>>>>
    }
  }
}
export {}