import { defineEventHand<PERSON>, readMultipartFormData } from 'h3';
import { useResponseSuccess } from '~/utils/response';

export default defineEventHandler(async (event) => {
  const formData = await readMultipartFormData(event);
  // Simulate generating an ID and URL
  const newId = Math.floor(Math.random() * 1000000).toString();
  const uploadedFile = formData.find(item => item.name === 'file');
  const fileName = uploadedFile ? uploadedFile.filename : 'default.txt';
  const path = formData.find(item => item.name === 'path')?.data.toString() || '';
  const url = `http://mock.cos.com/${path}/${fileName}`;
  const data = {
    ossId: newId,
    url: url,
    fileName: fileName,
    originalName: fileName,
    fileSuffix: '.txt',
    createTime: new Date().toISOString(),
    service: 'cos'
  };
  return { code: 200, data, msg: 'ok' };
});
