import { defineEvent<PERSON>and<PERSON>, getQuery, readBody } from 'h3';
import { faker } from '@faker-js/faker';

// Mock data store
let novels = Array.from({ length: 10 }, (_, id) => ({
  id: id + 1,
  name: faker.lorem.words(3),
  author: faker.person.fullName(),
  platform: faker.helpers.arrayElement(['PlatformA', 'PlatformB']),
  type: faker.helpers.arrayElement(['Fantasy', 'Sci-Fi']),
  status: faker.helpers.arrayElement(['Ongoing', 'Completed']),
}));

let nextId = novels.length + 1;

export default defineEventHandler(async (event) => {
  const method = event.method;
  let path = event.path;
   
  // 检查路径是否以/noval/NovalForYh开头，并移除这个前缀以便于后续处理
  const basePath = '/noval/NovalForYh';
  if (path.startsWith(basePath)) {
    path = path.substring(basePath.length);
  }

  // 处理/getInfo/{ids}格式的路由
  if (method === 'GET' && path.match(/^\/getInfo\/(.+)$/)) {
    const match = path.match(/^\/getInfo\/(.+)$/);
    if (match && match[1]) {
      const idsStr = match[1];
      const ids = idsStr.split(',').map(Number);
      const result = novels.filter(n => ids.includes(n.id));
      return { code: 200, rows: result, total: result.length };
    }
  }
  
  // 保留原有的列表查询功能，但改为/list路径
  if (method === 'GET' && path === '/list') {
    const query = getQuery(event);
    const pageNum = parseInt(query.pageNum) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    const name = query.name || '';
    const author = query.author || '';
    const platform = query.platform || '';
    const type = query.type || '';

    const filtered = novels.filter(n =>
      n.name.toLowerCase().includes(name.toLowerCase()) &&
      n.author.toLowerCase().includes(author.toLowerCase()) &&
      (!platform || n.platform === platform) &&
      (!type || n.type === type)
    );

    const start = (pageNum - 1) * pageSize;
    const paginated = filtered.slice(start, start + pageSize);

    return { code: 200, rows: paginated, total: filtered.length };
  }

  if (method === 'GET' && path.match(/^\/(\d+(,\d+)*)$/)) {
    const idsStr = path.slice(1);
    const ids = idsStr.split(',').map(Number);
    const result = novels.filter(n => ids.includes(n.id));
    return { code: 200, data: result };
  }

  if (method === 'GET' && path.match(/^\/(\d+)$/)) {
    const id = parseInt(path.slice(1));
    const novel = novels.find(n => n.id === id);
    return novel ? { code: 200, data: novel } : { code: 404, msg: 'Not found' };
  }

  if (method === 'POST' && path === '') {
    const data = await readBody(event);
    const newNovel = { id: nextId++, ...data };
    novels.push(newNovel);
    return { code: 200, data: newNovel };
  }

  if (method === 'PUT' && path === '') {
    const data = await readBody(event);
    const index = novels.findIndex(n => n.id === data.id);
    if (index !== -1) {
      novels[index] = { ...novels[index], ...data };
      return { code: 200, data: novels[index] };
    }
    return { code: 404, msg: 'Not found' };
  }

  if (method === 'POST' && path.endsWith('/remove')) {
    const { ids } = await readBody(event);
    novels = novels.filter(n => !ids.includes(n.id));
    return { code: 200, msg: 'Deleted' };
  }

  if (method === 'POST' && path.endsWith('/export')) {
    // Mock export, return blob-like response
    return { code: 200, data: 'Mock export data' };
  }

  return { code: 404, msg: 'Endpoint not found' };
});