{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/upload/src/upload.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/upload/src/util.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/upload/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/vxe-upload/index.js"], "sourcesContent": ["import { defineComponent, ref, h, reactive, watch, computed, TransitionGroup, inject, createCommentVNode, onUnmounted, onMounted } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI, getConfig, getI18n, getIcon, useSize, createEvent, globalEvents, renderEmptyElement } from '../../ui';\nimport { getSlotVNs } from '../..//ui/src/vn';\nimport { errLog, warnLog } from '../../ui/src/log';\nimport { initTpImg, getTpImg, getEventTargetNode, toCssUnit } from '../../ui/src/dom';\nimport { readLocalFile } from './util';\nimport VxeButtonComponent from '../../button/src/button';\nexport default defineComponent({\n    name: 'VxeUpload',\n    props: {\n        modelValue: [Array, String, Object],\n        showList: {\n            type: Boolean,\n            default: () => getConfig().upload.showList\n        },\n        moreConfig: Object,\n        readonly: {\n            type: Boolean,\n            default: null\n        },\n        disabled: {\n            type: Boolean,\n            default: null\n        },\n        mode: {\n            type: String,\n            default: () => getConfig().upload.mode\n        },\n        imageTypes: {\n            type: Array,\n            default: () => XEUtils.clone(getConfig().upload.imageTypes, true)\n        },\n        imageConfig: {\n            type: Object,\n            default: () => XEUtils.clone(getConfig().upload.imageConfig, true)\n        },\n        /**\n         * 已废弃，被 image-config 替换\n         * @deprecated\n         */\n        imageStyle: {\n            type: Object,\n            default: () => XEUtils.clone(getConfig().upload.imageStyle, true)\n        },\n        fileTypes: {\n            type: Array,\n            default: () => XEUtils.clone(getConfig().upload.fileTypes, true)\n        },\n        dragSort: Boolean,\n        dragToUpload: {\n            type: Boolean,\n            default: () => XEUtils.clone(getConfig().upload.dragToUpload, true)\n        },\n        pasteToUpload: {\n            type: Boolean,\n            default: () => XEUtils.clone(getConfig().upload.pasteToUpload, true)\n        },\n        keyField: String,\n        singleMode: Boolean,\n        urlMode: Boolean,\n        multiple: Boolean,\n        limitSize: {\n            type: [String, Number],\n            default: () => getConfig().upload.limitSize\n        },\n        limitCount: {\n            type: [String, Number],\n            default: () => getConfig().upload.limitCount\n        },\n        nameField: {\n            type: String,\n            default: () => getConfig().upload.nameField\n        },\n        typeField: {\n            type: String,\n            default: () => getConfig().upload.typeField\n        },\n        urlField: {\n            type: String,\n            default: () => getConfig().upload.urlField\n        },\n        sizeField: {\n            type: String,\n            default: () => getConfig().upload.sizeField\n        },\n        showErrorStatus: {\n            type: Boolean,\n            default: () => getConfig().upload.showErrorStatus\n        },\n        showProgress: {\n            type: Boolean,\n            default: () => getConfig().upload.showProgress\n        },\n        progressText: {\n            type: String,\n            default: () => getConfig().upload.progressText\n        },\n        autoHiddenButton: {\n            type: Boolean,\n            default: () => getConfig().upload.autoHiddenButton\n        },\n        showUploadButton: {\n            type: Boolean,\n            default: () => getConfig().upload.showUploadButton\n        },\n        buttonText: {\n            type: String,\n            default: () => getConfig().upload.buttonText\n        },\n        buttonIcon: {\n            type: String,\n            default: () => getConfig().upload.buttonIcon\n        },\n        showButtonText: {\n            type: Boolean,\n            default: () => getConfig().upload.showButtonText\n        },\n        showButtonIcon: {\n            type: Boolean,\n            default: () => getConfig().upload.showButtonIcon\n        },\n        showRemoveButton: {\n            type: Boolean,\n            default: () => getConfig().upload.showRemoveButton\n        },\n        showDownloadButton: {\n            type: Boolean,\n            default: () => getConfig().upload.showDownloadButton\n        },\n        showPreview: {\n            type: Boolean,\n            default: () => getConfig().upload.showPreview\n        },\n        tipText: String,\n        hintText: String,\n        previewMethod: Function,\n        uploadMethod: Function,\n        beforeRemoveMethod: Function,\n        removeMethod: Function,\n        beforeDownloadMethod: Function,\n        downloadMethod: Function,\n        getUrlMethod: Function,\n        getThumbnailUrlMethod: Function,\n        size: {\n            type: String,\n            default: () => getConfig().upload.size || getConfig().size\n        }\n    },\n    emits: [\n        'update:modelValue',\n        'add',\n        'remove',\n        'remove-fail',\n        'download',\n        'download-fail',\n        'upload-success',\n        'upload-error',\n        'sort-dragend'\n    ],\n    setup(props, context) {\n        const { emit, slots } = context;\n        const $xeForm = inject('$xeForm', null);\n        const formItemInfo = inject('xeFormItemInfo', null);\n        const $xeTable = inject('$xeTable', null);\n        const xID = XEUtils.uniqueId();\n        const { computeSize } = useSize(props);\n        const refElem = ref();\n        const refPopupElem = ref();\n        const refDragLineElem = ref();\n        const refModalDragLineElem = ref();\n        const reactData = reactive({\n            isDragUploadStatus: false,\n            showMorePopup: false,\n            isActivated: false,\n            fileList: [],\n            fileCacheMaps: {},\n            isDragMove: false,\n            dragIndex: -1,\n            dragTipText: ''\n        });\n        const internalData = {\n            imagePreviewTypes: ['jpg', 'jpeg', 'png', 'gif'],\n            prevDragIndex: -1\n            // prevDragPos: ''\n        };\n        const refMaps = {\n            refElem\n        };\n        const computeFormReadonly = computed(() => {\n            const { readonly } = props;\n            if (readonly === null) {\n                if ($xeForm) {\n                    return $xeForm.props.readonly;\n                }\n                return false;\n            }\n            return readonly;\n        });\n        const computeIsDisabled = computed(() => {\n            const { disabled } = props;\n            if (disabled === null) {\n                if ($xeForm) {\n                    return $xeForm.props.disabled;\n                }\n                return false;\n            }\n            return disabled;\n        });\n        const computeKeyField = computed(() => {\n            return props.keyField || '_X_KEY';\n        });\n        const computeIsImage = computed(() => {\n            return props.mode === 'image';\n        });\n        const computeNameProp = computed(() => {\n            return props.nameField || 'name';\n        });\n        const computeTypeProp = computed(() => {\n            return props.typeField || 'type';\n        });\n        const computeUrlProp = computed(() => {\n            return props.urlField || 'url';\n        });\n        const computeSizeProp = computed(() => {\n            return props.sizeField || 'size';\n        });\n        const computeLimitMaxSizeB = computed(() => {\n            return XEUtils.toNumber(props.limitSize) * 1024 * 1024;\n        });\n        const computeLimitMaxCount = computed(() => {\n            return props.multiple ? XEUtils.toNumber(props.limitCount) : 1;\n        });\n        const computeOverCount = computed(() => {\n            const { multiple } = props;\n            const { fileList } = reactData;\n            const limitMaxCount = computeLimitMaxCount.value;\n            if (multiple) {\n                if (limitMaxCount) {\n                    return fileList.length >= limitMaxCount;\n                }\n                return true;\n            }\n            return fileList.length >= 1;\n        });\n        const computeLimitSizeUnit = computed(() => {\n            const limitSize = XEUtils.toNumber(props.limitSize);\n            if (limitSize) {\n                if (limitSize > 1048576) {\n                    return `${limitSize / 1048576}T`;\n                }\n                if (limitSize > 1024) {\n                    return `${limitSize / 1024}G`;\n                }\n                return `${limitSize}M`;\n            }\n            return '';\n        });\n        const computedDefHintText = computed(() => {\n            const { limitSize, fileTypes, multiple, limitCount } = props;\n            const tipText = props.tipText || props.hintText;\n            const isImage = computeIsImage.value;\n            const limitSizeUnit = computeLimitSizeUnit.value;\n            if (XEUtils.isString(tipText)) {\n                return tipText;\n            }\n            const defHints = [];\n            if (isImage) {\n                if (multiple && limitCount) {\n                    defHints.push(getI18n('vxe.upload.imgCountHint', [limitCount]));\n                }\n                if (limitSize && limitSizeUnit) {\n                    defHints.push(getI18n('vxe.upload.imgSizeHint', [limitSizeUnit]));\n                }\n            }\n            else {\n                if (fileTypes && fileTypes.length) {\n                    defHints.push(getI18n('vxe.upload.fileTypeHint', [fileTypes.join('/')]));\n                }\n                if (limitSize && limitSizeUnit) {\n                    defHints.push(getI18n('vxe.upload.fileSizeHint', [limitSizeUnit]));\n                }\n                if (multiple && limitCount) {\n                    defHints.push(getI18n('vxe.upload.fileCountHint', [limitCount]));\n                }\n            }\n            return defHints.join(getI18n('vxe.base.comma'));\n        });\n        const computeImageOpts = computed(() => {\n            return Object.assign({}, props.imageConfig || props.imageStyle);\n        });\n        const computeImgStyle = computed(() => {\n            const imageOpts = computeImageOpts.value;\n            const { width, height } = imageOpts;\n            const stys = {};\n            if (width) {\n                stys.width = toCssUnit(width);\n            }\n            if (height) {\n                stys.height = toCssUnit(height);\n            }\n            return stys;\n        });\n        const computeMoreOpts = computed(() => {\n            return Object.assign({ showMoreButton: true }, props.moreConfig);\n        });\n        const computeMaps = {};\n        const $xeUpload = {\n            xID,\n            props,\n            context,\n            reactData,\n            internalData,\n            getRefMaps: () => refMaps,\n            getComputeMaps: () => computeMaps\n        };\n        const getUniqueKey = () => {\n            return XEUtils.uniqueId();\n        };\n        const getFieldKey = (item) => {\n            const keyField = computeKeyField.value;\n            return item[keyField];\n        };\n        const updateFileList = () => {\n            const { modelValue, multiple } = props;\n            const formReadonly = computeFormReadonly.value;\n            const keyField = computeKeyField.value;\n            const nameProp = computeNameProp.value;\n            const typeProp = computeTypeProp.value;\n            const urlProp = computeUrlProp.value;\n            const sizeProp = computeSizeProp.value;\n            const fileList = modelValue\n                ? (modelValue ? (XEUtils.isArray(modelValue) ? modelValue : [modelValue]) : []).map(item => {\n                    if (!item || XEUtils.isString(item)) {\n                        const url = `${item || ''}`;\n                        const urlObj = XEUtils.parseUrl(item);\n                        const name = (urlObj ? urlObj.searchQuery[nameProp] : '') || parseFileName(url);\n                        return {\n                            [nameProp]: name,\n                            [typeProp]: (urlObj ? urlObj.searchQuery[typeProp] : '') || parseFileType(name),\n                            [urlProp]: url,\n                            [sizeProp]: XEUtils.toNumber(urlObj ? urlObj.searchQuery[sizeProp] : 0) || 0,\n                            [keyField]: getUniqueKey()\n                        };\n                    }\n                    const name = item[nameProp] || '';\n                    item[nameProp] = name;\n                    item[typeProp] = item[typeProp] || parseFileType(name);\n                    item[urlProp] = item[urlProp] || '';\n                    item[sizeProp] = item[sizeProp] || 0;\n                    item[keyField] = item[keyField] || getUniqueKey();\n                    return item;\n                })\n                : [];\n            reactData.fileList = (formReadonly || multiple) ? fileList : (fileList.slice(0, 1));\n        };\n        const parseFileName = (url) => {\n            return decodeURIComponent(`${url || ''}`).split('/').pop() || '';\n        };\n        const parseFileType = (name) => {\n            // 这里不用split('.').pop()因为没有后缀时会返回自身\n            const index = name.lastIndexOf('.');\n            if (index > 0) {\n                return name.substring(index + 1).toLowerCase();\n            }\n            return '';\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $upload: $xeUpload }, params));\n        };\n        const handleChange = (value) => {\n            const { singleMode, urlMode } = props;\n            const urlProp = computeUrlProp.value;\n            const nameProp = computeNameProp.value;\n            let restList = value ? value.slice(0) : [];\n            if (urlMode) {\n                restList = restList.map(item => {\n                    const url = item[urlProp];\n                    if (url) {\n                        const urlObj = XEUtils.parseUrl(url);\n                        if (!urlObj.searchQuery[nameProp]) {\n                            return `${url}${url.indexOf('?') === -1 ? '?' : '&'}${nameProp}=${encodeURIComponent(item[nameProp] || '')}`;\n                        }\n                    }\n                    return url;\n                });\n            }\n            emit('update:modelValue', singleMode ? (restList[0] || null) : restList);\n        };\n        const getThumbnailFileUrl = (item) => {\n            const getThumbnailUrlFn = props.getThumbnailUrlMethod || getConfig().upload.getThumbnailUrlMethod;\n            if (getThumbnailUrlFn) {\n                return getThumbnailUrlFn({\n                    $upload: $xeUpload,\n                    option: item\n                });\n            }\n            return getFileUrl(item);\n        };\n        const getFileUrl = (item) => {\n            const getUrlFn = props.getUrlMethod || getConfig().upload.getUrlMethod;\n            const urlProp = computeUrlProp.value;\n            return getUrlFn\n                ? getUrlFn({\n                    $upload: $xeUpload,\n                    option: item\n                })\n                : item[urlProp];\n        };\n        const handleDefaultFilePreview = (item) => {\n            const { imageTypes, showDownloadButton } = props;\n            const typeProp = computeTypeProp.value;\n            const beforeDownloadFn = props.beforeDownloadMethod || getConfig().upload.beforeDownloadMethod;\n            const { imagePreviewTypes } = internalData;\n            // 如果是预览图片\n            if (imagePreviewTypes.concat(imageTypes || []).some(type => `${type}`.toLowerCase() === `${item[typeProp]}`.toLowerCase())) {\n                if (VxeUI.previewImage) {\n                    VxeUI.previewImage({\n                        urlList: [getFileUrl(item)],\n                        showDownloadButton,\n                        beforeDownloadMethod: beforeDownloadFn\n                            ? () => {\n                                return beforeDownloadFn({\n                                    $upload: $xeUpload,\n                                    option: item\n                                });\n                            }\n                            : undefined\n                    });\n                }\n            }\n        };\n        const handlePreviewFileEvent = (evnt, item) => {\n            const previewFn = props.previewMethod || getConfig().upload.previewMethod;\n            if (props.showPreview) {\n                if (previewFn) {\n                    previewFn({\n                        $upload: $xeUpload,\n                        option: item\n                    });\n                }\n                else {\n                    handleDefaultFilePreview(item);\n                }\n            }\n        };\n        const handlePreviewImageEvent = (evnt, item, index) => {\n            const { showDownloadButton } = props;\n            const { fileList } = reactData;\n            const beforeDownloadFn = props.beforeDownloadMethod || getConfig().upload.beforeDownloadMethod;\n            if (props.showPreview) {\n                if (VxeUI.previewImage) {\n                    VxeUI.previewImage({\n                        urlList: fileList.map(item => getFileUrl(item)),\n                        activeIndex: index,\n                        showDownloadButton,\n                        beforeDownloadMethod: beforeDownloadFn\n                            ? ({ index }) => {\n                                return beforeDownloadFn({\n                                    $upload: $xeUpload,\n                                    option: fileList[index]\n                                });\n                            }\n                            : undefined\n                    });\n                }\n            }\n        };\n        const handleUploadResult = (item, file) => {\n            const { showErrorStatus } = props;\n            const fileKey = getFieldKey(item);\n            const uploadFn = props.uploadMethod || getConfig().upload.uploadMethod;\n            if (uploadFn) {\n                return Promise.resolve(uploadFn({\n                    $upload: $xeUpload,\n                    file,\n                    option: item,\n                    updateProgress(percentNum) {\n                        const { fileCacheMaps } = reactData;\n                        const cacheItem = fileCacheMaps[getFieldKey(item)];\n                        if (cacheItem) {\n                            cacheItem.percent = Math.max(0, Math.min(99, XEUtils.toNumber(percentNum)));\n                        }\n                    }\n                })).then(res => {\n                    const { fileCacheMaps } = reactData;\n                    const cacheItem = fileCacheMaps[fileKey];\n                    if (cacheItem) {\n                        cacheItem.percent = 100;\n                    }\n                    Object.assign(item, res);\n                    dispatchEvent('upload-success', { option: item, data: res }, null);\n                }).catch((res) => {\n                    const { fileCacheMaps } = reactData;\n                    const cacheItem = fileCacheMaps[fileKey];\n                    if (cacheItem) {\n                        cacheItem.status = 'error';\n                    }\n                    if (showErrorStatus) {\n                        Object.assign(item, res);\n                    }\n                    else {\n                        reactData.fileList = reactData.fileList.filter(obj => getFieldKey(obj) !== fileKey);\n                    }\n                    dispatchEvent('upload-error', { option: item, data: res }, null);\n                }).finally(() => {\n                    const { fileCacheMaps } = reactData;\n                    const cacheItem = fileCacheMaps[fileKey];\n                    if (cacheItem) {\n                        cacheItem.loading = false;\n                    }\n                });\n            }\n            else {\n                const { fileCacheMaps } = reactData;\n                const cacheItem = fileCacheMaps[fileKey];\n                if (cacheItem) {\n                    cacheItem.loading = false;\n                }\n            }\n            return Promise.resolve();\n        };\n        const handleReUpload = (item) => {\n            const { uploadMethod, urlMode } = props;\n            const { fileCacheMaps } = reactData;\n            const fileKey = getFieldKey(item);\n            const cacheItem = fileCacheMaps[fileKey];\n            const uploadFn = uploadMethod || getConfig().upload.uploadMethod;\n            if (uploadFn && cacheItem) {\n                const file = cacheItem.file;\n                cacheItem.loading = true;\n                cacheItem.status = '';\n                cacheItem.percent = 0;\n                handleUploadResult(item, file).then(() => {\n                    if (urlMode) {\n                        handleChange(reactData.fileList);\n                    }\n                });\n            }\n        };\n        const uploadFile = (files, evnt) => {\n            const { multiple, urlMode } = props;\n            const { fileList } = reactData;\n            const uploadFn = props.uploadMethod || getConfig().upload.uploadMethod;\n            const keyField = computeKeyField.value;\n            const nameProp = computeNameProp.value;\n            const typeProp = computeTypeProp.value;\n            const urlProp = computeUrlProp.value;\n            const sizeProp = computeSizeProp.value;\n            const limitMaxSizeB = computeLimitMaxSizeB.value;\n            const limitMaxCount = computeLimitMaxCount.value;\n            const limitSizeUnit = computeLimitSizeUnit.value;\n            let selectFiles = files;\n            if (multiple && limitMaxCount) {\n                // 校验文件数量\n                if (fileList.length >= limitMaxCount) {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.notification({\n                            title: getI18n('vxe.modal.errTitle'),\n                            status: 'error',\n                            content: getI18n('vxe.upload.overCountErr', [limitMaxCount])\n                        });\n                    }\n                    return;\n                }\n                const overNum = selectFiles.length - (limitMaxCount - fileList.length);\n                if (overNum > 0) {\n                    const overExtraList = selectFiles.slice(limitMaxCount - fileList.length);\n                    if (VxeUI.modal) {\n                        VxeUI.modal.notification({\n                            title: getI18n('vxe.modal.errTitle'),\n                            status: 'error',\n                            width: null,\n                            slots: {\n                                default() {\n                                    return h('div', {\n                                        class: 'vxe-upload--file-message-over-error'\n                                    }, [\n                                        h('div', {}, getI18n('vxe.upload.overCountExtraErr', [limitMaxCount, overNum])),\n                                        h('div', {\n                                            class: 'vxe-upload--file-message-over-extra'\n                                        }, overExtraList.map((file, index) => {\n                                            return h('div', {\n                                                key: index,\n                                                class: 'vxe-upload--file-message-over-extra-item'\n                                            }, file.name);\n                                        }))\n                                    ]);\n                                }\n                            }\n                        });\n                    }\n                }\n                selectFiles = selectFiles.slice(0, limitMaxCount - fileList.length);\n            }\n            // 校验文件大小\n            if (limitMaxSizeB) {\n                for (let i = 0; i < files.length; i++) {\n                    const file = files[0];\n                    if (file.size > limitMaxSizeB) {\n                        if (VxeUI.modal) {\n                            VxeUI.modal.notification({\n                                title: getI18n('vxe.modal.errTitle'),\n                                status: 'error',\n                                content: getI18n('vxe.upload.overSizeErr', [limitSizeUnit])\n                            });\n                        }\n                        return;\n                    }\n                }\n            }\n            const cacheMaps = Object.assign({}, reactData.fileCacheMaps);\n            const newFileList = multiple ? fileList : [];\n            const uploadPromiseRests = [];\n            selectFiles.forEach(file => {\n                const { name } = file;\n                const fileKey = getUniqueKey();\n                const fileObj = {\n                    [nameProp]: name,\n                    [typeProp]: parseFileType(name),\n                    [sizeProp]: file.size,\n                    [urlProp]: URL.createObjectURL(file),\n                    [keyField]: fileKey\n                };\n                if (uploadFn) {\n                    cacheMaps[fileKey] = {\n                        file: file,\n                        loading: true,\n                        status: '',\n                        percent: 0\n                    };\n                }\n                const item = reactive(fileObj);\n                if (uploadFn) {\n                    uploadPromiseRests.push(handleUploadResult(item, file));\n                }\n                newFileList.push(item);\n                dispatchEvent('add', { option: item }, evnt);\n            });\n            reactData.fileList = newFileList;\n            reactData.fileCacheMaps = cacheMaps;\n            Promise.all(urlMode ? uploadPromiseRests : []).then(() => {\n                handleChange(newFileList);\n                // 自动更新校验状态\n                if ($xeForm && formItemInfo) {\n                    $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, newFileList);\n                }\n            });\n        };\n        const handleChoose = (evnt) => {\n            const { multiple, imageTypes, fileTypes } = props;\n            const isDisabled = computeIsDisabled.value;\n            const isImage = computeIsImage.value;\n            if (isDisabled) {\n                return Promise.resolve({\n                    status: false,\n                    files: [],\n                    file: null\n                });\n            }\n            return readLocalFile({\n                multiple,\n                types: isImage ? imageTypes : fileTypes\n            }).then((params) => {\n                uploadFile(params.files, evnt);\n                return params;\n            });\n        };\n        const clickEvent = (evnt) => {\n            handleChoose(evnt).catch(() => {\n                // 错误文件类型\n            });\n        };\n        const handleRemoveEvent = (evnt, item, index) => {\n            const { fileList } = reactData;\n            fileList.splice(index, 1);\n            handleChange(fileList);\n            // 自动更新校验状态\n            if ($xeForm && formItemInfo) {\n                $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, fileList);\n            }\n            dispatchEvent('remove', { option: item }, evnt);\n        };\n        const removeFileEvent = (evnt, item, index) => {\n            const beforeRemoveFn = props.beforeRemoveMethod || getConfig().upload.beforeRemoveMethod;\n            const removeFn = props.removeMethod || getConfig().upload.removeMethod;\n            Promise.resolve(beforeRemoveFn\n                ? beforeRemoveFn({\n                    $upload: $xeUpload,\n                    option: item\n                })\n                : true).then(status => {\n                if (status) {\n                    if (removeFn) {\n                        Promise.resolve(removeFn({\n                            $upload: $xeUpload,\n                            option: item\n                        })).then(() => {\n                            handleRemoveEvent(evnt, item, index);\n                        }).catch(e => e);\n                    }\n                    else {\n                        handleRemoveEvent(evnt, item, index);\n                    }\n                }\n                else {\n                    dispatchEvent('remove-fail', { option: item }, evnt);\n                }\n            });\n        };\n        const handleDownloadEvent = (evnt, item) => {\n            dispatchEvent('download', { option: item }, evnt);\n        };\n        const downloadFileEvent = (evnt, item) => {\n            const beforeDownloadFn = props.beforeDownloadMethod || getConfig().upload.beforeDownloadMethod;\n            const downloadFn = props.downloadMethod || getConfig().upload.downloadMethod;\n            Promise.resolve(beforeDownloadFn\n                ? beforeDownloadFn({\n                    $upload: $xeUpload,\n                    option: item\n                })\n                : true).then(status => {\n                if (status) {\n                    if (downloadFn) {\n                        Promise.resolve(downloadFn({\n                            $upload: $xeUpload,\n                            option: item\n                        })).then(() => {\n                            handleDownloadEvent(evnt, item);\n                        }).catch(e => e);\n                    }\n                    else {\n                        handleDownloadEvent(evnt, item);\n                    }\n                }\n                else {\n                    dispatchEvent('download-fail', { option: item }, evnt);\n                }\n            });\n        };\n        const handleUploadDragleaveEvent = (evnt) => {\n            const targetElem = evnt.currentTarget;\n            const { clientX, clientY } = evnt;\n            if (targetElem) {\n                const { x: targetX, y: targetY, height: targetHeight, width: targetWidth } = targetElem.getBoundingClientRect();\n                if (clientX < targetX || clientX > targetX + targetWidth || clientY < targetY || clientY > targetY + targetHeight) {\n                    reactData.isDragUploadStatus = false;\n                }\n            }\n        };\n        const handleUploadDragoverEvent = (evnt) => {\n            const dataTransfer = evnt.dataTransfer;\n            if (dataTransfer) {\n                const { items } = dataTransfer;\n                if (items && items.length) {\n                    evnt.preventDefault();\n                    reactData.isDragUploadStatus = true;\n                }\n            }\n        };\n        const uploadTransferFileEvent = (evnt, files) => {\n            const { imageTypes } = props;\n            const { imagePreviewTypes } = internalData;\n            const isImage = computeIsImage.value;\n            if (isImage) {\n                const pasteImgTypes = imagePreviewTypes.concat(imageTypes && imageTypes.length ? imageTypes : []);\n                files = files.filter(file => {\n                    const fileType = `${file.type.split('/')[1] || ''}`.toLowerCase();\n                    if (pasteImgTypes.some(type => `${type}`.toLowerCase() === fileType)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            // 如果全部不满足条件\n            if (!files.length) {\n                if (VxeUI.modal) {\n                    VxeUI.modal.notification({\n                        title: getI18n('vxe.modal.errTitle'),\n                        status: 'error',\n                        content: getI18n('vxe.upload.uploadTypeErr')\n                    });\n                }\n                return;\n            }\n            uploadFile(files, evnt);\n        };\n        const handleUploadDropEvent = (evnt) => {\n            const dataTransfer = evnt.dataTransfer;\n            if (dataTransfer) {\n                const { items } = dataTransfer;\n                if (items && items.length) {\n                    evnt.preventDefault();\n                    const files = handleTransferFiles(items);\n                    if (files.length) {\n                        uploadTransferFileEvent(evnt, files);\n                    }\n                }\n            }\n            reactData.isDragUploadStatus = false;\n        };\n        const handleTransferFiles = (items) => {\n            const files = [];\n            XEUtils.arrayEach(items, item => {\n                const file = item.getAsFile();\n                if (file) {\n                    files.push(file);\n                }\n            });\n            return files;\n        };\n        const handleMoreEvent = () => {\n            const formReadonly = computeFormReadonly.value;\n            const isImage = computeIsImage.value;\n            if (VxeUI.modal) {\n                VxeUI.modal.open({\n                    title: formReadonly ? getI18n('vxe.upload.morePopup.readTitle') : getI18n(`vxe.upload.morePopup.${isImage ? 'imageTitle' : 'fileTitle'}`),\n                    width: 660,\n                    height: 500,\n                    escClosable: true,\n                    showMaximize: true,\n                    resize: true,\n                    maskClosable: true,\n                    slots: {\n                        default() {\n                            const { showErrorStatus, dragToUpload, dragSort } = props;\n                            const { isActivated, isDragMove, isDragUploadStatus, dragIndex } = reactData;\n                            const { fileList } = reactData;\n                            const isDisabled = computeIsDisabled.value;\n                            const ons = {};\n                            if (dragToUpload && dragIndex === -1) {\n                                ons.onDragover = handleUploadDragoverEvent;\n                                ons.onDragleave = handleUploadDragleaveEvent;\n                                ons.onDrop = handleUploadDropEvent;\n                            }\n                            return h('div', Object.assign({ ref: refPopupElem, class: ['vxe-upload--more-popup', {\n                                        'is--readonly': formReadonly,\n                                        'is--disabled': isDisabled,\n                                        'is--active': isActivated,\n                                        'show--error': showErrorStatus,\n                                        'is--drag': isDragUploadStatus\n                                    }] }, ons), [\n                                isImage\n                                    ? (dragSort\n                                        ? h(TransitionGroup, {\n                                            name: `vxe-upload--drag-list${isDragMove ? '' : '-disabled'}`,\n                                            tag: 'div',\n                                            class: 'vxe-upload--image-more-list'\n                                        }, {\n                                            default: () => renderImageItemList(fileList, true).concat(renderImageAction(true))\n                                        })\n                                        : h('div', {\n                                            class: 'vxe-upload--image-more-list'\n                                        }, renderImageItemList(fileList, true).concat(renderImageAction(true))))\n                                    : h('div', {\n                                        class: 'vxe-upload--file-more-list'\n                                    }, [\n                                        renderFileAction(true),\n                                        dragSort\n                                            ? h(TransitionGroup, {\n                                                name: `vxe-upload--drag-list${isDragMove ? '' : '-disabled'}`,\n                                                tag: 'div',\n                                                class: 'vxe-upload--file-list'\n                                            }, {\n                                                default: () => renderFileItemList(fileList, false)\n                                            })\n                                            : h('div', {\n                                                class: 'vxe-upload--file-list'\n                                            }, renderFileItemList(fileList, true))\n                                    ]),\n                                dragSort\n                                    ? h('div', {\n                                        ref: refModalDragLineElem,\n                                        class: 'vxe-upload--drag-line'\n                                    })\n                                    : renderEmptyElement($xeUpload),\n                                isDragUploadStatus\n                                    ? h('div', {\n                                        class: 'vxe-upload--drag-placeholder'\n                                    }, getI18n('vxe.upload.dragPlaceholder'))\n                                    : renderEmptyElement($xeUpload)\n                            ]);\n                        }\n                    },\n                    onShow() {\n                        reactData.showMorePopup = true;\n                    },\n                    onHide() {\n                        reactData.showMorePopup = false;\n                    }\n                });\n            }\n        };\n        const showDropTip = (evnt, dragEl, dragPos) => {\n            const { showMorePopup } = reactData;\n            const el = refElem.value;\n            const popupEl = refPopupElem.value;\n            const wrapperEl = showMorePopup ? popupEl : el;\n            if (!wrapperEl) {\n                return;\n            }\n            const wrapperRect = wrapperEl.getBoundingClientRect();\n            const ddLineEl = refDragLineElem.value;\n            const mdLineEl = refModalDragLineElem.value;\n            const currDLineEl = showMorePopup ? mdLineEl : ddLineEl;\n            if (currDLineEl) {\n                const dragRect = dragEl.getBoundingClientRect();\n                currDLineEl.style.display = 'block';\n                currDLineEl.style.top = `${Math.max(1, dragRect.y - wrapperRect.y)}px`;\n                currDLineEl.style.left = `${Math.max(1, dragRect.x - wrapperRect.x)}px`;\n                currDLineEl.style.height = `${dragRect.height}px`;\n                currDLineEl.style.width = `${dragRect.width - 1}px`;\n                currDLineEl.setAttribute('drag-pos', dragPos);\n            }\n        };\n        const hideDropTip = () => {\n            const ddLineEl = refDragLineElem.value;\n            const mdLineEl = refModalDragLineElem.value;\n            if (ddLineEl) {\n                ddLineEl.style.display = '';\n            }\n            if (mdLineEl) {\n                mdLineEl.style.display = '';\n            }\n        };\n        // 拖拽\n        const handleDragSortDragstartEvent = (evnt) => {\n            evnt.stopPropagation();\n            if (evnt.dataTransfer) {\n                evnt.dataTransfer.setDragImage(getTpImg(), 0, 0);\n            }\n            const dragEl = evnt.currentTarget;\n            const parentEl = dragEl.parentElement;\n            const dragIndex = XEUtils.findIndexOf(Array.from(parentEl.children), item => dragEl === item);\n            reactData.isDragMove = true;\n            reactData.dragIndex = dragIndex;\n            setTimeout(() => {\n                reactData.isDragMove = false;\n            }, 500);\n        };\n        const handleDragSortDragoverEvent = (evnt) => {\n            evnt.stopPropagation();\n            evnt.preventDefault();\n            const { dragIndex } = reactData;\n            if (dragIndex === -1) {\n                return;\n            }\n            const isImage = computeIsImage.value;\n            const dragEl = evnt.currentTarget;\n            const parentEl = dragEl.parentElement;\n            const currIndex = XEUtils.findIndexOf(Array.from(parentEl.children), item => dragEl === item);\n            let dragPos = '';\n            if (isImage) {\n                const offsetX = evnt.clientX - dragEl.getBoundingClientRect().x;\n                dragPos = offsetX < dragEl.clientWidth / 2 ? 'left' : 'right';\n            }\n            else {\n                const offsetY = evnt.clientY - dragEl.getBoundingClientRect().y;\n                dragPos = offsetY < dragEl.clientHeight / 2 ? 'top' : 'bottom';\n            }\n            if (dragIndex === currIndex) {\n                showDropTip(evnt, dragEl, dragPos);\n                return;\n            }\n            showDropTip(evnt, dragEl, dragPos);\n            internalData.prevDragIndex = currIndex;\n            internalData.prevDragPos = dragPos;\n        };\n        const handleDragSortDragendEvent = (evnt) => {\n            const { fileList, dragIndex } = reactData;\n            const { prevDragIndex, prevDragPos } = internalData;\n            const oldIndex = dragIndex;\n            const targetIndex = prevDragIndex;\n            const dragOffsetIndex = prevDragPos === 'bottom' || prevDragPos === 'right' ? 1 : 0;\n            const oldItem = fileList[oldIndex];\n            const newItem = fileList[targetIndex];\n            if (oldItem && newItem) {\n                fileList.splice(oldIndex, 1);\n                const ptfIndex = XEUtils.findIndexOf(fileList, item => newItem === item);\n                const nIndex = ptfIndex + dragOffsetIndex;\n                fileList.splice(nIndex, 0, oldItem);\n                dispatchEvent('sort-dragend', {\n                    oldItem: oldItem,\n                    newItem: newItem,\n                    dragPos: prevDragPos,\n                    offsetIndex: dragOffsetIndex,\n                    _index: {\n                        newIndex: nIndex,\n                        oldIndex: oldIndex\n                    }\n                }, evnt);\n            }\n            hideDropTip();\n            reactData.dragIndex = -1;\n        };\n        const handleItemMousedownEvent = (evnt) => {\n            if ($xeTable) {\n                evnt.stopPropagation();\n            }\n            reactData.isActivated = true;\n        };\n        const handleGlobalPasteEvent = (evnt) => {\n            const { pasteToUpload } = props;\n            const { isActivated } = reactData;\n            if (!isActivated || !pasteToUpload) {\n                return;\n            }\n            const clipboardData = evnt.clipboardData || evnt.originalEvent.clipboardData;\n            if (!clipboardData) {\n                return;\n            }\n            const { items } = clipboardData;\n            if (!items) {\n                return;\n            }\n            const files = handleTransferFiles(items);\n            if (files.length) {\n                evnt.preventDefault();\n                uploadTransferFileEvent(evnt, files);\n            }\n        };\n        const handleGlobalMousedownEvent = (evnt) => {\n            const el = refElem.value;\n            const popupEl = refPopupElem.value;\n            let isActivated = getEventTargetNode(evnt, el).flag;\n            if (!isActivated && popupEl) {\n                const parentEl = popupEl.parentElement || popupEl;\n                const modalEl = parentEl ? parentEl.parentElement : parentEl;\n                isActivated = getEventTargetNode(evnt, modalEl).flag;\n            }\n            reactData.isActivated = isActivated;\n        };\n        const handleGlobalBlurEvent = () => {\n            reactData.isActivated = false;\n        };\n        const uploadMethods = {\n            dispatchEvent,\n            choose() {\n                return handleChoose(null);\n            }\n        };\n        const uploadPrivateMethods = {};\n        Object.assign($xeUpload, uploadMethods, uploadPrivateMethods);\n        const renderFileItemList = (currList, isMoreView) => {\n            const { showRemoveButton, showDownloadButton, showProgress, progressText, showPreview, showErrorStatus, dragSort } = props;\n            const { fileCacheMaps } = reactData;\n            const isDisabled = computeIsDisabled.value;\n            const formReadonly = computeFormReadonly.value;\n            const nameProp = computeNameProp.value;\n            const typeProp = computeTypeProp.value;\n            const cornerSlot = slots.corner;\n            const ons = {};\n            if (dragSort && currList.length > 1) {\n                ons.onDragstart = handleDragSortDragstartEvent;\n                ons.onDragover = handleDragSortDragoverEvent;\n                ons.onDragend = handleDragSortDragendEvent;\n            }\n            return currList.map((item, index) => {\n                const fileKey = getFieldKey(item);\n                const cacheItem = fileCacheMaps[fileKey];\n                const isLoading = cacheItem && cacheItem.loading;\n                const isError = cacheItem && cacheItem.status === 'error';\n                return h('div', Object.assign({ key: dragSort ? fileKey : index, class: ['vxe-upload--file-item', {\n                            'is--preview': showPreview,\n                            'is--loading': isLoading,\n                            'is--error': isError\n                        }], fileid: fileKey, draggable: dragSort ? true : null }, ons), [\n                    h('div', {\n                        class: 'vxe-upload--file-item-icon'\n                    }, [\n                        h('i', {\n                            class: getIcon()[`UPLOAD_FILE_TYPE_${`${item[typeProp]}`.toLocaleUpperCase()}`] || getIcon().UPLOAD_FILE_TYPE_DEFAULT\n                        })\n                    ]),\n                    h('div', {\n                        class: 'vxe-upload--file-item-name',\n                        onClick(evnt) {\n                            if (!isLoading && !isError) {\n                                handlePreviewFileEvent(evnt, item);\n                            }\n                        }\n                    }, `${item[nameProp] || ''}`),\n                    isLoading\n                        ? h('div', {\n                            class: 'vxe-upload--file-item-loading-icon'\n                        }, [\n                            h('i', {\n                                class: getIcon().UPLOAD_LOADING\n                            })\n                        ])\n                        : createCommentVNode(),\n                    showProgress && isLoading && cacheItem\n                        ? h('div', {\n                            class: 'vxe-upload--file-item-loading-text'\n                        }, progressText ? XEUtils.toFormatString(progressText, { percent: cacheItem.percent }) : getI18n('vxe.upload.uploadProgress', [cacheItem.percent]))\n                        : createCommentVNode(),\n                    showErrorStatus && isError\n                        ? h('div', {\n                            class: 'vxe-upload--image-item-error'\n                        }, [\n                            h(VxeButtonComponent, {\n                                icon: getIcon().UPLOAD_IMAGE_RE_UPLOAD,\n                                mode: 'text',\n                                status: 'primary',\n                                content: getI18n('vxe.upload.reUpload'),\n                                onClick() {\n                                    handleReUpload(item);\n                                }\n                            })\n                        ])\n                        : createCommentVNode(),\n                    h('div', {\n                        class: 'vxe-upload--file-item-btn-wrapper'\n                    }, [\n                        cornerSlot\n                            ? h('div', {\n                                class: 'vxe-upload--file-item-corner'\n                            }, getSlotVNs(cornerSlot({ option: item, isMoreView, readonly: formReadonly })))\n                            : createCommentVNode(),\n                        showDownloadButton && !isLoading\n                            ? h('div', {\n                                class: 'vxe-upload--file-item-download-btn',\n                                onClick(evnt) {\n                                    downloadFileEvent(evnt, item);\n                                }\n                            }, [\n                                h('i', {\n                                    class: getIcon().UPLOAD_FILE_DOWNLOAD\n                                })\n                            ])\n                            : createCommentVNode(),\n                        showRemoveButton && !formReadonly && !isDisabled && !isLoading\n                            ? h('div', {\n                                class: 'vxe-upload--file-item-remove-btn',\n                                onClick(evnt) {\n                                    removeFileEvent(evnt, item, index);\n                                }\n                            }, [\n                                h('i', {\n                                    class: getIcon().UPLOAD_FILE_REMOVE\n                                })\n                            ])\n                            : createCommentVNode()\n                    ])\n                ]);\n            });\n        };\n        const renderFileAction = (isMoreView) => {\n            const { showUploadButton, buttonText, buttonIcon, showButtonText, showButtonIcon, autoHiddenButton } = props;\n            const isDisabled = computeIsDisabled.value;\n            const formReadonly = computeFormReadonly.value;\n            const defHintText = computedDefHintText.value;\n            const overCount = computeOverCount.value;\n            const defaultSlot = slots.default;\n            const tipSlot = slots.tip || slots.hint;\n            if (formReadonly || !showUploadButton) {\n                return createCommentVNode();\n            }\n            return h('div', {\n                class: 'vxe-upload--file-action'\n            }, [\n                autoHiddenButton && overCount\n                    ? createCommentVNode()\n                    : h('div', {\n                        class: 'vxe-upload--file-action-btn',\n                        onClick: clickEvent\n                    }, defaultSlot\n                        ? getSlotVNs(defaultSlot({ $upload: $xeUpload }))\n                        : [\n                            h(VxeButtonComponent, {\n                                class: 'vxe-upload--file-action-button',\n                                content: (isMoreView || showButtonText) ? (buttonText ? `${buttonText}` : getI18n('vxe.upload.fileBtnText')) : '',\n                                icon: showButtonIcon ? (buttonIcon || getIcon().UPLOAD_FILE_ADD) : '',\n                                disabled: isDisabled\n                            })\n                        ]),\n                isMoreView && (defHintText || tipSlot)\n                    ? h('div', {\n                        class: 'vxe-upload--file-action-tip'\n                    }, tipSlot ? getSlotVNs(tipSlot({ $upload: $xeUpload })) : defHintText)\n                    : createCommentVNode()\n            ]);\n        };\n        const renderAllMode = () => {\n            const { showList, moreConfig, dragSort } = props;\n            const { fileList, isDragMove } = reactData;\n            const moreOpts = computeMoreOpts.value;\n            const { maxCount, showMoreButton, layout } = moreOpts;\n            const isHorizontal = layout === 'horizontal';\n            let currList = fileList;\n            let overMaxNum = 0;\n            if (maxCount && fileList.length > maxCount) {\n                overMaxNum = fileList.length - maxCount;\n                currList = fileList.slice(0, maxCount);\n            }\n            return h('div', {\n                key: 'all',\n                class: 'vxe-upload--file-wrapper'\n            }, showList\n                ? [\n                    showMoreButton && moreConfig && isHorizontal\n                        ? createCommentVNode()\n                        : renderFileAction(true),\n                    (currList.length || (showMoreButton && isHorizontal))\n                        ? h('div', {\n                            class: ['vxe-upload--file-list-wrapper', {\n                                    'is--horizontal': isHorizontal\n                                }]\n                        }, [\n                            currList.length\n                                ? (dragSort\n                                    ? h(TransitionGroup, {\n                                        name: `vxe-upload--drag-list${isDragMove ? '' : '-disabled'}`,\n                                        tag: 'div',\n                                        class: 'vxe-upload--file-list'\n                                    }, {\n                                        default: () => renderFileItemList(currList, false)\n                                    })\n                                    : h('div', {\n                                        class: 'vxe-upload--file-list'\n                                    }, renderFileItemList(currList, false)))\n                                : createCommentVNode(),\n                            showMoreButton && overMaxNum\n                                ? h('div', {\n                                    class: 'vxe-upload--file-over-more'\n                                }, [\n                                    h(VxeButtonComponent, {\n                                        mode: 'text',\n                                        content: getI18n('vxe.upload.moreBtnText', [fileList.length]),\n                                        status: 'primary',\n                                        onClick: handleMoreEvent\n                                    })\n                                ])\n                                : createCommentVNode(),\n                            showMoreButton && moreConfig && isHorizontal\n                                ? renderFileAction(false)\n                                : createCommentVNode()\n                        ])\n                        : createCommentVNode()\n                ]\n                : [\n                    renderFileAction(false)\n                ]);\n        };\n        const renderImageItemList = (currList, isMoreView) => {\n            const { showRemoveButton, showProgress, progressText, showPreview, showErrorStatus, dragSort } = props;\n            const { fileCacheMaps } = reactData;\n            const isDisabled = computeIsDisabled.value;\n            const formReadonly = computeFormReadonly.value;\n            const imageOpts = computeImageOpts.value;\n            const imgStyle = computeImgStyle.value;\n            const cornerSlot = slots.corner;\n            const ons = {\n                onMousedown: handleItemMousedownEvent\n            };\n            if (dragSort && currList.length > 1) {\n                ons.onDragstart = handleDragSortDragstartEvent;\n                ons.onDragover = handleDragSortDragoverEvent;\n                ons.onDragend = handleDragSortDragendEvent;\n            }\n            return currList.map((item, index) => {\n                const fileKey = getFieldKey(item);\n                const cacheItem = fileCacheMaps[fileKey];\n                const isLoading = cacheItem && cacheItem.loading;\n                const isError = cacheItem && cacheItem.status === 'error';\n                return h('div', Object.assign({ key: dragSort ? fileKey : index, class: ['vxe-upload--image-item', {\n                            'is--preview': showPreview,\n                            'is--circle': imageOpts.circle,\n                            'is--loading': isLoading,\n                            'is--error': isError\n                        }], fileid: fileKey, draggable: dragSort ? true : null }, ons), [\n                    h('div', {\n                        class: 'vxe-upload--image-item-box',\n                        style: isMoreView ? null : imgStyle,\n                        title: getI18n('vxe.upload.viewItemTitle'),\n                        onClick(evnt) {\n                            if (!isLoading && !isError) {\n                                handlePreviewImageEvent(evnt, item, index);\n                            }\n                        }\n                    }, [\n                        isLoading && cacheItem\n                            ? h('div', {\n                                class: 'vxe-upload--image-item-loading'\n                            }, [\n                                h('div', {\n                                    class: 'vxe-upload--image-item-loading-icon'\n                                }, [\n                                    h('i', {\n                                        class: getIcon().UPLOAD_LOADING\n                                    })\n                                ]),\n                                showProgress\n                                    ? h('div', {\n                                        class: 'vxe-upload--image-item-loading-text'\n                                    }, progressText ? XEUtils.toFormatString(progressText, { percent: cacheItem.percent }) : getI18n('vxe.upload.uploadProgress', [cacheItem.percent]))\n                                    : createCommentVNode()\n                            ])\n                            : createCommentVNode(),\n                        !isLoading\n                            ? (isError && showErrorStatus\n                                ? h('div', {\n                                    class: 'vxe-upload--image-item-error'\n                                }, [\n                                    h(VxeButtonComponent, {\n                                        icon: getIcon().UPLOAD_IMAGE_RE_UPLOAD,\n                                        mode: 'text',\n                                        status: 'primary',\n                                        content: getI18n('vxe.upload.reUpload'),\n                                        onClick() {\n                                            handleReUpload(item);\n                                        }\n                                    })\n                                ])\n                                : h('div', {\n                                    class: 'vxe-upload--image-item-img-wrapper'\n                                }, [\n                                    h('img', {\n                                        class: 'vxe-upload--image-item-img',\n                                        src: getThumbnailFileUrl(item)\n                                    })\n                                ]))\n                            : createCommentVNode(),\n                        h('div', {\n                            class: 'vxe-upload--image-item-btn-wrapper',\n                            onClick(evnt) {\n                                evnt.stopPropagation();\n                            }\n                        }, [\n                            cornerSlot\n                                ? h('div', {\n                                    class: 'vxe-upload--file-item-corner'\n                                }, getSlotVNs(cornerSlot({ option: item, isMoreView, readonly: formReadonly })))\n                                : createCommentVNode(),\n                            showRemoveButton && !formReadonly && !isDisabled && !isLoading\n                                ? h('div', {\n                                    class: 'vxe-upload--image-item-remove-btn',\n                                    onClick(evnt) {\n                                        evnt.stopPropagation();\n                                        removeFileEvent(evnt, item, index);\n                                    }\n                                }, [\n                                    h('i', {\n                                        class: getIcon().UPLOAD_IMAGE_REMOVE\n                                    })\n                                ])\n                                : createCommentVNode()\n                        ])\n                    ])\n                ]);\n            });\n        };\n        const renderImageAction = (isMoreView) => {\n            const { showUploadButton, buttonText, buttonIcon, showButtonText, showButtonIcon, autoHiddenButton } = props;\n            const formReadonly = computeFormReadonly.value;\n            const defHintText = computedDefHintText.value;\n            const overCount = computeOverCount.value;\n            const imgStyle = computeImgStyle.value;\n            const defaultSlot = slots.default;\n            const hintSlot = slots.hint;\n            if (formReadonly || !showUploadButton || (autoHiddenButton && overCount)) {\n                return createCommentVNode();\n            }\n            return h('div', {\n                key: 'action',\n                class: 'vxe-upload--image-action'\n            }, [\n                h('div', {\n                    class: 'vxe-upload--image-action-btn',\n                    onClick: clickEvent\n                }, defaultSlot\n                    ? defaultSlot({ $upload: $xeUpload })\n                    : [\n                        h('div', {\n                            class: 'vxe-upload--image-action-box',\n                            style: isMoreView ? null : imgStyle\n                        }, [\n                            showButtonIcon\n                                ? h('div', {\n                                    class: 'vxe-upload--image-action-icon'\n                                }, [\n                                    h('i', {\n                                        class: buttonIcon || getIcon().UPLOAD_IMAGE_ADD\n                                    })\n                                ])\n                                : createCommentVNode(),\n                            isMoreView || showButtonText\n                                ? h('div', {\n                                    class: 'vxe-upload--image-action-content'\n                                }, buttonText ? `${buttonText}` : getI18n('vxe.upload.imgBtnText'))\n                                : createCommentVNode(),\n                            isMoreView && (defHintText || hintSlot)\n                                ? h('div', {\n                                    class: 'vxe-upload--image-action-hint'\n                                }, hintSlot ? getSlotVNs(hintSlot({ $upload: $xeUpload })) : defHintText)\n                                : createCommentVNode()\n                        ])\n                    ])\n            ]);\n        };\n        const renderImageMode = () => {\n            const { showList, dragSort } = props;\n            const { fileList, isDragMove } = reactData;\n            const moreOpts = computeMoreOpts.value;\n            const { maxCount, showMoreButton } = moreOpts;\n            let currList = fileList;\n            let overMaxNum = 0;\n            if (maxCount && fileList.length > maxCount) {\n                overMaxNum = fileList.length - maxCount;\n                currList = fileList.slice(0, maxCount);\n            }\n            return h('div', {\n                key: 'image',\n                class: 'vxe-upload--image-wrapper'\n            }, showList\n                ? [\n                    dragSort\n                        ? h(TransitionGroup, {\n                            name: `vxe-upload--drag-list${isDragMove ? '' : '-disabled'}`,\n                            tag: 'div',\n                            class: 'vxe-upload--image-list'\n                        }, {\n                            default: () => renderImageItemList(currList, false).concat([\n                                showMoreButton && overMaxNum\n                                    ? h('div', {\n                                        key: 'om',\n                                        class: 'vxe-upload--image-over-more'\n                                    }, [\n                                        h(VxeButtonComponent, {\n                                            mode: 'text',\n                                            content: getI18n('vxe.upload.moreBtnText', [fileList.length]),\n                                            status: 'primary',\n                                            onClick: handleMoreEvent\n                                        })\n                                    ])\n                                    : createCommentVNode(),\n                                renderImageAction(false)\n                            ])\n                        })\n                        : h('div', {\n                            class: 'vxe-upload--image-list'\n                        }, renderImageItemList(currList, false).concat([\n                            showMoreButton && overMaxNum\n                                ? h('div', {\n                                    class: 'vxe-upload--image-over-more'\n                                }, [\n                                    h(VxeButtonComponent, {\n                                        mode: 'text',\n                                        content: getI18n('vxe.upload.moreBtnText', [fileList.length]),\n                                        status: 'primary',\n                                        onClick: handleMoreEvent\n                                    })\n                                ])\n                                : createCommentVNode(),\n                            renderImageAction(false)\n                        ]))\n                ]\n                : [\n                    h('div', {\n                        class: 'vxe-upload--image-list'\n                    }, [\n                        renderImageAction(false)\n                    ])\n                ]);\n        };\n        const renderVN = () => {\n            const { showErrorStatus, dragToUpload, pasteToUpload, dragSort } = props;\n            const { isDragUploadStatus, showMorePopup, isActivated, dragIndex } = reactData;\n            const vSize = computeSize.value;\n            const isDisabled = computeIsDisabled.value;\n            const formReadonly = computeFormReadonly.value;\n            const isImage = computeIsImage.value;\n            const ons = {\n                onMousedown: handleItemMousedownEvent\n            };\n            if (dragToUpload && dragIndex === -1) {\n                ons.onDragover = handleUploadDragoverEvent;\n                ons.onDragleave = handleUploadDragleaveEvent;\n                ons.onDrop = handleUploadDropEvent;\n            }\n            return h('div', Object.assign({ ref: refElem, class: ['vxe-upload', {\n                        [`size--${vSize}`]: vSize,\n                        'is--active': isActivated,\n                        'is--readonly': formReadonly,\n                        'is--disabled': isDisabled,\n                        'is--paste': pasteToUpload,\n                        'show--error': showErrorStatus,\n                        'is--drag': isDragUploadStatus\n                    }] }, ons), [\n                isImage ? renderImageMode() : renderAllMode(),\n                dragSort\n                    ? h('div', {\n                        ref: refDragLineElem,\n                        class: 'vxe-upload--drag-line'\n                    })\n                    : renderEmptyElement($xeUpload),\n                isDragUploadStatus && !showMorePopup\n                    ? h('div', {\n                        class: 'vxe-upload--drag-placeholder'\n                    }, getI18n('vxe.upload.dragPlaceholder'))\n                    : renderEmptyElement($xeUpload)\n            ]);\n        };\n        const listFlag = ref(0);\n        watch(() => props.modelValue ? props.modelValue.length : 0, () => {\n            listFlag.value++;\n        });\n        watch(() => props.modelValue, () => {\n            listFlag.value++;\n        });\n        watch(listFlag, () => {\n            updateFileList();\n        });\n        onMounted(() => {\n            if (process.env.NODE_ENV === 'development') {\n                if (props.multiple && props.singleMode) {\n                    errLog('vxe.error.errConflicts', ['multiple', 'single-mode']);\n                }\n                if (props.imageStyle) {\n                    warnLog('vxe.error.delProp', ['image-style', 'image-config']);\n                }\n            }\n            if (props.dragSort) {\n                initTpImg();\n            }\n            globalEvents.on($xeUpload, 'paste', handleGlobalPasteEvent);\n            globalEvents.on($xeUpload, 'mousedown', handleGlobalMousedownEvent);\n            globalEvents.on($xeUpload, 'blur', handleGlobalBlurEvent);\n        });\n        onUnmounted(() => {\n            reactData.isDragUploadStatus = false;\n            globalEvents.off($xeUpload, 'paste');\n            globalEvents.off($xeUpload, 'mousedown');\n            globalEvents.off($xeUpload, 'blur');\n        });\n        updateFileList();\n        $xeUpload.renderVN = renderVN;\n        return $xeUpload;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n", "import XEUtils from 'xe-utils';\nimport { VxeUI, getI18n } from '@vxe-ui/core';\n// 导入\nlet fileForm = null;\nlet fileInput = null;\nexport function parseFile(file) {\n    const name = file.name;\n    const tIndex = XEUtils.lastIndexOf(name, '.');\n    const type = name.substring(tIndex + 1, name.length).toLowerCase();\n    const filename = name.substring(0, tIndex);\n    return { filename, type };\n}\n/**\n * 读取本地文件\n */\nexport const readLocalFile = (options) => {\n    const opts = Object.assign({}, options);\n    return new Promise((resolve, reject) => {\n        if (!fileInput) {\n            fileInput = document.createElement('input');\n            fileInput.name = 'file';\n            fileInput.type = 'file';\n        }\n        if (!fileForm) {\n            fileForm = document.createElement('form');\n            fileForm.style.display = 'none';\n            fileForm.appendChild(fileInput);\n            document.body.appendChild(fileForm);\n        }\n        const types = opts.types || [];\n        const isAllType = !types.length || types.some((type) => type === '*');\n        fileInput.multiple = !!opts.multiple;\n        fileInput.accept = isAllType ? '' : `.${types.join(', .')}`;\n        fileInput.onchange = (evnt) => {\n            const eventTarget = evnt.target;\n            const files = Array.from(eventTarget.files || []);\n            const file = files[0];\n            let errType = '';\n            // 校验类型\n            if (!isAllType) {\n                for (let fIndex = 0; fIndex < files.length; fIndex++) {\n                    const { type } = parseFile(files[fIndex]);\n                    if (!XEUtils.includes(types, type)) {\n                        errType = type;\n                        break;\n                    }\n                }\n            }\n            if (!errType) {\n                resolve({ status: true, files, file });\n            }\n            else {\n                if (opts.message !== false) {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.message({\n                            content: getI18n('vxe.error.notType', [errType]),\n                            status: 'error'\n                        });\n                    }\n                }\n                const params = { status: false, files, file };\n                reject(params);\n            }\n        };\n        fileForm.reset();\n        fileInput.click();\n    });\n};\nexport function getExportBlobByContent(content, options) {\n    return new Blob([content], { type: `text/${options.type};charset=utf-8;` });\n}\n/**\n * 保存文件到本地\n */\nexport const saveLocalFile = (options) => {\n    const opts = Object.assign({ type: '' }, options);\n    const { filename, type, content } = opts;\n    const name = type ? `${filename}.${type}` : `${filename}`;\n    if (window.Blob) {\n        const blob = content instanceof Blob ? content : getExportBlobByContent(XEUtils.toValueString(content), opts);\n        const winNavigator = window.navigator;\n        if (winNavigator.msSaveBlob) {\n            winNavigator.msSaveBlob(blob, name);\n        }\n        else {\n            const url = URL.createObjectURL(blob);\n            const linkElem = document.createElement('a');\n            linkElem.target = '_blank';\n            linkElem.download = name;\n            linkElem.href = url;\n            document.body.appendChild(linkElem);\n            linkElem.click();\n            requestAnimationFrame(() => {\n                if (linkElem.parentNode) {\n                    linkElem.parentNode.removeChild(linkElem);\n                }\n                URL.revokeObjectURL(url);\n            });\n        }\n        return Promise.resolve();\n    }\n    return Promise.reject(new Error(getI18n('vxe.error.notExp')));\n};\n", "import VxeUploadComponent from './src/upload';\nimport { VxeUI } from '@vxe-ui/core';\nimport { dynamicApp } from '../dynamics';\nimport { saveLocalFile, readLocalFile } from './src/util';\nexport const VxeUpload = Object.assign({}, VxeUploadComponent, {\n    install(app) {\n        app.component(VxeUploadComponent.name, VxeUploadComponent);\n    }\n});\ndynamicApp.use(VxeUpload);\nVxeUI.component(VxeUploadComponent);\nVxeUI.saveFile = saveLocalFile;\nVxeUI.readFile = readLocalFile;\nexport const Upload = VxeUpload;\nexport default VxeUpload;\n", "import VxeUpload from '../upload';\nexport * from '../upload';\nexport default VxeUpload;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,mBAAoB;;;ACDpB,sBAAoB;AAGpB,IAAI,WAAW;AACf,IAAI,YAAY;AACT,SAAS,UAAU,MAAM;AAC5B,QAAM,OAAO,KAAK;AAClB,QAAM,SAAS,gBAAAC,QAAQ,YAAY,MAAM,GAAG;AAC5C,QAAM,OAAO,KAAK,UAAU,SAAS,GAAG,KAAK,MAAM,EAAE,YAAY;AACjE,QAAM,WAAW,KAAK,UAAU,GAAG,MAAM;AACzC,SAAO,EAAE,UAAU,KAAK;AAC5B;AAIO,IAAM,gBAAgB,CAAC,YAAY;AACtC,QAAM,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AACtC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,QAAI,CAAC,WAAW;AACZ,kBAAY,SAAS,cAAc,OAAO;AAC1C,gBAAU,OAAO;AACjB,gBAAU,OAAO;AAAA,IACrB;AACA,QAAI,CAAC,UAAU;AACX,iBAAW,SAAS,cAAc,MAAM;AACxC,eAAS,MAAM,UAAU;AACzB,eAAS,YAAY,SAAS;AAC9B,eAAS,KAAK,YAAY,QAAQ;AAAA,IACtC;AACA,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,UAAM,YAAY,CAAC,MAAM,UAAU,MAAM,KAAK,CAAC,SAAS,SAAS,GAAG;AACpE,cAAU,WAAW,CAAC,CAAC,KAAK;AAC5B,cAAU,SAAS,YAAY,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AACzD,cAAU,WAAW,CAAC,SAAS;AAC3B,YAAM,cAAc,KAAK;AACzB,YAAM,QAAQ,MAAM,KAAK,YAAY,SAAS,CAAC,CAAC;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,UAAI,UAAU;AAEd,UAAI,CAAC,WAAW;AACZ,iBAAS,SAAS,GAAG,SAAS,MAAM,QAAQ,UAAU;AAClD,gBAAM,EAAE,KAAK,IAAI,UAAU,MAAM,MAAM,CAAC;AACxC,cAAI,CAAC,gBAAAA,QAAQ,SAAS,OAAO,IAAI,GAAG;AAChC,sBAAU;AACV;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC,SAAS;AACV,gBAAQ,EAAE,QAAQ,MAAM,OAAO,KAAK,CAAC;AAAA,MACzC,OACK;AACD,YAAI,KAAK,YAAY,OAAO;AACxB,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,QAAQ;AAAA,cAChB,SAAS,QAAQ,qBAAqB,CAAC,OAAO,CAAC;AAAA,cAC/C,QAAQ;AAAA,YACZ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,cAAM,SAAS,EAAE,QAAQ,OAAO,OAAO,KAAK;AAC5C,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AACA,aAAS,MAAM;AACf,cAAU,MAAM;AAAA,EACpB,CAAC;AACL;AACO,SAAS,uBAAuB,SAAS,SAAS;AACrD,SAAO,IAAI,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,QAAQ,QAAQ,IAAI,kBAAkB,CAAC;AAC9E;AAIO,IAAM,gBAAgB,CAAC,YAAY;AACtC,QAAM,OAAO,OAAO,OAAO,EAAE,MAAM,GAAG,GAAG,OAAO;AAChD,QAAM,EAAE,UAAU,MAAM,QAAQ,IAAI;AACpC,QAAM,OAAO,OAAO,GAAG,QAAQ,IAAI,IAAI,KAAK,GAAG,QAAQ;AACvD,MAAI,OAAO,MAAM;AACb,UAAM,OAAO,mBAAmB,OAAO,UAAU,uBAAuB,gBAAAA,QAAQ,cAAc,OAAO,GAAG,IAAI;AAC5G,UAAM,eAAe,OAAO;AAC5B,QAAI,aAAa,YAAY;AACzB,mBAAa,WAAW,MAAM,IAAI;AAAA,IACtC,OACK;AACD,YAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,YAAM,WAAW,SAAS,cAAc,GAAG;AAC3C,eAAS,SAAS;AAClB,eAAS,WAAW;AACpB,eAAS,OAAO;AAChB,eAAS,KAAK,YAAY,QAAQ;AAClC,eAAS,MAAM;AACf,4BAAsB,MAAM;AACxB,YAAI,SAAS,YAAY;AACrB,mBAAS,WAAW,YAAY,QAAQ;AAAA,QAC5C;AACA,YAAI,gBAAgB,GAAG;AAAA,MAC3B,CAAC;AAAA,IACL;AACA,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AACA,SAAO,QAAQ,OAAO,IAAI,MAAM,QAAQ,kBAAkB,CAAC,CAAC;AAChE;;;AD9FA,IAAO,iBAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,YAAY,CAAC,OAAO,QAAQ,MAAM;AAAA,IAClC,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,iBAAAC,QAAQ,MAAM,UAAU,EAAE,OAAO,YAAY,IAAI;AAAA,IACpE;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAM,iBAAAA,QAAQ,MAAM,UAAU,EAAE,OAAO,aAAa,IAAI;AAAA,IACrE;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,iBAAAA,QAAQ,MAAM,UAAU,EAAE,OAAO,YAAY,IAAI;AAAA,IACpE;AAAA,IACA,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,iBAAAA,QAAQ,MAAM,UAAU,EAAE,OAAO,WAAW,IAAI;AAAA,IACnE;AAAA,IACA,UAAU;AAAA,IACV,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAM,iBAAAA,QAAQ,MAAM,UAAU,EAAE,OAAO,cAAc,IAAI;AAAA,IACtE;AAAA,IACA,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM,iBAAAA,QAAQ,MAAM,UAAU,EAAE,OAAO,eAAe,IAAI;AAAA,IACvE;AAAA,IACA,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,oBAAoB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO;AAAA,IACtC;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,OAAO,QAAQ,UAAU,EAAE;AAAA,IAC1D;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,MAAM,MAAM,IAAI;AACxB,UAAM,UAAU,OAAO,WAAW,IAAI;AACtC,UAAM,eAAe,OAAO,kBAAkB,IAAI;AAClD,UAAM,WAAW,OAAO,YAAY,IAAI;AACxC,UAAM,MAAM,iBAAAA,QAAQ,SAAS;AAC7B,UAAM,EAAE,YAAY,IAAI,QAAQ,KAAK;AACrC,UAAM,UAAU,IAAI;AACpB,UAAM,eAAe,IAAI;AACzB,UAAM,kBAAkB,IAAI;AAC5B,UAAM,uBAAuB,IAAI;AACjC,UAAM,YAAY,SAAS;AAAA,MACvB,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,UAAU,CAAC;AAAA,MACX,eAAe,CAAC;AAAA,MAChB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,IACjB,CAAC;AACD,UAAM,eAAe;AAAA,MACjB,mBAAmB,CAAC,OAAO,QAAQ,OAAO,KAAK;AAAA,MAC/C,eAAe;AAAA;AAAA,IAEnB;AACA,UAAM,UAAU;AAAA,MACZ;AAAA,IACJ;AACA,UAAM,sBAAsB,SAAS,MAAM;AACvC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,aAAa,MAAM;AACnB,YAAI,SAAS;AACT,iBAAO,QAAQ,MAAM;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,aAAa,MAAM;AACnB,YAAI,SAAS;AACT,iBAAO,QAAQ,MAAM;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,MAAM,YAAY;AAAA,IAC7B,CAAC;AACD,UAAM,iBAAiB,SAAS,MAAM;AAClC,aAAO,MAAM,SAAS;AAAA,IAC1B,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,MAAM,aAAa;AAAA,IAC9B,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,MAAM,aAAa;AAAA,IAC9B,CAAC;AACD,UAAM,iBAAiB,SAAS,MAAM;AAClC,aAAO,MAAM,YAAY;AAAA,IAC7B,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,MAAM,aAAa;AAAA,IAC9B,CAAC;AACD,UAAM,uBAAuB,SAAS,MAAM;AACxC,aAAO,iBAAAA,QAAQ,SAAS,MAAM,SAAS,IAAI,OAAO;AAAA,IACtD,CAAC;AACD,UAAM,uBAAuB,SAAS,MAAM;AACxC,aAAO,MAAM,WAAW,iBAAAA,QAAQ,SAAS,MAAM,UAAU,IAAI;AAAA,IACjE,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,gBAAgB,qBAAqB;AAC3C,UAAI,UAAU;AACV,YAAI,eAAe;AACf,iBAAO,SAAS,UAAU;AAAA,QAC9B;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,UAAU;AAAA,IAC9B,CAAC;AACD,UAAM,uBAAuB,SAAS,MAAM;AACxC,YAAM,YAAY,iBAAAA,QAAQ,SAAS,MAAM,SAAS;AAClD,UAAI,WAAW;AACX,YAAI,YAAY,SAAS;AACrB,iBAAO,GAAG,YAAY,OAAO;AAAA,QACjC;AACA,YAAI,YAAY,MAAM;AAClB,iBAAO,GAAG,YAAY,IAAI;AAAA,QAC9B;AACA,eAAO,GAAG,SAAS;AAAA,MACvB;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,sBAAsB,SAAS,MAAM;AACvC,YAAM,EAAE,WAAW,WAAW,UAAU,WAAW,IAAI;AACvD,YAAM,UAAU,MAAM,WAAW,MAAM;AACvC,YAAM,UAAU,eAAe;AAC/B,YAAM,gBAAgB,qBAAqB;AAC3C,UAAI,iBAAAA,QAAQ,SAAS,OAAO,GAAG;AAC3B,eAAO;AAAA,MACX;AACA,YAAM,WAAW,CAAC;AAClB,UAAI,SAAS;AACT,YAAI,YAAY,YAAY;AACxB,mBAAS,KAAK,QAAQ,2BAA2B,CAAC,UAAU,CAAC,CAAC;AAAA,QAClE;AACA,YAAI,aAAa,eAAe;AAC5B,mBAAS,KAAK,QAAQ,0BAA0B,CAAC,aAAa,CAAC,CAAC;AAAA,QACpE;AAAA,MACJ,OACK;AACD,YAAI,aAAa,UAAU,QAAQ;AAC/B,mBAAS,KAAK,QAAQ,2BAA2B,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,QAC3E;AACA,YAAI,aAAa,eAAe;AAC5B,mBAAS,KAAK,QAAQ,2BAA2B,CAAC,aAAa,CAAC,CAAC;AAAA,QACrE;AACA,YAAI,YAAY,YAAY;AACxB,mBAAS,KAAK,QAAQ,4BAA4B,CAAC,UAAU,CAAC,CAAC;AAAA,QACnE;AAAA,MACJ;AACA,aAAO,SAAS,KAAK,QAAQ,gBAAgB,CAAC;AAAA,IAClD,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,OAAO,OAAO,CAAC,GAAG,MAAM,eAAe,MAAM,UAAU;AAAA,IAClE,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,YAAM,YAAY,iBAAiB;AACnC,YAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,YAAM,OAAO,CAAC;AACd,UAAI,OAAO;AACP,aAAK,QAAQ,UAAU,KAAK;AAAA,MAChC;AACA,UAAI,QAAQ;AACR,aAAK,SAAS,UAAU,MAAM;AAAA,MAClC;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,OAAO,OAAO,EAAE,gBAAgB,KAAK,GAAG,MAAM,UAAU;AAAA,IACnE,CAAC;AACD,UAAM,cAAc,CAAC;AACrB,UAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,MAClB,gBAAgB,MAAM;AAAA,IAC1B;AACA,UAAM,eAAe,MAAM;AACvB,aAAO,iBAAAA,QAAQ,SAAS;AAAA,IAC5B;AACA,UAAM,cAAc,CAAC,SAAS;AAC1B,YAAM,WAAW,gBAAgB;AACjC,aAAO,KAAK,QAAQ;AAAA,IACxB;AACA,UAAM,iBAAiB,MAAM;AACzB,YAAM,EAAE,YAAY,SAAS,IAAI;AACjC,YAAM,eAAe,oBAAoB;AACzC,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,UAAU,eAAe;AAC/B,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,cACV,aAAc,iBAAAA,QAAQ,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU,IAAK,CAAC,GAAG,IAAI,UAAQ;AACxF,YAAI,CAAC,QAAQ,iBAAAA,QAAQ,SAAS,IAAI,GAAG;AACjC,gBAAM,MAAM,GAAG,QAAQ,EAAE;AACzB,gBAAM,SAAS,iBAAAA,QAAQ,SAAS,IAAI;AACpC,gBAAMC,SAAQ,SAAS,OAAO,YAAY,QAAQ,IAAI,OAAO,cAAc,GAAG;AAC9E,iBAAO;AAAA,YACH,CAAC,QAAQ,GAAGA;AAAA,YACZ,CAAC,QAAQ,IAAI,SAAS,OAAO,YAAY,QAAQ,IAAI,OAAO,cAAcA,KAAI;AAAA,YAC9E,CAAC,OAAO,GAAG;AAAA,YACX,CAAC,QAAQ,GAAG,iBAAAD,QAAQ,SAAS,SAAS,OAAO,YAAY,QAAQ,IAAI,CAAC,KAAK;AAAA,YAC3E,CAAC,QAAQ,GAAG,aAAa;AAAA,UAC7B;AAAA,QACJ;AACA,cAAM,OAAO,KAAK,QAAQ,KAAK;AAC/B,aAAK,QAAQ,IAAI;AACjB,aAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,cAAc,IAAI;AACrD,aAAK,OAAO,IAAI,KAAK,OAAO,KAAK;AACjC,aAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK;AACnC,aAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,aAAa;AAChD,eAAO;AAAA,MACX,CAAC,IACC,CAAC;AACP,gBAAU,WAAY,gBAAgB,WAAY,WAAY,SAAS,MAAM,GAAG,CAAC;AAAA,IACrF;AACA,UAAM,gBAAgB,CAAC,QAAQ;AAC3B,aAAO,mBAAmB,GAAG,OAAO,EAAE,EAAE,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK;AAAA,IAClE;AACA,UAAM,gBAAgB,CAAC,SAAS;AAE5B,YAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,UAAI,QAAQ,GAAG;AACX,eAAO,KAAK,UAAU,QAAQ,CAAC,EAAE,YAAY;AAAA,MACjD;AACA,aAAO;AAAA,IACX;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,SAAS,UAAU,GAAG,MAAM,CAAC;AAAA,IAChE;AACA,UAAM,eAAe,CAAC,UAAU;AAC5B,YAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,YAAM,UAAU,eAAe;AAC/B,YAAM,WAAW,gBAAgB;AACjC,UAAI,WAAW,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC;AACzC,UAAI,SAAS;AACT,mBAAW,SAAS,IAAI,UAAQ;AAC5B,gBAAM,MAAM,KAAK,OAAO;AACxB,cAAI,KAAK;AACL,kBAAM,SAAS,iBAAAA,QAAQ,SAAS,GAAG;AACnC,gBAAI,CAAC,OAAO,YAAY,QAAQ,GAAG;AAC/B,qBAAO,GAAG,GAAG,GAAG,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,GAAG,GAAG,QAAQ,IAAI,mBAAmB,KAAK,QAAQ,KAAK,EAAE,CAAC;AAAA,YAC9G;AAAA,UACJ;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,WAAK,qBAAqB,aAAc,SAAS,CAAC,KAAK,OAAQ,QAAQ;AAAA,IAC3E;AACA,UAAM,sBAAsB,CAAC,SAAS;AAClC,YAAM,oBAAoB,MAAM,yBAAyB,UAAU,EAAE,OAAO;AAC5E,UAAI,mBAAmB;AACnB,eAAO,kBAAkB;AAAA,UACrB,SAAS;AAAA,UACT,QAAQ;AAAA,QACZ,CAAC;AAAA,MACL;AACA,aAAO,WAAW,IAAI;AAAA,IAC1B;AACA,UAAM,aAAa,CAAC,SAAS;AACzB,YAAM,WAAW,MAAM,gBAAgB,UAAU,EAAE,OAAO;AAC1D,YAAM,UAAU,eAAe;AAC/B,aAAO,WACD,SAAS;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ,CAAC,IACC,KAAK,OAAO;AAAA,IACtB;AACA,UAAM,2BAA2B,CAAC,SAAS;AACvC,YAAM,EAAE,YAAY,mBAAmB,IAAI;AAC3C,YAAM,WAAW,gBAAgB;AACjC,YAAM,mBAAmB,MAAM,wBAAwB,UAAU,EAAE,OAAO;AAC1E,YAAM,EAAE,kBAAkB,IAAI;AAE9B,UAAI,kBAAkB,OAAO,cAAc,CAAC,CAAC,EAAE,KAAK,UAAQ,GAAG,IAAI,GAAG,YAAY,MAAM,GAAG,KAAK,QAAQ,CAAC,GAAG,YAAY,CAAC,GAAG;AACxH,YAAI,MAAM,cAAc;AACpB,gBAAM,aAAa;AAAA,YACf,SAAS,CAAC,WAAW,IAAI,CAAC;AAAA,YAC1B;AAAA,YACA,sBAAsB,mBAChB,MAAM;AACJ,qBAAO,iBAAiB;AAAA,gBACpB,SAAS;AAAA,gBACT,QAAQ;AAAA,cACZ,CAAC;AAAA,YACL,IACE;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,yBAAyB,CAAC,MAAM,SAAS;AAC3C,YAAM,YAAY,MAAM,iBAAiB,UAAU,EAAE,OAAO;AAC5D,UAAI,MAAM,aAAa;AACnB,YAAI,WAAW;AACX,oBAAU;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL,OACK;AACD,mCAAyB,IAAI;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,0BAA0B,CAAC,MAAM,MAAM,UAAU;AACnD,YAAM,EAAE,mBAAmB,IAAI;AAC/B,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,mBAAmB,MAAM,wBAAwB,UAAU,EAAE,OAAO;AAC1E,UAAI,MAAM,aAAa;AACnB,YAAI,MAAM,cAAc;AACpB,gBAAM,aAAa;AAAA,YACf,SAAS,SAAS,IAAI,CAAAE,UAAQ,WAAWA,KAAI,CAAC;AAAA,YAC9C,aAAa;AAAA,YACb;AAAA,YACA,sBAAsB,mBAChB,CAAC,EAAE,OAAAC,OAAM,MAAM;AACb,qBAAO,iBAAiB;AAAA,gBACpB,SAAS;AAAA,gBACT,QAAQ,SAASA,MAAK;AAAA,cAC1B,CAAC;AAAA,YACL,IACE;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,qBAAqB,CAAC,MAAM,SAAS;AACvC,YAAM,EAAE,gBAAgB,IAAI;AAC5B,YAAM,UAAU,YAAY,IAAI;AAChC,YAAM,WAAW,MAAM,gBAAgB,UAAU,EAAE,OAAO;AAC1D,UAAI,UAAU;AACV,eAAO,QAAQ,QAAQ,SAAS;AAAA,UAC5B,SAAS;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,UACR,eAAe,YAAY;AACvB,kBAAM,EAAE,cAAc,IAAI;AAC1B,kBAAM,YAAY,cAAc,YAAY,IAAI,CAAC;AACjD,gBAAI,WAAW;AACX,wBAAU,UAAU,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,iBAAAH,QAAQ,SAAS,UAAU,CAAC,CAAC;AAAA,YAC9E;AAAA,UACJ;AAAA,QACJ,CAAC,CAAC,EAAE,KAAK,SAAO;AACZ,gBAAM,EAAE,cAAc,IAAI;AAC1B,gBAAM,YAAY,cAAc,OAAO;AACvC,cAAI,WAAW;AACX,sBAAU,UAAU;AAAA,UACxB;AACA,iBAAO,OAAO,MAAM,GAAG;AACvB,wBAAc,kBAAkB,EAAE,QAAQ,MAAM,MAAM,IAAI,GAAG,IAAI;AAAA,QACrE,CAAC,EAAE,MAAM,CAAC,QAAQ;AACd,gBAAM,EAAE,cAAc,IAAI;AAC1B,gBAAM,YAAY,cAAc,OAAO;AACvC,cAAI,WAAW;AACX,sBAAU,SAAS;AAAA,UACvB;AACA,cAAI,iBAAiB;AACjB,mBAAO,OAAO,MAAM,GAAG;AAAA,UAC3B,OACK;AACD,sBAAU,WAAW,UAAU,SAAS,OAAO,SAAO,YAAY,GAAG,MAAM,OAAO;AAAA,UACtF;AACA,wBAAc,gBAAgB,EAAE,QAAQ,MAAM,MAAM,IAAI,GAAG,IAAI;AAAA,QACnE,CAAC,EAAE,QAAQ,MAAM;AACb,gBAAM,EAAE,cAAc,IAAI;AAC1B,gBAAM,YAAY,cAAc,OAAO;AACvC,cAAI,WAAW;AACX,sBAAU,UAAU;AAAA,UACxB;AAAA,QACJ,CAAC;AAAA,MACL,OACK;AACD,cAAM,EAAE,cAAc,IAAI;AAC1B,cAAM,YAAY,cAAc,OAAO;AACvC,YAAI,WAAW;AACX,oBAAU,UAAU;AAAA,QACxB;AAAA,MACJ;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,UAAM,iBAAiB,CAAC,SAAS;AAC7B,YAAM,EAAE,cAAc,QAAQ,IAAI;AAClC,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,UAAU,YAAY,IAAI;AAChC,YAAM,YAAY,cAAc,OAAO;AACvC,YAAM,WAAW,gBAAgB,UAAU,EAAE,OAAO;AACpD,UAAI,YAAY,WAAW;AACvB,cAAM,OAAO,UAAU;AACvB,kBAAU,UAAU;AACpB,kBAAU,SAAS;AACnB,kBAAU,UAAU;AACpB,2BAAmB,MAAM,IAAI,EAAE,KAAK,MAAM;AACtC,cAAI,SAAS;AACT,yBAAa,UAAU,QAAQ;AAAA,UACnC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,aAAa,CAAC,OAAO,SAAS;AAChC,YAAM,EAAE,UAAU,QAAQ,IAAI;AAC9B,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,WAAW,MAAM,gBAAgB,UAAU,EAAE,OAAO;AAC1D,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,UAAU,eAAe;AAC/B,YAAM,WAAW,gBAAgB;AACjC,YAAM,gBAAgB,qBAAqB;AAC3C,YAAM,gBAAgB,qBAAqB;AAC3C,YAAM,gBAAgB,qBAAqB;AAC3C,UAAI,cAAc;AAClB,UAAI,YAAY,eAAe;AAE3B,YAAI,SAAS,UAAU,eAAe;AAClC,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,aAAa;AAAA,cACrB,OAAO,QAAQ,oBAAoB;AAAA,cACnC,QAAQ;AAAA,cACR,SAAS,QAAQ,2BAA2B,CAAC,aAAa,CAAC;AAAA,YAC/D,CAAC;AAAA,UACL;AACA;AAAA,QACJ;AACA,cAAM,UAAU,YAAY,UAAU,gBAAgB,SAAS;AAC/D,YAAI,UAAU,GAAG;AACb,gBAAM,gBAAgB,YAAY,MAAM,gBAAgB,SAAS,MAAM;AACvE,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,aAAa;AAAA,cACrB,OAAO,QAAQ,oBAAoB;AAAA,cACnC,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,OAAO;AAAA,gBACH,UAAU;AACN,yBAAO,EAAE,OAAO;AAAA,oBACZ,OAAO;AAAA,kBACX,GAAG;AAAA,oBACC,EAAE,OAAO,CAAC,GAAG,QAAQ,gCAAgC,CAAC,eAAe,OAAO,CAAC,CAAC;AAAA,oBAC9E,EAAE,OAAO;AAAA,sBACL,OAAO;AAAA,oBACX,GAAG,cAAc,IAAI,CAAC,MAAM,UAAU;AAClC,6BAAO,EAAE,OAAO;AAAA,wBACZ,KAAK;AAAA,wBACL,OAAO;AAAA,sBACX,GAAG,KAAK,IAAI;AAAA,oBAChB,CAAC,CAAC;AAAA,kBACN,CAAC;AAAA,gBACL;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,sBAAc,YAAY,MAAM,GAAG,gBAAgB,SAAS,MAAM;AAAA,MACtE;AAEA,UAAI,eAAe;AACf,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,gBAAM,OAAO,MAAM,CAAC;AACpB,cAAI,KAAK,OAAO,eAAe;AAC3B,gBAAI,MAAM,OAAO;AACb,oBAAM,MAAM,aAAa;AAAA,gBACrB,OAAO,QAAQ,oBAAoB;AAAA,gBACnC,QAAQ;AAAA,gBACR,SAAS,QAAQ,0BAA0B,CAAC,aAAa,CAAC;AAAA,cAC9D,CAAC;AAAA,YACL;AACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,YAAY,OAAO,OAAO,CAAC,GAAG,UAAU,aAAa;AAC3D,YAAM,cAAc,WAAW,WAAW,CAAC;AAC3C,YAAM,qBAAqB,CAAC;AAC5B,kBAAY,QAAQ,UAAQ;AACxB,cAAM,EAAE,KAAK,IAAI;AACjB,cAAM,UAAU,aAAa;AAC7B,cAAM,UAAU;AAAA,UACZ,CAAC,QAAQ,GAAG;AAAA,UACZ,CAAC,QAAQ,GAAG,cAAc,IAAI;AAAA,UAC9B,CAAC,QAAQ,GAAG,KAAK;AAAA,UACjB,CAAC,OAAO,GAAG,IAAI,gBAAgB,IAAI;AAAA,UACnC,CAAC,QAAQ,GAAG;AAAA,QAChB;AACA,YAAI,UAAU;AACV,oBAAU,OAAO,IAAI;AAAA,YACjB;AAAA,YACA,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,SAAS;AAAA,UACb;AAAA,QACJ;AACA,cAAM,OAAO,SAAS,OAAO;AAC7B,YAAI,UAAU;AACV,6BAAmB,KAAK,mBAAmB,MAAM,IAAI,CAAC;AAAA,QAC1D;AACA,oBAAY,KAAK,IAAI;AACrB,sBAAc,OAAO,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,MAC/C,CAAC;AACD,gBAAU,WAAW;AACrB,gBAAU,gBAAgB;AAC1B,cAAQ,IAAI,UAAU,qBAAqB,CAAC,CAAC,EAAE,KAAK,MAAM;AACtD,qBAAa,WAAW;AAExB,YAAI,WAAW,cAAc;AACzB,kBAAQ,iBAAiB,MAAM,aAAa,WAAW,OAAO,WAAW;AAAA,QAC7E;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,eAAe,CAAC,SAAS;AAC3B,YAAM,EAAE,UAAU,YAAY,UAAU,IAAI;AAC5C,YAAM,aAAa,kBAAkB;AACrC,YAAM,UAAU,eAAe;AAC/B,UAAI,YAAY;AACZ,eAAO,QAAQ,QAAQ;AAAA,UACnB,QAAQ;AAAA,UACR,OAAO,CAAC;AAAA,UACR,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AACA,aAAO,cAAc;AAAA,QACjB;AAAA,QACA,OAAO,UAAU,aAAa;AAAA,MAClC,CAAC,EAAE,KAAK,CAAC,WAAW;AAChB,mBAAW,OAAO,OAAO,IAAI;AAC7B,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,UAAM,aAAa,CAAC,SAAS;AACzB,mBAAa,IAAI,EAAE,MAAM,MAAM;AAAA,MAE/B,CAAC;AAAA,IACL;AACA,UAAM,oBAAoB,CAAC,MAAM,MAAM,UAAU;AAC7C,YAAM,EAAE,SAAS,IAAI;AACrB,eAAS,OAAO,OAAO,CAAC;AACxB,mBAAa,QAAQ;AAErB,UAAI,WAAW,cAAc;AACzB,gBAAQ,iBAAiB,MAAM,aAAa,WAAW,OAAO,QAAQ;AAAA,MAC1E;AACA,oBAAc,UAAU,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,IAClD;AACA,UAAM,kBAAkB,CAAC,MAAM,MAAM,UAAU;AAC3C,YAAM,iBAAiB,MAAM,sBAAsB,UAAU,EAAE,OAAO;AACtE,YAAM,WAAW,MAAM,gBAAgB,UAAU,EAAE,OAAO;AAC1D,cAAQ,QAAQ,iBACV,eAAe;AAAA,QACb,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ,CAAC,IACC,IAAI,EAAE,KAAK,YAAU;AACvB,YAAI,QAAQ;AACR,cAAI,UAAU;AACV,oBAAQ,QAAQ,SAAS;AAAA,cACrB,SAAS;AAAA,cACT,QAAQ;AAAA,YACZ,CAAC,CAAC,EAAE,KAAK,MAAM;AACX,gCAAkB,MAAM,MAAM,KAAK;AAAA,YACvC,CAAC,EAAE,MAAM,OAAK,CAAC;AAAA,UACnB,OACK;AACD,8BAAkB,MAAM,MAAM,KAAK;AAAA,UACvC;AAAA,QACJ,OACK;AACD,wBAAc,eAAe,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,QACvD;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,sBAAsB,CAAC,MAAM,SAAS;AACxC,oBAAc,YAAY,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,IACpD;AACA,UAAM,oBAAoB,CAAC,MAAM,SAAS;AACtC,YAAM,mBAAmB,MAAM,wBAAwB,UAAU,EAAE,OAAO;AAC1E,YAAM,aAAa,MAAM,kBAAkB,UAAU,EAAE,OAAO;AAC9D,cAAQ,QAAQ,mBACV,iBAAiB;AAAA,QACf,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ,CAAC,IACC,IAAI,EAAE,KAAK,YAAU;AACvB,YAAI,QAAQ;AACR,cAAI,YAAY;AACZ,oBAAQ,QAAQ,WAAW;AAAA,cACvB,SAAS;AAAA,cACT,QAAQ;AAAA,YACZ,CAAC,CAAC,EAAE,KAAK,MAAM;AACX,kCAAoB,MAAM,IAAI;AAAA,YAClC,CAAC,EAAE,MAAM,OAAK,CAAC;AAAA,UACnB,OACK;AACD,gCAAoB,MAAM,IAAI;AAAA,UAClC;AAAA,QACJ,OACK;AACD,wBAAc,iBAAiB,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL;AACA,UAAM,6BAA6B,CAAC,SAAS;AACzC,YAAM,aAAa,KAAK;AACxB,YAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,UAAI,YAAY;AACZ,cAAM,EAAE,GAAG,SAAS,GAAG,SAAS,QAAQ,cAAc,OAAO,YAAY,IAAI,WAAW,sBAAsB;AAC9G,YAAI,UAAU,WAAW,UAAU,UAAU,eAAe,UAAU,WAAW,UAAU,UAAU,cAAc;AAC/G,oBAAU,qBAAqB;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,4BAA4B,CAAC,SAAS;AACxC,YAAM,eAAe,KAAK;AAC1B,UAAI,cAAc;AACd,cAAM,EAAE,MAAM,IAAI;AAClB,YAAI,SAAS,MAAM,QAAQ;AACvB,eAAK,eAAe;AACpB,oBAAU,qBAAqB;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,0BAA0B,CAAC,MAAM,UAAU;AAC7C,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,EAAE,kBAAkB,IAAI;AAC9B,YAAM,UAAU,eAAe;AAC/B,UAAI,SAAS;AACT,cAAM,gBAAgB,kBAAkB,OAAO,cAAc,WAAW,SAAS,aAAa,CAAC,CAAC;AAChG,gBAAQ,MAAM,OAAO,UAAQ;AACzB,gBAAM,WAAW,GAAG,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,YAAY;AAChE,cAAI,cAAc,KAAK,UAAQ,GAAG,IAAI,GAAG,YAAY,MAAM,QAAQ,GAAG;AAClE,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AAEA,UAAI,CAAC,MAAM,QAAQ;AACf,YAAI,MAAM,OAAO;AACb,gBAAM,MAAM,aAAa;AAAA,YACrB,OAAO,QAAQ,oBAAoB;AAAA,YACnC,QAAQ;AAAA,YACR,SAAS,QAAQ,0BAA0B;AAAA,UAC/C,CAAC;AAAA,QACL;AACA;AAAA,MACJ;AACA,iBAAW,OAAO,IAAI;AAAA,IAC1B;AACA,UAAM,wBAAwB,CAAC,SAAS;AACpC,YAAM,eAAe,KAAK;AAC1B,UAAI,cAAc;AACd,cAAM,EAAE,MAAM,IAAI;AAClB,YAAI,SAAS,MAAM,QAAQ;AACvB,eAAK,eAAe;AACpB,gBAAM,QAAQ,oBAAoB,KAAK;AACvC,cAAI,MAAM,QAAQ;AACd,oCAAwB,MAAM,KAAK;AAAA,UACvC;AAAA,QACJ;AAAA,MACJ;AACA,gBAAU,qBAAqB;AAAA,IACnC;AACA,UAAM,sBAAsB,CAAC,UAAU;AACnC,YAAM,QAAQ,CAAC;AACf,uBAAAA,QAAQ,UAAU,OAAO,UAAQ;AAC7B,cAAM,OAAO,KAAK,UAAU;AAC5B,YAAI,MAAM;AACN,gBAAM,KAAK,IAAI;AAAA,QACnB;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,eAAe,oBAAoB;AACzC,YAAM,UAAU,eAAe;AAC/B,UAAI,MAAM,OAAO;AACb,cAAM,MAAM,KAAK;AAAA,UACb,OAAO,eAAe,QAAQ,gCAAgC,IAAI,QAAQ,wBAAwB,UAAU,eAAe,WAAW,EAAE;AAAA,UACxI,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,OAAO;AAAA,YACH,UAAU;AACN,oBAAM,EAAE,iBAAiB,cAAc,SAAS,IAAI;AACpD,oBAAM,EAAE,aAAa,YAAY,oBAAoB,UAAU,IAAI;AACnE,oBAAM,EAAE,SAAS,IAAI;AACrB,oBAAM,aAAa,kBAAkB;AACrC,oBAAM,MAAM,CAAC;AACb,kBAAI,gBAAgB,cAAc,IAAI;AAClC,oBAAI,aAAa;AACjB,oBAAI,cAAc;AAClB,oBAAI,SAAS;AAAA,cACjB;AACA,qBAAO,EAAE,OAAO,OAAO,OAAO,EAAE,KAAK,cAAc,OAAO,CAAC,0BAA0B;AAAA,gBACzE,gBAAgB;AAAA,gBAChB,gBAAgB;AAAA,gBAChB,cAAc;AAAA,gBACd,eAAe;AAAA,gBACf,YAAY;AAAA,cAChB,CAAC,EAAE,GAAG,GAAG,GAAG;AAAA,gBAChB,UACO,WACG,EAAE,iBAAiB;AAAA,kBACjB,MAAM,wBAAwB,aAAa,KAAK,WAAW;AAAA,kBAC3D,KAAK;AAAA,kBACL,OAAO;AAAA,gBACX,GAAG;AAAA,kBACC,SAAS,MAAM,oBAAoB,UAAU,IAAI,EAAE,OAAO,kBAAkB,IAAI,CAAC;AAAA,gBACrF,CAAC,IACC,EAAE,OAAO;AAAA,kBACP,OAAO;AAAA,gBACX,GAAG,oBAAoB,UAAU,IAAI,EAAE,OAAO,kBAAkB,IAAI,CAAC,CAAC,IACxE,EAAE,OAAO;AAAA,kBACP,OAAO;AAAA,gBACX,GAAG;AAAA,kBACC,iBAAiB,IAAI;AAAA,kBACrB,WACM,EAAE,iBAAiB;AAAA,oBACjB,MAAM,wBAAwB,aAAa,KAAK,WAAW;AAAA,oBAC3D,KAAK;AAAA,oBACL,OAAO;AAAA,kBACX,GAAG;AAAA,oBACC,SAAS,MAAM,mBAAmB,UAAU,KAAK;AAAA,kBACrD,CAAC,IACC,EAAE,OAAO;AAAA,oBACP,OAAO;AAAA,kBACX,GAAG,mBAAmB,UAAU,IAAI,CAAC;AAAA,gBAC7C,CAAC;AAAA,gBACL,WACM,EAAE,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,OAAO;AAAA,gBACX,CAAC,IACC,mBAAmB,SAAS;AAAA,gBAClC,qBACM,EAAE,OAAO;AAAA,kBACP,OAAO;AAAA,gBACX,GAAG,QAAQ,4BAA4B,CAAC,IACtC,mBAAmB,SAAS;AAAA,cACtC,CAAC;AAAA,YACL;AAAA,UACJ;AAAA,UACA,SAAS;AACL,sBAAU,gBAAgB;AAAA,UAC9B;AAAA,UACA,SAAS;AACL,sBAAU,gBAAgB;AAAA,UAC9B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,cAAc,CAAC,MAAM,QAAQ,YAAY;AAC3C,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,KAAK,QAAQ;AACnB,YAAM,UAAU,aAAa;AAC7B,YAAM,YAAY,gBAAgB,UAAU;AAC5C,UAAI,CAAC,WAAW;AACZ;AAAA,MACJ;AACA,YAAM,cAAc,UAAU,sBAAsB;AACpD,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,qBAAqB;AACtC,YAAM,cAAc,gBAAgB,WAAW;AAC/C,UAAI,aAAa;AACb,cAAM,WAAW,OAAO,sBAAsB;AAC9C,oBAAY,MAAM,UAAU;AAC5B,oBAAY,MAAM,MAAM,GAAG,KAAK,IAAI,GAAG,SAAS,IAAI,YAAY,CAAC,CAAC;AAClE,oBAAY,MAAM,OAAO,GAAG,KAAK,IAAI,GAAG,SAAS,IAAI,YAAY,CAAC,CAAC;AACnE,oBAAY,MAAM,SAAS,GAAG,SAAS,MAAM;AAC7C,oBAAY,MAAM,QAAQ,GAAG,SAAS,QAAQ,CAAC;AAC/C,oBAAY,aAAa,YAAY,OAAO;AAAA,MAChD;AAAA,IACJ;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,qBAAqB;AACtC,UAAI,UAAU;AACV,iBAAS,MAAM,UAAU;AAAA,MAC7B;AACA,UAAI,UAAU;AACV,iBAAS,MAAM,UAAU;AAAA,MAC7B;AAAA,IACJ;AAEA,UAAM,+BAA+B,CAAC,SAAS;AAC3C,WAAK,gBAAgB;AACrB,UAAI,KAAK,cAAc;AACnB,aAAK,aAAa,aAAa,SAAS,GAAG,GAAG,CAAC;AAAA,MACnD;AACA,YAAM,SAAS,KAAK;AACpB,YAAM,WAAW,OAAO;AACxB,YAAM,YAAY,iBAAAA,QAAQ,YAAY,MAAM,KAAK,SAAS,QAAQ,GAAG,UAAQ,WAAW,IAAI;AAC5F,gBAAU,aAAa;AACvB,gBAAU,YAAY;AACtB,iBAAW,MAAM;AACb,kBAAU,aAAa;AAAA,MAC3B,GAAG,GAAG;AAAA,IACV;AACA,UAAM,8BAA8B,CAAC,SAAS;AAC1C,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,YAAM,EAAE,UAAU,IAAI;AACtB,UAAI,cAAc,IAAI;AAClB;AAAA,MACJ;AACA,YAAM,UAAU,eAAe;AAC/B,YAAM,SAAS,KAAK;AACpB,YAAM,WAAW,OAAO;AACxB,YAAM,YAAY,iBAAAA,QAAQ,YAAY,MAAM,KAAK,SAAS,QAAQ,GAAG,UAAQ,WAAW,IAAI;AAC5F,UAAI,UAAU;AACd,UAAI,SAAS;AACT,cAAM,UAAU,KAAK,UAAU,OAAO,sBAAsB,EAAE;AAC9D,kBAAU,UAAU,OAAO,cAAc,IAAI,SAAS;AAAA,MAC1D,OACK;AACD,cAAM,UAAU,KAAK,UAAU,OAAO,sBAAsB,EAAE;AAC9D,kBAAU,UAAU,OAAO,eAAe,IAAI,QAAQ;AAAA,MAC1D;AACA,UAAI,cAAc,WAAW;AACzB,oBAAY,MAAM,QAAQ,OAAO;AACjC;AAAA,MACJ;AACA,kBAAY,MAAM,QAAQ,OAAO;AACjC,mBAAa,gBAAgB;AAC7B,mBAAa,cAAc;AAAA,IAC/B;AACA,UAAM,6BAA6B,CAAC,SAAS;AACzC,YAAM,EAAE,UAAU,UAAU,IAAI;AAChC,YAAM,EAAE,eAAe,YAAY,IAAI;AACvC,YAAM,WAAW;AACjB,YAAM,cAAc;AACpB,YAAM,kBAAkB,gBAAgB,YAAY,gBAAgB,UAAU,IAAI;AAClF,YAAM,UAAU,SAAS,QAAQ;AACjC,YAAM,UAAU,SAAS,WAAW;AACpC,UAAI,WAAW,SAAS;AACpB,iBAAS,OAAO,UAAU,CAAC;AAC3B,cAAM,WAAW,iBAAAA,QAAQ,YAAY,UAAU,UAAQ,YAAY,IAAI;AACvE,cAAM,SAAS,WAAW;AAC1B,iBAAS,OAAO,QAAQ,GAAG,OAAO;AAClC,sBAAc,gBAAgB;AAAA,UAC1B;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,UACb,QAAQ;AAAA,YACJ,UAAU;AAAA,YACV;AAAA,UACJ;AAAA,QACJ,GAAG,IAAI;AAAA,MACX;AACA,kBAAY;AACZ,gBAAU,YAAY;AAAA,IAC1B;AACA,UAAM,2BAA2B,CAAC,SAAS;AACvC,UAAI,UAAU;AACV,aAAK,gBAAgB;AAAA,MACzB;AACA,gBAAU,cAAc;AAAA,IAC5B;AACA,UAAM,yBAAyB,CAAC,SAAS;AACrC,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,CAAC,eAAe,CAAC,eAAe;AAChC;AAAA,MACJ;AACA,YAAM,gBAAgB,KAAK,iBAAiB,KAAK,cAAc;AAC/D,UAAI,CAAC,eAAe;AAChB;AAAA,MACJ;AACA,YAAM,EAAE,MAAM,IAAI;AAClB,UAAI,CAAC,OAAO;AACR;AAAA,MACJ;AACA,YAAM,QAAQ,oBAAoB,KAAK;AACvC,UAAI,MAAM,QAAQ;AACd,aAAK,eAAe;AACpB,gCAAwB,MAAM,KAAK;AAAA,MACvC;AAAA,IACJ;AACA,UAAM,6BAA6B,CAAC,SAAS;AACzC,YAAM,KAAK,QAAQ;AACnB,YAAM,UAAU,aAAa;AAC7B,UAAI,cAAc,mBAAmB,MAAM,EAAE,EAAE;AAC/C,UAAI,CAAC,eAAe,SAAS;AACzB,cAAM,WAAW,QAAQ,iBAAiB;AAC1C,cAAM,UAAU,WAAW,SAAS,gBAAgB;AACpD,sBAAc,mBAAmB,MAAM,OAAO,EAAE;AAAA,MACpD;AACA,gBAAU,cAAc;AAAA,IAC5B;AACA,UAAM,wBAAwB,MAAM;AAChC,gBAAU,cAAc;AAAA,IAC5B;AACA,UAAM,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS;AACL,eAAO,aAAa,IAAI;AAAA,MAC5B;AAAA,IACJ;AACA,UAAM,uBAAuB,CAAC;AAC9B,WAAO,OAAO,WAAW,eAAe,oBAAoB;AAC5D,UAAM,qBAAqB,CAAC,UAAU,eAAe;AACjD,YAAM,EAAE,kBAAkB,oBAAoB,cAAc,cAAc,aAAa,iBAAiB,SAAS,IAAI;AACrH,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,eAAe,oBAAoB;AACzC,YAAM,WAAW,gBAAgB;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,aAAa,MAAM;AACzB,YAAM,MAAM,CAAC;AACb,UAAI,YAAY,SAAS,SAAS,GAAG;AACjC,YAAI,cAAc;AAClB,YAAI,aAAa;AACjB,YAAI,YAAY;AAAA,MACpB;AACA,aAAO,SAAS,IAAI,CAAC,MAAM,UAAU;AACjC,cAAM,UAAU,YAAY,IAAI;AAChC,cAAM,YAAY,cAAc,OAAO;AACvC,cAAM,YAAY,aAAa,UAAU;AACzC,cAAM,UAAU,aAAa,UAAU,WAAW;AAClD,eAAO,EAAE,OAAO,OAAO,OAAO,EAAE,KAAK,WAAW,UAAU,OAAO,OAAO,CAAC,yBAAyB;AAAA,UACtF,eAAe;AAAA,UACf,eAAe;AAAA,UACf,aAAa;AAAA,QACjB,CAAC,GAAG,QAAQ,SAAS,WAAW,WAAW,OAAO,KAAK,GAAG,GAAG,GAAG;AAAA,UACpE,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,KAAK;AAAA,cACH,OAAO,QAAQ,EAAE,oBAAoB,GAAG,KAAK,QAAQ,CAAC,GAAG,kBAAkB,CAAC,EAAE,KAAK,QAAQ,EAAE;AAAA,YACjG,CAAC;AAAA,UACL,CAAC;AAAA,UACD,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,YACP,QAAQ,MAAM;AACV,kBAAI,CAAC,aAAa,CAAC,SAAS;AACxB,uCAAuB,MAAM,IAAI;AAAA,cACrC;AAAA,YACJ;AAAA,UACJ,GAAG,GAAG,KAAK,QAAQ,KAAK,EAAE,EAAE;AAAA,UAC5B,YACM,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,KAAK;AAAA,cACH,OAAO,QAAQ,EAAE;AAAA,YACrB,CAAC;AAAA,UACL,CAAC,IACC,mBAAmB;AAAA,UACzB,gBAAgB,aAAa,YACvB,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,eAAe,iBAAAA,QAAQ,eAAe,cAAc,EAAE,SAAS,UAAU,QAAQ,CAAC,IAAI,QAAQ,6BAA6B,CAAC,UAAU,OAAO,CAAC,CAAC,IAChJ,mBAAmB;AAAA,UACzB,mBAAmB,UACb,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,gBAAoB;AAAA,cAClB,MAAM,QAAQ,EAAE;AAAA,cAChB,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,SAAS,QAAQ,qBAAqB;AAAA,cACtC,UAAU;AACN,+BAAe,IAAI;AAAA,cACvB;AAAA,YACJ,CAAC;AAAA,UACL,CAAC,IACC,mBAAmB;AAAA,UACzB,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,UACX,GAAG;AAAA,YACC,aACM,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG,WAAW,WAAW,EAAE,QAAQ,MAAM,YAAY,UAAU,aAAa,CAAC,CAAC,CAAC,IAC7E,mBAAmB;AAAA,YACzB,sBAAsB,CAAC,YACjB,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,cACP,QAAQ,MAAM;AACV,kCAAkB,MAAM,IAAI;AAAA,cAChC;AAAA,YACJ,GAAG;AAAA,cACC,EAAE,KAAK;AAAA,gBACH,OAAO,QAAQ,EAAE;AAAA,cACrB,CAAC;AAAA,YACL,CAAC,IACC,mBAAmB;AAAA,YACzB,oBAAoB,CAAC,gBAAgB,CAAC,cAAc,CAAC,YAC/C,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,cACP,QAAQ,MAAM;AACV,gCAAgB,MAAM,MAAM,KAAK;AAAA,cACrC;AAAA,YACJ,GAAG;AAAA,cACC,EAAE,KAAK;AAAA,gBACH,OAAO,QAAQ,EAAE;AAAA,cACrB,CAAC;AAAA,YACL,CAAC,IACC,mBAAmB;AAAA,UAC7B,CAAC;AAAA,QACL,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,UAAM,mBAAmB,CAAC,eAAe;AACrC,YAAM,EAAE,kBAAkB,YAAY,YAAY,gBAAgB,gBAAgB,iBAAiB,IAAI;AACvG,YAAM,aAAa,kBAAkB;AACrC,YAAM,eAAe,oBAAoB;AACzC,YAAM,cAAc,oBAAoB;AACxC,YAAM,YAAY,iBAAiB;AACnC,YAAM,cAAc,MAAM;AAC1B,YAAM,UAAU,MAAM,OAAO,MAAM;AACnC,UAAI,gBAAgB,CAAC,kBAAkB;AACnC,eAAO,mBAAmB;AAAA,MAC9B;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,OAAO;AAAA,MACX,GAAG;AAAA,QACC,oBAAoB,YACd,mBAAmB,IACnB,EAAE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,QACb,GAAG,cACG,WAAW,YAAY,EAAE,SAAS,UAAU,CAAC,CAAC,IAC9C;AAAA,UACE,EAAE,gBAAoB;AAAA,YAClB,OAAO;AAAA,YACP,SAAU,cAAc,iBAAmB,aAAa,GAAG,UAAU,KAAK,QAAQ,wBAAwB,IAAK;AAAA,YAC/G,MAAM,iBAAkB,cAAc,QAAQ,EAAE,kBAAmB;AAAA,YACnE,UAAU;AAAA,UACd,CAAC;AAAA,QACL,CAAC;AAAA,QACT,eAAe,eAAe,WACxB,EAAE,OAAO;AAAA,UACP,OAAO;AAAA,QACX,GAAG,UAAU,WAAW,QAAQ,EAAE,SAAS,UAAU,CAAC,CAAC,IAAI,WAAW,IACpE,mBAAmB;AAAA,MAC7B,CAAC;AAAA,IACL;AACA,UAAM,gBAAgB,MAAM;AACxB,YAAM,EAAE,UAAU,YAAY,SAAS,IAAI;AAC3C,YAAM,EAAE,UAAU,WAAW,IAAI;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,UAAU,gBAAgB,OAAO,IAAI;AAC7C,YAAM,eAAe,WAAW;AAChC,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,YAAY,SAAS,SAAS,UAAU;AACxC,qBAAa,SAAS,SAAS;AAC/B,mBAAW,SAAS,MAAM,GAAG,QAAQ;AAAA,MACzC;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO;AAAA,MACX,GAAG,WACG;AAAA,QACE,kBAAkB,cAAc,eAC1B,mBAAmB,IACnB,iBAAiB,IAAI;AAAA,QAC1B,SAAS,UAAW,kBAAkB,eACjC,EAAE,OAAO;AAAA,UACP,OAAO,CAAC,iCAAiC;AAAA,YACjC,kBAAkB;AAAA,UACtB,CAAC;AAAA,QACT,GAAG;AAAA,UACC,SAAS,SACF,WACG,EAAE,iBAAiB;AAAA,YACjB,MAAM,wBAAwB,aAAa,KAAK,WAAW;AAAA,YAC3D,KAAK;AAAA,YACL,OAAO;AAAA,UACX,GAAG;AAAA,YACC,SAAS,MAAM,mBAAmB,UAAU,KAAK;AAAA,UACrD,CAAC,IACC,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,mBAAmB,UAAU,KAAK,CAAC,IACxC,mBAAmB;AAAA,UACzB,kBAAkB,aACZ,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,gBAAoB;AAAA,cAClB,MAAM;AAAA,cACN,SAAS,QAAQ,0BAA0B,CAAC,SAAS,MAAM,CAAC;AAAA,cAC5D,QAAQ;AAAA,cACR,SAAS;AAAA,YACb,CAAC;AAAA,UACL,CAAC,IACC,mBAAmB;AAAA,UACzB,kBAAkB,cAAc,eAC1B,iBAAiB,KAAK,IACtB,mBAAmB;AAAA,QAC7B,CAAC,IACC,mBAAmB;AAAA,MAC7B,IACE;AAAA,QACE,iBAAiB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACT;AACA,UAAM,sBAAsB,CAAC,UAAU,eAAe;AAClD,YAAM,EAAE,kBAAkB,cAAc,cAAc,aAAa,iBAAiB,SAAS,IAAI;AACjG,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,eAAe,oBAAoB;AACzC,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,YAAM,aAAa,MAAM;AACzB,YAAM,MAAM;AAAA,QACR,aAAa;AAAA,MACjB;AACA,UAAI,YAAY,SAAS,SAAS,GAAG;AACjC,YAAI,cAAc;AAClB,YAAI,aAAa;AACjB,YAAI,YAAY;AAAA,MACpB;AACA,aAAO,SAAS,IAAI,CAAC,MAAM,UAAU;AACjC,cAAM,UAAU,YAAY,IAAI;AAChC,cAAM,YAAY,cAAc,OAAO;AACvC,cAAM,YAAY,aAAa,UAAU;AACzC,cAAM,UAAU,aAAa,UAAU,WAAW;AAClD,eAAO,EAAE,OAAO,OAAO,OAAO,EAAE,KAAK,WAAW,UAAU,OAAO,OAAO,CAAC,0BAA0B;AAAA,UACvF,eAAe;AAAA,UACf,cAAc,UAAU;AAAA,UACxB,eAAe;AAAA,UACf,aAAa;AAAA,QACjB,CAAC,GAAG,QAAQ,SAAS,WAAW,WAAW,OAAO,KAAK,GAAG,GAAG,GAAG;AAAA,UACpE,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO,aAAa,OAAO;AAAA,YAC3B,OAAO,QAAQ,0BAA0B;AAAA,YACzC,QAAQ,MAAM;AACV,kBAAI,CAAC,aAAa,CAAC,SAAS;AACxB,wCAAwB,MAAM,MAAM,KAAK;AAAA,cAC7C;AAAA,YACJ;AAAA,UACJ,GAAG;AAAA,YACC,aAAa,YACP,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG;AAAA,cACC,EAAE,OAAO;AAAA,gBACL,OAAO;AAAA,cACX,GAAG;AAAA,gBACC,EAAE,KAAK;AAAA,kBACH,OAAO,QAAQ,EAAE;AAAA,gBACrB,CAAC;AAAA,cACL,CAAC;AAAA,cACD,eACM,EAAE,OAAO;AAAA,gBACP,OAAO;AAAA,cACX,GAAG,eAAe,iBAAAA,QAAQ,eAAe,cAAc,EAAE,SAAS,UAAU,QAAQ,CAAC,IAAI,QAAQ,6BAA6B,CAAC,UAAU,OAAO,CAAC,CAAC,IAChJ,mBAAmB;AAAA,YAC7B,CAAC,IACC,mBAAmB;AAAA,YACzB,CAAC,YACM,WAAW,kBACR,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG;AAAA,cACC,EAAE,gBAAoB;AAAA,gBAClB,MAAM,QAAQ,EAAE;AAAA,gBAChB,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,SAAS,QAAQ,qBAAqB;AAAA,gBACtC,UAAU;AACN,iCAAe,IAAI;AAAA,gBACvB;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,IACC,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG;AAAA,cACC,EAAE,OAAO;AAAA,gBACL,OAAO;AAAA,gBACP,KAAK,oBAAoB,IAAI;AAAA,cACjC,CAAC;AAAA,YACL,CAAC,IACH,mBAAmB;AAAA,YACzB,EAAE,OAAO;AAAA,cACL,OAAO;AAAA,cACP,QAAQ,MAAM;AACV,qBAAK,gBAAgB;AAAA,cACzB;AAAA,YACJ,GAAG;AAAA,cACC,aACM,EAAE,OAAO;AAAA,gBACP,OAAO;AAAA,cACX,GAAG,WAAW,WAAW,EAAE,QAAQ,MAAM,YAAY,UAAU,aAAa,CAAC,CAAC,CAAC,IAC7E,mBAAmB;AAAA,cACzB,oBAAoB,CAAC,gBAAgB,CAAC,cAAc,CAAC,YAC/C,EAAE,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,QAAQ,MAAM;AACV,uBAAK,gBAAgB;AACrB,kCAAgB,MAAM,MAAM,KAAK;AAAA,gBACrC;AAAA,cACJ,GAAG;AAAA,gBACC,EAAE,KAAK;AAAA,kBACH,OAAO,QAAQ,EAAE;AAAA,gBACrB,CAAC;AAAA,cACL,CAAC,IACC,mBAAmB;AAAA,YAC7B,CAAC;AAAA,UACL,CAAC;AAAA,QACL,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,UAAM,oBAAoB,CAAC,eAAe;AACtC,YAAM,EAAE,kBAAkB,YAAY,YAAY,gBAAgB,gBAAgB,iBAAiB,IAAI;AACvG,YAAM,eAAe,oBAAoB;AACzC,YAAM,cAAc,oBAAoB;AACxC,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,YAAM,cAAc,MAAM;AAC1B,YAAM,WAAW,MAAM;AACvB,UAAI,gBAAgB,CAAC,oBAAqB,oBAAoB,WAAY;AACtE,eAAO,mBAAmB;AAAA,MAC9B;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO;AAAA,MACX,GAAG;AAAA,QACC,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,QACb,GAAG,cACG,YAAY,EAAE,SAAS,UAAU,CAAC,IAClC;AAAA,UACE,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO,aAAa,OAAO;AAAA,UAC/B,GAAG;AAAA,YACC,iBACM,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG;AAAA,cACC,EAAE,KAAK;AAAA,gBACH,OAAO,cAAc,QAAQ,EAAE;AAAA,cACnC,CAAC;AAAA,YACL,CAAC,IACC,mBAAmB;AAAA,YACzB,cAAc,iBACR,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG,aAAa,GAAG,UAAU,KAAK,QAAQ,uBAAuB,CAAC,IAChE,mBAAmB;AAAA,YACzB,eAAe,eAAe,YACxB,EAAE,OAAO;AAAA,cACP,OAAO;AAAA,YACX,GAAG,WAAW,WAAW,SAAS,EAAE,SAAS,UAAU,CAAC,CAAC,IAAI,WAAW,IACtE,mBAAmB;AAAA,UAC7B,CAAC;AAAA,QACL,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,EAAE,UAAU,SAAS,IAAI;AAC/B,YAAM,EAAE,UAAU,WAAW,IAAI;AACjC,YAAM,WAAW,gBAAgB;AACjC,YAAM,EAAE,UAAU,eAAe,IAAI;AACrC,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,YAAY,SAAS,SAAS,UAAU;AACxC,qBAAa,SAAS,SAAS;AAC/B,mBAAW,SAAS,MAAM,GAAG,QAAQ;AAAA,MACzC;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO;AAAA,MACX,GAAG,WACG;AAAA,QACE,WACM,EAAE,iBAAiB;AAAA,UACjB,MAAM,wBAAwB,aAAa,KAAK,WAAW;AAAA,UAC3D,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG;AAAA,UACC,SAAS,MAAM,oBAAoB,UAAU,KAAK,EAAE,OAAO;AAAA,YACvD,kBAAkB,aACZ,EAAE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,OAAO;AAAA,YACX,GAAG;AAAA,cACC,EAAE,gBAAoB;AAAA,gBAClB,MAAM;AAAA,gBACN,SAAS,QAAQ,0BAA0B,CAAC,SAAS,MAAM,CAAC;AAAA,gBAC5D,QAAQ;AAAA,gBACR,SAAS;AAAA,cACb,CAAC;AAAA,YACL,CAAC,IACC,mBAAmB;AAAA,YACzB,kBAAkB,KAAK;AAAA,UAC3B,CAAC;AAAA,QACL,CAAC,IACC,EAAE,OAAO;AAAA,UACP,OAAO;AAAA,QACX,GAAG,oBAAoB,UAAU,KAAK,EAAE,OAAO;AAAA,UAC3C,kBAAkB,aACZ,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG;AAAA,YACC,EAAE,gBAAoB;AAAA,cAClB,MAAM;AAAA,cACN,SAAS,QAAQ,0BAA0B,CAAC,SAAS,MAAM,CAAC;AAAA,cAC5D,QAAQ;AAAA,cACR,SAAS;AAAA,YACb,CAAC;AAAA,UACL,CAAC,IACC,mBAAmB;AAAA,UACzB,kBAAkB,KAAK;AAAA,QAC3B,CAAC,CAAC;AAAA,MACV,IACE;AAAA,QACE,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG;AAAA,UACC,kBAAkB,KAAK;AAAA,QAC3B,CAAC;AAAA,MACL,CAAC;AAAA,IACT;AACA,UAAM,WAAW,MAAM;AACnB,YAAM,EAAE,iBAAiB,cAAc,eAAe,SAAS,IAAI;AACnE,YAAM,EAAE,oBAAoB,eAAe,aAAa,UAAU,IAAI;AACtE,YAAM,QAAQ,YAAY;AAC1B,YAAM,aAAa,kBAAkB;AACrC,YAAM,eAAe,oBAAoB;AACzC,YAAM,UAAU,eAAe;AAC/B,YAAM,MAAM;AAAA,QACR,aAAa;AAAA,MACjB;AACA,UAAI,gBAAgB,cAAc,IAAI;AAClC,YAAI,aAAa;AACjB,YAAI,cAAc;AAClB,YAAI,SAAS;AAAA,MACjB;AACA,aAAO,EAAE,OAAO,OAAO,OAAO,EAAE,KAAK,SAAS,OAAO,CAAC,cAAc;AAAA,QACxD,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,QACpB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,YAAY;AAAA,MAChB,CAAC,EAAE,GAAG,GAAG,GAAG;AAAA,QAChB,UAAU,gBAAgB,IAAI,cAAc;AAAA,QAC5C,WACM,EAAE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,OAAO;AAAA,QACX,CAAC,IACC,mBAAmB,SAAS;AAAA,QAClC,sBAAsB,CAAC,gBACjB,EAAE,OAAO;AAAA,UACP,OAAO;AAAA,QACX,GAAG,QAAQ,4BAA4B,CAAC,IACtC,mBAAmB,SAAS;AAAA,MACtC,CAAC;AAAA,IACL;AACA,UAAM,WAAW,IAAI,CAAC;AACtB,UAAM,MAAM,MAAM,aAAa,MAAM,WAAW,SAAS,GAAG,MAAM;AAC9D,eAAS;AAAA,IACb,CAAC;AACD,UAAM,MAAM,MAAM,YAAY,MAAM;AAChC,eAAS;AAAA,IACb,CAAC;AACD,UAAM,UAAU,MAAM;AAClB,qBAAe;AAAA,IACnB,CAAC;AACD,cAAU,MAAM;AACZ,UAAI,MAAwC;AACxC,YAAI,MAAM,YAAY,MAAM,YAAY;AACpC,iBAAO,0BAA0B,CAAC,YAAY,aAAa,CAAC;AAAA,QAChE;AACA,YAAI,MAAM,YAAY;AAClB,kBAAQ,qBAAqB,CAAC,eAAe,cAAc,CAAC;AAAA,QAChE;AAAA,MACJ;AACA,UAAI,MAAM,UAAU;AAChB,kBAAU;AAAA,MACd;AACA,mBAAa,GAAG,WAAW,SAAS,sBAAsB;AAC1D,mBAAa,GAAG,WAAW,aAAa,0BAA0B;AAClE,mBAAa,GAAG,WAAW,QAAQ,qBAAqB;AAAA,IAC5D,CAAC;AACD,gBAAY,MAAM;AACd,gBAAU,qBAAqB;AAC/B,mBAAa,IAAI,WAAW,OAAO;AACnC,mBAAa,IAAI,WAAW,WAAW;AACvC,mBAAa,IAAI,WAAW,MAAM;AAAA,IACtC,CAAC;AACD,mBAAe;AACf,cAAU,WAAW;AACrB,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;;;AElgDM,IAAM,YAAY,OAAO,OAAO,CAAC,GAAG,gBAAoB;AAAA,EAC3D,QAAQ,KAAK;AACT,QAAI,UAAU,eAAmB,MAAM,cAAkB;AAAA,EAC7D;AACJ,CAAC;AACD,WAAW,IAAI,SAAS;AACxB,MAAM,UAAU,cAAkB;AAClC,MAAM,WAAW;AACjB,MAAM,WAAW;AACV,IAAM,SAAS;AACtB,IAAOI,kBAAQ;;;ACZf,IAAO,qBAAQC;", "names": ["import_xe_utils", "XEUtils", "XEUtils", "name", "item", "index", "upload_default", "upload_default"]}