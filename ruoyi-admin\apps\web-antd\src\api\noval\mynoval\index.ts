import type { MynovalVO, MynovalForm, MynovalQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询我的小说列表
* @param params
* @returns 我的小说列表
*/
export function mynovalList(userid: number, params: MynovalQuery = {}) {
  return requestClient.get<PageResult<MynovalVo>>(`/noval/mynoval/list/${userid}`, { params });
}

/**
 * 导出我的小说列表
 * @param params
 * @returns 我的小说列表
 */
export function mynovalExport(userid: number, params: MynovalQuery = {}) {
  return commonExport('/system/mynoval/export', { userid, ...params });
}

/**
 * 查询我的小说详情
 * @param id id
 * @returns 我的小说详情
 */
export function mynovalInfo(id: ID) {
  return requestClient.get<MynovalVO>(`/system/mynoval/${id}`);
}

/**
 * 新增我的小说
 * @param data
 * @returns void
 */
export function mynovalAdd(data: MynovalForm) {
  return requestClient.postWithMsg<MynovalVO>('/noval/mynoval', data);
}

/**
 * 更新我的小说
 * @param data
 * @returns void
 */
export function mynovalUpdate(data: MynovalForm) {
  return requestClient.putWithMsg<void>('/system/mynoval', data);
}

/**
 * 删除我的小说
 * @param id id
 * @returns void
 */
export function mynovalRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/system/mynoval/${id}`);
}
