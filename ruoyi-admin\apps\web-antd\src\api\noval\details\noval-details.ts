import type { DetailsVO } from './noval-details-model';

import type { ID, IDS, PageQuery, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  novalDetailsExport = '/noval/details/export',
  novalDetailsList = '/noval/details/list',
  novalDetailsRefreshCache = '/noval/details/refreshCache',
  novalDetailsSelectList = '/noval/details/optionselect',
  root = '/noval/details',
}

/**
 * 获取字典类型列表
 * @param params 请求参数
 * @returns list
 */
export function novalDetailsList(params?: PageQuery) {
  return requestClient.get<PageResult<DetailsVO>>(Api.novalDetailsList, {
    params,
  });
}

/**
 * 导出字典类型列表
 * @param data 表单参数
 * @returns blob
 */
export function novalDetailsExport(data: Partial<DetailsVO>) {
  return commonExport(Api.novalDetailsExport, data);
}

/**
 * 删除字典类型
 * @param dictIds 字典类型id数组
 * @returns void
 */
export function novalDetailsRemove(dictIds: IDS) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${dictIds}`);
}

/**
 * 刷新字典缓存
 * @returns void
 */
export function novalDetailsTypeCache() {
  return requestClient.deleteWithMsg<void>(Api.novalDetailsRefreshCache);
}

/**
 * 新增
 * @param data 表单参数
 * @returns void
 */
export function novalDetailsAdd(data: Partial<DetailsVO>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改
 * @param data 表单参数
 * @returns void
 */
export function novalDetailsUpdate(data: Partial<DetailsVO>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 查询详情
 * @param dictId 字典类型id
 * @returns 信息
 */
export function novalDetailsInfo(id: ID) {
  return requestClient.get<DetailsVO>(`${Api.root}/${id}`);
}

/**
 * 这个在ele用到 v5用不上
 * 下拉框  返回值和list一样
 * @returns options
 */
export function novalDetailsSelectList() {
  return requestClient.get<DetailsVO[]>(Api.novalDetailsSelectList);
}
