import type { ExegesisVO, ExegesisForm, ExegesisQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询批注列表
* @param params
* @returns 批注列表
*/
export function exegesisList(params?: ExegesisQuery) {
  return requestClient.get<PageResult<ExegesisVO>>('/noval/exegesis/list', { params });
}

/**
 * 导出批注列表
 * @param params
 * @returns 批注列表
 */
export function exegesisExport(params?: ExegesisQuery) {
  return commonExport('/noval/exegesis/export', params ?? {});
}

/**
 * 查询批注详情
 * @param id id
 * @returns 批注详情
 */
export function exegesisInfo(id: ID) {
  return requestClient.get<ExegesisVO>(`/noval/exegesis/${id}`);
}

/**
 * 新增批注
 * @param data
 * @returns void
 */
export function exegesisAdd(data: ExegesisForm) {
  return requestClient.postWithMsg<void>('/noval/exegesis', data);
}

/**
 * 更新批注
 * @param data
 * @returns void
 */
export function exegesisUpdate(data: ExegesisForm) {
  return requestClient.putWithMsg<void>('/noval/exegesis', data);
}

/**
 * 删除批注
 * @param id id
 * @returns void
 */
export function exegesisRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/exegesis/${id}`);
}
