// 共享的notice数据存储
export let noticeData = [
  {
    noticeId: 1,
    noticeTitle: '系统维护通知',
    noticeType: '1', // 通知
    noticeContent: '系统将于2024年2月1日凌晨2点至4点进行例行维护，期间系统将不可用。',
    status: '0', // 正常状态
    remark: '系统维护',
    createBy: 'admin',
    createByName: '管理员',
    createTime: '2024-01-20 10:00:00',
  },
  {
    noticeId: 2,
    noticeTitle: '新功能上线公告',
    noticeType: '2', // 公告
    noticeContent: '我们很高兴地宣布，新的小说推荐功能已经上线，欢迎大家体验！',
    status: '0', // 正常状态
    remark: '功能更新',
    createBy: 'admin',
    createByName: '管理员',
    createTime: '2024-01-22 14:30:00',
  },
  {
    noticeId: 3,
    noticeTitle: '用户行为规范通知',
    noticeType: '1', // 通知
    noticeContent: '请所有用户遵守社区规则，文明发言，共同维护良好的社区环境。',
    status: '0', // 正常状态
    remark: '行为规范',
    createBy: 'admin',
    createByName: '管理员',
    createTime: '2024-01-25 09:15:00',
  },
];

// 获取下一个通知ID
export function getNextNoticeId(): number {
  return Math.max(...noticeData.map(item => Number(item.noticeId))) + 1;
}

// 添加通知
export function addNotice(notice: any): void {
  noticeData.push(notice);
}

// 更新通知
export function updateNotice(noticeId: number, updates: any): boolean {
  const index = noticeData.findIndex(item => Number(item.noticeId) === noticeId);
  if (index === -1) {
    return false;
  }
  noticeData[index] = { ...noticeData[index], ...updates };
  return true;
}

// 删除通知
export function deleteNotice(noticeId: number): boolean {
  const index = noticeData.findIndex(item => Number(item.noticeId) === noticeId);
  if (index === -1) {
    return false;
  }
  noticeData.splice(index, 1);
  return true;
}

// 删除多个通知
export function deleteNoticeMultiple(ids: number[]): boolean {
  let success = true;
  for (const id of ids) {
    if (!deleteNotice(id)) {
      success = false;
    }
  }
  return success;
}

// 查找通知
export function findNotice(noticeId: number): any | undefined {
  return noticeData.find(item => Number(item.noticeId) === noticeId);
}

// 根据通知类型查找通知
export function findNoticeByType(noticeType: string): any[] {
  return noticeData.filter(item => item.noticeType === noticeType);
}
