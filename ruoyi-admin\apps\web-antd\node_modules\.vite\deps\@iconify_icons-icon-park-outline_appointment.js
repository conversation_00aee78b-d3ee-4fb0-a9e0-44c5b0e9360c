import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@iconify+icons-icon-park-outline@1.2.11/node_modules/@iconify/icons-icon-park-outline/appointment.js
var require_appointment = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-icon-park-outline@1.2.11/node_modules/@iconify/icons-icon-park-outline/appointment.js"(exports) {
    var data = {
      "width": 48,
      "height": 48,
      "body": '<g fill="none" stroke="currentColor" stroke-width="4"><circle cx="24" cy="11" r="7" stroke-linecap="round" stroke-linejoin="round"/><path stroke-linecap="round" stroke-linejoin="round" d="M4 41c0-8.837 8.059-16 18-16"/><circle cx="34" cy="34" r="9"/><path stroke-linecap="round" stroke-linejoin="round" d="M33 31v4h4"/></g>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_appointment();
//# sourceMappingURL=@iconify_icons-icon-park-outline_appointment.js.map
