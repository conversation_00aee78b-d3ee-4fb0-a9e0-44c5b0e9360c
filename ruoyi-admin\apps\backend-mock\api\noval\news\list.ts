import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { newsData } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    pageNum = 1,
    pageSize = 20,
    id,
    otherId,
    MyId,
    news,
    type,
    isread,
  } = getQuery(event);

  let listData = structuredClone(newsData);

  // 根据消息ID筛选
  if (id) {
    listData = listData.filter((item) =>
      String(item.id).includes(String(id)),
    );
  }

  // 根据消息发起人id筛选
  if (otherId) {
    listData = listData.filter((item) =>
      String(item.otherId).includes(String(otherId)),
    );
  }

  // 根据消息接收者id筛选
  if (MyId) {
    listData = listData.filter((item) =>
      String(item.MyId).includes(String(MyId)),
    );
  }

  // 根据消息内容筛选
  if (news) {
    listData = listData.filter((item) =>
      item.news.toLowerCase().includes(String(news).toLowerCase()),
    );
  }

  // 根据消息类型筛选
  if (type !== undefined && type !== '') {
    listData = listData.filter((item) => String(item.type) === String(type));
  }

  // 根据是否已读筛选
  if (isread !== undefined && isread !== '') {
    listData = listData.filter((item) => String(item.isread) === String(isread));
  }

  // 按创建时间倒序排列
  listData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());

  const total = listData.length;
  const startIndex = (Number(pageNum) - 1) * Number(pageSize);
  const endIndex = startIndex + Number(pageSize);
  const paginatedData = listData.slice(startIndex, endIndex);

  return useResponseSuccess({
    rows: paginatedData,
    total,
  });
});
