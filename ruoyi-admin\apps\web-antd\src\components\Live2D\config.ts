// Live2D 配置文件

export const LIVE2D_CONFIG = {
  // 基础设置
  width: 300,
  height: 400,
  scale: 0.3,
  position: { x: 150, y: 200 },
  
  // 功能开关
  autoPlay: true,
  showToolbar: true,
  enableInteraction: true,
  enableMessages: true,
  
  // 模型列表
  models: [
    {
      id: 'mayahakune',
      name: '真夜白音',
      path: '/live2d/真夜白音/真夜白音.model3.json',
      preview: '/live2d/真夜白音/preview.png'
    },
    {
      id: 'ariu',
      name: '<PERSON><PERSON>',
      path: '/live2d/ariu/ariu.model3.json',
      preview: '/live2d/ariu/preview.png'
    },
    // 可以添加更多模型
    {
      id: 'shizuku',
      name: '<PERSON><PERSON><PERSON> (远程)',
      path: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/shizuku/shizuku.model.json',
      preview: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/shizuku/preview.png'
    }
  ],
  
  // 默认模型
  defaultModel: 'mayahakune',
  
  // 消息配置
  messages: {
    // 欢迎消息
    welcome: [
      '你好呀！我是你的专属看板娘~',
      '欢迎来到这里！',
      '今天也要加油哦！'
    ],
    
    // 点击消息
    click: {
      head: [
        '不要摸我的头啦！',
        '呀！好痒~',
        '你想干什么呢？'
      ],
      body: [
        '呀！好痒~',
        '不要乱摸啦！',
        '讨厌~'
      ]
    },
    
    // 随机消息
    random: [
      '今天天气不错呢！',
      '要不要一起学习呢？',
      '记得多喝水哦~',
      '工作累了就休息一下吧！',
      '你今天也很棒呢！',
      '要保持好心情哦~',
      '记得按时吃饭！',
      '适当运动对身体好~'
    ],
    
    // 时间相关消息
    time: {
      morning: [
        '早上好！新的一天开始了~',
        '早安！今天也要元气满满！',
        '美好的一天从现在开始！'
      ],
      noon: [
        '中午了，记得吃午饭哦~',
        '午休时间到了！',
        '中午好！'
      ],
      afternoon: [
        '下午好！工作辛苦了~',
        '下午茶时间到了！',
        '继续加油！'
      ],
      evening: [
        '晚上好！今天辛苦了~',
        '该休息了哦~',
        '晚安！做个好梦~'
      ]
    },
    
    // 系统消息
    system: {
      modelLoaded: '模型加载成功！',
      modelLoadFailed: '模型加载失败，请检查模型文件',
      modelSwitched: '换了新造型，怎么样？',
      textureChanged: '换了新衣服！',
      screenshotSaved: '截图保存成功！',
      screenshotFailed: '截图失败了~',
      hidden: '我先隐藏一下~'
    }
  },
  
  // 动画配置
  animations: {
    idle: 'idle',
    tapHead: 'tap_head',
    tapBody: 'tap_body',
    greeting: 'greeting'
  },
  
  // 响应式配置
  responsive: {
    mobile: {
      width: 250,
      height: 320,
      scale: 0.25,
      position: { x: 10, y: 10 }
    },
    tablet: {
      width: 280,
      height: 360,
      scale: 0.28,
      position: { x: 15, y: 15 }
    }
  }
}

// 获取随机消息
export const getRandomMessage = (category: string, subcategory?: string): string => {
  const messages = LIVE2D_CONFIG.messages as any
  
  if (subcategory) {
    const subMessages = messages[category]?.[subcategory]
    if (Array.isArray(subMessages) && subMessages.length > 0) {
      return subMessages[Math.floor(Math.random() * subMessages.length)]
    }
  }
  
  const categoryMessages = messages[category]
  if (Array.isArray(categoryMessages) && categoryMessages.length > 0) {
    return categoryMessages[Math.floor(Math.random() * categoryMessages.length)]
  }
  
  return '...'
}

// 根据时间获取消息
export const getTimeBasedMessage = (): string => {
  const hour = new Date().getHours()
  
  if (hour >= 6 && hour < 12) {
    return getRandomMessage('time', 'morning')
  } else if (hour >= 12 && hour < 14) {
    return getRandomMessage('time', 'noon')
  } else if (hour >= 14 && hour < 18) {
    return getRandomMessage('time', 'afternoon')
  } else {
    return getRandomMessage('time', 'evening')
  }
}

// 检测设备类型
export const getDeviceConfig = () => {
  const width = window.innerWidth
  
  if (width <= 768) {
    return LIVE2D_CONFIG.responsive.mobile
  } else if (width <= 1024) {
    return LIVE2D_CONFIG.responsive.tablet
  } else {
    return {
      width: LIVE2D_CONFIG.width,
      height: LIVE2D_CONFIG.height,
      scale: LIVE2D_CONFIG.scale,
      position: LIVE2D_CONFIG.position
    }
  }
}
