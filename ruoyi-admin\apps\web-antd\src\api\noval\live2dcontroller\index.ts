import type { Live2dcontrollerVO, Live2dcontrollerForm, Live2dcontrollerQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询用户个性live2d控制列表
* @param params
* @returns 用户个性live2d控制列表
*/
export function live2dcontrollerList(params?: Live2dcontrollerQuery) {
  return requestClient.get<PageResult<Live2dcontrollerVO>>('/noval/live2dcontroller/list', { params });
}

/**
 * 导出用户个性live2d控制列表
 * @param params
 * @returns 用户个性live2d控制列表
 */
export function live2dcontrollerExport(params?: Live2dcontrollerQuery) {
  return commonExport('/noval/live2dcontroller/export', params ?? {});
}

/**
 * 查询用户个性live2d控制详情
 * @param id id
 * @returns 用户个性live2d控制详情
 */
export function live2dcontrollerInfo(id: ID) {
  return requestClient.get<Live2dcontrollerVO>(`/noval/live2dcontroller/${id}`);
}

/**
 * 新增用户个性live2d控制
 * @param data
 * @returns void
 */
export function live2dcontrollerAdd(data: Live2dcontrollerForm) {
  return requestClient.postWithMsg<void>('/noval/live2dcontroller', data);
}

/**
 * 更新用户个性live2d控制
 * @param data
 * @returns void
 */
export function live2dcontrollerUpdate(data: Live2dcontrollerForm) {
  return requestClient.putWithMsg<void>('/noval/live2dcontroller', data);
}

/**
 * 删除用户个性live2d控制
 * @param id id
 * @returns void
 */
export function live2dcontrollerRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/live2dcontroller/${id}`);
}


