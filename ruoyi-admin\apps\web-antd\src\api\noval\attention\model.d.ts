import type { PageQuery, BaseEntity } from '#/api/common';

export interface AttentionVO {
  /**
   * 我的id
   */
  id: string | number;

  /**
   * 我关注的id
   */
  otherId: string | number;

  /**
   * 关注时间
   */
  createTime: string;

}

export interface AttentionForm extends BaseEntity {
  /**
   * 我的id
   */
  id?: string | number;

  /**
   * 我关注的id
   */
  otherId?: string | number;

    /**
   * 关注时间
   */
  createTime?: string;

}

export interface AttentionQuery extends PageQuery {
  /**
   * 我的id
   */
  id?: string | number;

  /**
   * 我关注的id
   */
  otherId?: string | number;

    /**
   * 关注时间
   */
  createTime?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
