{"models": [{"id": "a<PERSON>u", "name": "<PERSON><PERSON>", "path": "/live2d/ariu/ariu.model3.json", "preview": "/live2d/ariu/preview.png"}, {"id": "shi<PERSON>ku", "name": "<PERSON><PERSON><PERSON>", "path": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/shizuku/shizuku.model.json", "preview": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/shizuku/preview.png"}, {"id": "haru", "name": "<PERSON><PERSON>", "path": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/haru/haru_greeter_t03.model3.json", "preview": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/haru/preview.png"}, {"id": "mao", "name": "<PERSON>", "path": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/tororo/tororo.model.json", "preview": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/tororo/preview.png"}], "messages": {"welcome": ["你好呀！我是你的专属看板娘~", "欢迎来到这里！", "今天也要加油哦！"], "click": {"head": ["不要摸我的头啦！", "呀！好痒~", "你想干什么呢？"], "body": ["呀！好痒~", "不要乱摸啦！", "讨厌~"]}, "random": ["今天天气不错呢！", "要不要一起学习呢？", "记得多喝水哦~", "工作累了就休息一下吧！", "你今天也很棒呢！", "要保持好心情哦~", "记得按时吃饭！", "适当运动对身体好~"]}, "animations": {"idle": "idle", "tapHead": "tap_head", "tapBody": "tap_body", "greeting": "greeting"}}