import type { CommentVO, CommentForm, CommentQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询评论列表
* @param params
* @returns 评论列表
*/
export function commentList(params?: CommentQuery) {
  console.log('commentList API调用开始，请求URL: /noval/comment/list');
  console.log('commentList 请求参数:', params);
  console.log('commentList 请求参数JSON:', JSON.stringify(params, null, 2));

  return requestClient.get<PageResult<CommentVO>>('/noval/comment/list', { params })
    .then(data => {
      console.log('commentList API调用成功');
      console.log('commentList 响应数据:', data);
      console.log('commentList 响应数据类型:', typeof data);
      console.log('commentList 响应完整JSON:', JSON.stringify(data, null, 2));
      console.log('commentList rows数据:', data.rows);
      console.log('commentList rows长度:', data.rows?.length);
      return data;
    })
    .catch(error => {
      console.error('commentList API调用失败');
      console.error('请求URL: /noval/comment/list');
      console.error('请求参数:', params);
      console.error('错误详情:', error);
      console.error('错误响应状态:', error.response?.status);
      console.error('错误响应数据:', error.response?.data);
      console.error('错误响应头:', error.response?.headers);
      throw error;
    });
}

/**
 * 导出评论列表
 * @param params
 * @returns 评论列表
 */
export function commentExport(params?: CommentQuery) {
  return commonExport('/noval/comment/export', params ?? {});
}

/**
 * 查询评论详情
 * @param commerntId id
 * @returns 评论详情数组
 */
export function commentInfo(commerntId: ID) {
  const id = String(commerntId); // 显式转换为字符串
    console.log('commentInfo API调用开始，请求URL:', `/noval/comment/${id}`);
  console.log('commentInfo API调用参数:', id, '参数类型:', typeof id);

  return requestClient.get<CommentVO[]>(`/noval/comment/${id}`, {
    isReturnNativeResponse: true // 返回原生响应对象，包含headers、status等信息
  }).then(response => {
    console.log('commentInfo API调用成功');
    console.log('commentInfo 原始响应对象:', response);
    console.log('commentInfo 响应状态:', response.status);
    console.log('commentInfo 响应头:', response.headers);
    console.log('commentInfo 响应数据:', response.data);
    console.log('commentInfo response完整JSON:', JSON.stringify(response.data, null, 2));

    // 从响应对象中提取数据
    const data = response.data;
    // 确保返回的是数组类型
    if (!Array.isArray(data)) {
      console.warn('commentInfo: 后端返回的不是数组，已转换为数组格式');
      return data ? [data] : [];
    }
    return data;
  }).catch(error => {
    console.error('commentInfo API调用失败');
        console.error('请求URL:', `/noval/comment/${id}`);
    console.error('请求参数:', commerntId);
    console.error('错误详情:', error);
    console.error('错误响应状态:', error.response?.status);
    console.error('错误响应数据:', error.response?.data);
    console.error('错误响应头:', error.response?.headers);
    throw error;
  });
}

/**
 * 获取单个评论详情（兼容旧代码，实际返回数组中的第一个元素）
 * @param commerntId id
 * @returns 单个评论详情
 * @deprecated 请使用 commentInfo 函数，并从返回的数组中获取所需元素
 */
export function getCommentDetail(commerntId: ID) {
  return commentInfo(commerntId).then(data => {
    return data.length > 0 ? data[0] : null;
  });
}

/**
 * 新增评论
 * @param data
 * @returns void
 */
export function commentAdd(data: CommentForm) {
  return requestClient.postWithMsg<void>('/noval/comment', data);
}

/**
 * 更新评论
 * @param data
 * @returns void
 */
export function commentUpdate(data: CommentForm) {
  return requestClient.putWithMsg<void>('/noval/comment', data);
}

/**
 * 删除评论
 * @param commerntId id
 * @returns void
 */
export function commentRemove(commerntId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/comment/${commerntId}`);
}

/**
 * 增加评论浏览量
 * @param commentData 评论数据（包含更新后的look值）
 * @returns void
 */
export function commentIncreaseLook(commentData: CommentForm) {
  return requestClient.putWithMsg<void>('/noval/comment', commentData);
}
