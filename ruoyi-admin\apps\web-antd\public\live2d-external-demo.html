<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>外链页面Live2D模型演示</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #fff;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-radius: 5px;
    }
    h1 {
      margin: 0;
      color: #333;
      font-size: 24px;
    }
    .content {
      background-color: #fff;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .chapter-title {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    .chapter-content {
      font-size: 16px;
      line-height: 1.8;
    }
    .chapter-content p {
      margin-bottom: 20px;
      text-indent: 2em;
    }
    .controls {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
    }
    .btn {
      display: inline-block;
      padding: 8px 16px;
      background-color: #0078d7;
      color: white;
      border-radius: 4px;
      text-decoration: none;
      border: none;
      cursor: pointer;
    }
    .btn:hover {
      background-color: #005a9e;
    }
    .btn-secondary {
      background-color: #6c757d;
    }
    .btn-secondary:hover {
      background-color: #5a6268;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>小说阅读页面</h1>
    </header>

    <div class="content">
      <div class="chapter-title">
        第1章 序章
      </div>

      <div class="chapter-content">
        <p>这是一个外链页面的示例，用于演示在外部网站中嵌入Live2D模型。</p>
        <p>当您从小说详情页面点击"阅读"按钮跳转到外链页面时，Live2D模型将自动加载并显示在页面右下角。</p>
        <p>您可以与Live2D模型进行交互，例如点击模型、显示消息、切换模型等。</p>
        <p>这个示例页面模拟了一个小说阅读页面，您可以在这里测试Live2D模型的功能。</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
      </div>

      <div class="controls">
        <button class="btn btn-secondary">上一章</button>
        <button class="btn">下一章</button>
      </div>
    </div>
  </div>

  <!-- 加载Live2D模型 -->
  <script>
    // 模拟从localStorage获取Live2D配置
    const live2dConfig = {
      modelId: 'haru',
      width: 280,
      height: 300,
      right: 0,
      bottom: 0,
      zIndex: 1000,
      showToolbar: true,
      bookName: '测试小说',
      chapterName: '第1章 序章'
    };

    // 保存配置到localStorage
    localStorage.setItem('live2dConfig', JSON.stringify(live2dConfig));

    // 设置来源标记和时间戳
    sessionStorage.setItem('from_novel_app', 'true');
    sessionStorage.setItem('novel_link_timestamp', Date.now().toString());

    // 加载Live2D注入脚本
    const script = document.createElement('script');
    script.src = '/live2d-inject.js';
    document.body.appendChild(script);

    // 测试Live2D API
    window.addEventListener('message', function(event) {
      if (event.data && event.data.type === 'live2d-loaded') {
        console.log('Live2D模型已加载:', event.data.modelName);

        // Live2D模型加载后会自动显示包含书名和章节名的欢迎消息
        // 这里可以添加额外的测试代码
        setTimeout(() => {
          if (window.Live2DExternal) {
            window.Live2DExternal.showMessage('你可以点击我切换模型哦！', 5000);
          }
        }, 6000); // 在自动欢迎消息显示后再显示这条消息
      }
    });
  </script>
</body>
</html>
