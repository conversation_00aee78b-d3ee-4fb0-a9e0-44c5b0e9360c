// 共享的news数据存储
export let newsData = [
  {
    id: 1, // id（后端随机生成）
    otherId: 1, // 消息发起人id (管理员)
    MyId: 1, // 消息接收者id (用户1)
    news: '欢迎使用我们的小说论坛！请遵守社区规则，文明发言。',
    type: 0, // 管理员信息
    isread: 0, // 未读
    createTime: '2024-01-15 10:30:00',
  },
  {
    id: 2, // id（后端随机生成）
    otherId: 2, // 消息发起人id (用户2)
    MyId: 1, // 消息接收者id (用户1)
    news: '您的帖子《关于玄幻小说的讨论》收到了新的回复',
    type: 1, // 论坛信息
    isread: 0, // 未读
    createTime: '2024-01-15 14:20:00',
  },
  {
    id: 3, // id（后端随机生成）
    otherId: 3, // 消息发起人id (用户3)
    MyId: 1, // 消息接收者id (用户1)
    news: '你好，我想和你讨论一下最近看的小说',
    type: 2, // 私聊信息
    isread: 0, // 未读
    createTime: '2024-01-15 16:45:00',
  },
  {
    id: 4, // id（后端随机生成）
    otherId: 1, // 消息发起人id (管理员)
    MyId: 1, // 消息接收者id (用户1)
    news: '系统将于今晚22:00-24:00进行维护，期间可能影响正常使用。',
    type: 0, // 管理员信息
    isread: 1, // 已读
    createTime: '2024-01-16 09:00:00',
  },
  {
    id: 5, // id（后端随机生成）
    otherId: 4, // 消息发起人id (用户4)
    MyId: 1, // 消息接收者id (用户1)
    news: '有人点赞了您的评论',
    type: 3, // 点赞消息
    isread: 0, // 未读
    createTime: '2024-01-16 11:30:00',
  },
  {
    id: 6, // id（后端随机生成）
    otherId: 5, // 消息发起人id (用户5)
    MyId: 1, // 消息接收者id (用户1)
    news: '感谢您的推荐，那本小说真的很不错！',
    type: 2, // 私聊信息
    isread: 0, // 未读
    createTime: '2024-01-16 15:20:00',
  },
  {
    id: 7, // id（后端随机生成）
    otherId: 2, // 消息发起人id (用户2)
    MyId: 1, // 消息接收者id (用户1)
    news: '最近有什么好看的小说推荐吗？',
    type: 2, // 私聊信息
    isread: 0, // 未读
    createTime: '2024-01-17 09:15:00',
  },
  {
    id: 8, // id（后端随机生成）
    otherId: 6, // 消息发起人id (用户6)
    MyId: 1, // 消息接收者id (用户1)
    news: '你好，我看到你的书评写得很好，想和你交流一下',
    type: 2, // 私聊信息
    isread: 0, // 未读
    createTime: '2024-01-17 14:30:00',
  },
];

// 获取下一个ID
export function getNextNewsId(): number {
  return Math.max(...newsData.map(item => Number(item.id))) + 1;
}

// 添加消息
export function addNews(news: any): void {
  newsData.push(news);
}

// 更新消息
export function updateNews(id: number, updates: any): boolean {
  const index = newsData.findIndex(item => Number(item.id) === id);
  if (index === -1) {
    return false;
  }
  newsData[index] = { ...newsData[index], ...updates };
  return true;
}

// 删除消息
export function deleteNews(id: number): boolean {
  const index = newsData.findIndex(item => Number(item.id) === id);
  if (index === -1) {
    return false;
  }
  newsData.splice(index, 1);
  return true;
}

// 删除多个消息
export function deleteNewsMultiple(ids: number[]): number {
  let deletedCount = 0;
  ids.forEach(id => {
    if (deleteNews(id)) {
      deletedCount++;
    }
  });
  return deletedCount;
}

// 查找消息
export function findNews(id: number): any | undefined {
  return newsData.find(item => Number(item.id) === id);
}

// 根据接收者ID查找消息
export function findNewsByReceiver(MyId: number | string): any[] {
  return newsData.filter(item => Number(item.MyId) === Number(MyId));
}

// 根据发送者ID查找消息
export function findNewsBySender(otherId: number | string): any[] {
  return newsData.filter(item => Number(item.otherId) === Number(otherId));
}

// 根据类型查找消息
export function findNewsByType(type: number | string): any[] {
  return newsData.filter(item => Number(item.type) === Number(type));
}
