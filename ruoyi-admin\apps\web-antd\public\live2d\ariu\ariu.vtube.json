{"Version": 1, "Name": "a<PERSON>u", "ModelID": "894faaae94c84413b557726a43b23af0", "FileReferences": {"Icon": "", "Model": "ariu.model3.json", "IdleAnimation": "", "IdleAnimationWhenTrackingLost": ""}, "ModelSaveMetadata": {"LastSavedVTubeStudioVersion": "1.23.5", "LastSavedPlatform": "Steam", "LastSavedDateUTC": "Sunday, 25 December 2022, 14:07:16", "LastSavedDateLocalTime": "Sunday, 25 December 2022, 22:07:16", "LastSavedDateUnixMillisecondTimestamp": "1671977236967"}, "SavedModelPosition": {"Position": {"x": 6.604533672332764, "y": -66.7232894897461, "z": 0.0}, "Rotation": {"x": 0.0, "y": 0.0, "z": 2.839905164364609e-06, "w": 1.0}, "Scale": {"x": 1.1690603494644165, "y": 1.1690603494644165, "z": 1.0}}, "ModelPositionMovement": {"Use": false, "X": 6, "Y": 8, "Z": 11, "SmoothingX": 10, "SmoothingY": 10, "SmoothingZ": 10}, "ItemSettings": {"OnlyMoveWhenPinned": false, "AllowNormalHotkeyTriggers": true, "Multiplier_HeadAngleX": 1.0, "Multiplier_HeadAngleY": 1.0, "Multiplier_HeadAngleZ": 1.0, "Shift_HeadAngleX": 0.0, "Shift_HeadAngleY": 0.0, "Smoothing_HeadAngleX": 15.0, "Smoothing_HeadAngleY": 15.0, "Smoothing_HeadAngleZ": 15.0}, "PhysicsSettings": {"Use": true, "UseLegacyPhysics": false, "Live2DPhysicsFPS": 3, "PhysicsStrength": 58, "WindStrength": 0, "DraggingPhysicsStrength": 0}, "GeneralSettings": {"TimeUntilTrackingLostIdleAnimation": 0.0, "WorkshopSharingForbidden": true, "EnableExpressionSaving": false}, "ParameterSettings": [{"Folder": "", "Name": "Eye Open Left", "Input": "EyeOpenLeft", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.899999976158142, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeLOpen", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Eye Open Right", "Input": "EyeOpenRight", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.899999976158142, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeROpen", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Eye X", "Input": "EyeRightX", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": 1.0, "OutputRangeUpper": -1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeBallX", "Smoothing": 8, "Minimized": false}, {"Folder": "", "Name": "Eye Y", "Input": "EyeRightY", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeBallY", "Smoothing": 8, "Minimized": false}, {"Folder": "", "Name": "Mouth Smile", "Input": "MouthSmile", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.2000000476837158, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamMouthForm", "Smoothing": 0, "Minimized": false}, {"Folder": "", "Name": "Mouth Open", "Input": "MouthOpen", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.2000000476837158, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamMouthOpenY", "Smoothing": 0, "Minimized": false}, {"Folder": "", "Name": "Brow Height Left", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLY", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Brow Height Right", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowRY", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Brow Form Left", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLForm", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Brow Form Right", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowRForm", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Face Left/Right Rotation", "Input": "FaceAngleX", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamAngleX", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Face Up/Down Rotation", "Input": "FaceAngleY", "InputRangeLower": -20.0, "InputRangeUpper": 20.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamAngleY", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Face Lean Rotation", "Input": "FaceAngleZ", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamAngleZ", "Smoothing": 30, "Minimized": false}, {"Folder": "", "Name": "Body Rotation X", "Input": "FaceAngleX", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleX", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "Body Rotation Y", "Input": "FaceAngleY", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleY", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "Body Rotation Z", "Input": "FaceAngleZ", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleZ", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "Auto Breath", "Input": "", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": true, "OutputLive2D": "ParamBreath", "Smoothing": 0, "Minimized": false}], "Hotkeys": [{"HotkeyID": "ef69f6e43e224f868954aa1209985997", "Name": "jk包", "Action": "ToggleExpression", "File": "jk包.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N1", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "8ed42641e03b4d62b02b32b0eec22052", "Name": "圈圈眼", "Action": "ToggleExpression", "File": "圈圈眼.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N2", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "eac9073e1b394a5dab93ff80c95128e3", "Name": "戴帽子", "Action": "ToggleExpression", "File": "戴帽子.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N3", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "959722d723b84bea93eab66b46cafeab", "Name": "手柄", "Action": "ToggleExpression", "File": "手柄.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N4", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "c34df958c8b347a1869d07ed19ef9f45", "Name": "爱心眼", "Action": "ToggleExpression", "File": "爱心眼.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N5", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "92450f6c8e9545d3b6c4a1998d4cb386", "Name": "脱外套", "Action": "ToggleExpression", "File": "脱外套.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N6", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "c2d45269c0d5467f881d8e4d0a6e6520", "Name": "裙子", "Action": "ToggleExpression", "File": "裙子.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N7", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "6ec1db4fa7b64cfaa7bb7b62bdcc3a07", "Name": "马尾L隐藏", "Action": "ToggleExpression", "File": "马尾L隐藏.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N8", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "fbae2db3baa74074bf8e61b1278ff0bb", "Name": "马尾R隐藏", "Action": "ToggleExpression", "File": "马尾R隐藏.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "N9", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "02ec5ef3c7804fdf9959c800bffd1794", "Name": "黑化", "Action": "ToggleExpression", "File": "黑化.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "LeftControl", "Trigger2": "Q", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "HotkeySettings": {"UseOnScreenHotkeys": false, "UseKeyboardHotkeys": true, "SendOnScreenHotkeysToPC": true, "OnScreenHotkeyAlpha": 75}, "ArtMeshDetails": {"ArtMeshesExcludedFromPinning": [], "ArtMeshesThatDeleteItemsOnDrop": [], "ArtMeshSceneLightingMultipliers": [], "ArtMeshMultiplyAndScreenColors": []}, "PhysicsCustomizationSettings": {"PhysicsMultipliersPerPhysicsGroup": [], "WindMultipliersPerPhysicsGroup": [], "DraggingPhysicsMultipliersPerPhysicsGroup": []}, "FolderInfo": {"HotkeyFolders": [], "ConfigItemFolders": []}, "SavedActiveExpressions": []}