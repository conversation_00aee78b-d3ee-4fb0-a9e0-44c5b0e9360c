import {
  errLog,
  getSlotVNs,
  warnLog
} from "./chunk-X4FLHRLX.js";
import {
  eqEmptyValue,
  formatText,
  getFuncText,
  isEnableConf
} from "./chunk-EKP7TM2V.js";
import {
  VxeUI,
  require_xe_utils
} from "./chunk-BCXSQRR2.js";
import {
  h,
  reactive,
  watch
} from "./chunk-GE6DY3YU.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/ui/src/dom.js
var import_xe_utils = __toESM(require_xe_utils());
var reClsMap = {};
var browse = import_xe_utils.default.browse();
var tpImgEl;
function initTpImg() {
  if (!tpImgEl) {
    tpImgEl = new Image();
    tpImgEl.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
  }
  return tpImgEl;
}
function getTpImg() {
  if (!tpImgEl) {
    return initTpImg();
  }
  return tpImgEl;
}
function getPropClass(property, params) {
  return property ? import_xe_utils.default.isFunction(property) ? property(params) : property : "";
}
function getClsRE(cls) {
  if (!reClsMap[cls]) {
    reClsMap[cls] = new RegExp(`(?:^|\\s)${cls}(?!\\S)`, "g");
  }
  return reClsMap[cls];
}
function getNodeOffset(elem, container, rest) {
  if (elem) {
    const parentElem = elem.parentNode;
    rest.top += elem.offsetTop;
    rest.left += elem.offsetLeft;
    if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {
      rest.top -= parentElem.scrollTop;
      rest.left -= parentElem.scrollLeft;
    }
    if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {
      return getNodeOffset(elem.offsetParent, container, rest);
    }
  }
  return rest;
}
function isPx(val) {
  return val && /^\d+(px)?$/.test(val);
}
function isScale(val) {
  return val && /^\d+%$/.test(val);
}
function hasClass(elem, cls) {
  return elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls));
}
function removeClass(elem, cls) {
  if (elem && hasClass(elem, cls)) {
    elem.className = elem.className.replace(getClsRE(cls), "");
  }
}
function addClass(elem, cls) {
  if (elem && !hasClass(elem, cls)) {
    removeClass(elem, cls);
    elem.className = `${elem.className} ${cls}`;
  }
}
function getDomNode() {
  const documentElement = document.documentElement;
  const bodyElem = document.body;
  return {
    scrollTop: documentElement.scrollTop || bodyElem.scrollTop,
    scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,
    visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,
    visibleWidth: documentElement.clientWidth || bodyElem.clientWidth
  };
}
function getOffsetHeight(elem) {
  return elem ? elem.offsetHeight : 0;
}
function getPaddingTopBottomSize(elem) {
  if (elem) {
    const computedStyle = getComputedStyle(elem);
    const paddingTop = import_xe_utils.default.toNumber(computedStyle.paddingTop);
    const paddingBottom = import_xe_utils.default.toNumber(computedStyle.paddingBottom);
    return paddingTop + paddingBottom;
  }
  return 0;
}
function setScrollTop(elem, scrollTop) {
  if (elem) {
    elem.scrollTop = scrollTop;
  }
}
function setScrollLeft(elem, scrollLeft) {
  if (elem) {
    elem.scrollLeft = scrollLeft;
  }
}
function updateCellTitle(overflowElem, column) {
  const content = column.type === "html" ? overflowElem.innerText : overflowElem.textContent;
  if (overflowElem.getAttribute("title") !== content) {
    overflowElem.setAttribute("title", content);
  }
}
function getEventTargetNode(evnt, container, queryCls, queryMethod) {
  let targetElem;
  let target = evnt.target.shadowRoot && evnt.composed ? evnt.composedPath()[0] || evnt.target : evnt.target;
  while (target && target.nodeType && target !== document) {
    if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {
      targetElem = target;
    } else if (target === container) {
      return { flag: queryCls ? !!targetElem : true, container, targetElem };
    }
    target = target.parentNode;
  }
  return { flag: false };
}
function getOffsetPos(elem, container) {
  return getNodeOffset(elem, container, { left: 0, top: 0 });
}
function getAbsolutePos(elem) {
  const bounding = elem.getBoundingClientRect();
  const boundingTop = bounding.top;
  const boundingLeft = bounding.left;
  const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();
  return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };
}
var scrollIntoViewIfNeeded = "scrollIntoViewIfNeeded";
var scrollIntoView = "scrollIntoView";
function scrollToView(elem) {
  if (elem) {
    if (elem[scrollIntoViewIfNeeded]) {
      elem[scrollIntoViewIfNeeded]();
    } else if (elem[scrollIntoView]) {
      elem[scrollIntoView]();
    }
  }
}
function triggerEvent(targetElem, type) {
  if (targetElem) {
    targetElem.dispatchEvent(new Event(type));
  }
}
function isNodeElement(elem) {
  return elem && elem.nodeType === 1;
}

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/util.js
var import_xe_utils3 = __toESM(require_xe_utils());

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/columnInfo.js
var import_xe_utils2 = __toESM(require_xe_utils());
var { getI18n, formats } = VxeUI;
var ColumnInfo = class {
  /* eslint-disable @typescript-eslint/no-use-before-define */
  constructor($xeTable, _vm, { renderHeader, renderCell, renderFooter, renderData } = {}) {
    const $xeGrid = $xeTable.xegrid;
    const formatter = _vm.formatter;
    const visible = import_xe_utils2.default.isBoolean(_vm.visible) ? _vm.visible : true;
    const { props: tableProps } = $xeTable;
    if (true) {
      const types = ["seq", "checkbox", "radio", "expand", "html"];
      if (_vm.type && types.indexOf(_vm.type) === -1) {
        warnLog("vxe.error.errProp", [`type=${_vm.type}`, types.join(", ")]);
      }
      if (import_xe_utils2.default.isBoolean(_vm.cellRender) || _vm.cellRender && !import_xe_utils2.default.isObject(_vm.cellRender)) {
        warnLog("vxe.error.errProp", [`column.cell-render=${_vm.cellRender}`, "column.cell-render={}"]);
      }
      if (import_xe_utils2.default.isBoolean(_vm.editRender) || _vm.editRender && !import_xe_utils2.default.isObject(_vm.editRender)) {
        warnLog("vxe.error.errProp", [`column.edit-render=${_vm.editRender}`, "column.edit-render={}"]);
      }
      if (_vm.cellRender && _vm.editRender) {
        warnLog("vxe.error.errConflicts", ["column.cell-render", "column.edit-render"]);
      }
      if (_vm.type === "expand") {
        const { treeConfig } = tableProps;
        const { computeTreeOpts } = $xeTable.getComputeMaps();
        const treeOpts = computeTreeOpts.value;
        if (treeConfig && (treeOpts.showLine || treeOpts.line)) {
          errLog("vxe.error.errConflicts", ["tree-config.showLine", "column.type=expand"]);
        }
      }
      if (formatter) {
        if (import_xe_utils2.default.isString(formatter)) {
          const gFormatOpts = formats.get(formatter) || import_xe_utils2.default[formatter];
          if (!gFormatOpts || !import_xe_utils2.default.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {
            errLog("vxe.error.notFormats", [formatter]);
          }
        } else if (import_xe_utils2.default.isArray(formatter)) {
          const gFormatOpts = formats.get(formatter[0]) || import_xe_utils2.default[formatter[0]];
          if (!gFormatOpts || !import_xe_utils2.default.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {
            errLog("vxe.error.notFormats", [formatter[0]]);
          }
        }
      }
    }
    Object.assign(this, {
      // 基本属性
      type: _vm.type,
      property: _vm.field,
      field: _vm.field,
      title: _vm.title,
      width: _vm.width,
      minWidth: _vm.minWidth,
      maxWidth: _vm.maxWidth,
      resizable: _vm.resizable,
      fixed: _vm.fixed,
      align: _vm.align,
      headerAlign: _vm.headerAlign,
      footerAlign: _vm.footerAlign,
      showOverflow: _vm.showOverflow,
      showHeaderOverflow: _vm.showHeaderOverflow,
      showFooterOverflow: _vm.showFooterOverflow,
      className: _vm.className,
      headerClassName: _vm.headerClassName,
      footerClassName: _vm.footerClassName,
      formatter,
      footerFormatter: _vm.footerFormatter,
      sortable: _vm.sortable,
      sortBy: _vm.sortBy,
      sortType: _vm.sortType,
      filters: toFilters(_vm.filters),
      filterMultiple: import_xe_utils2.default.isBoolean(_vm.filterMultiple) ? _vm.filterMultiple : true,
      filterMethod: _vm.filterMethod,
      filterResetMethod: _vm.filterResetMethod,
      filterRecoverMethod: _vm.filterRecoverMethod,
      filterRender: _vm.filterRender,
      treeNode: _vm.treeNode,
      dragSort: _vm.dragSort,
      cellType: _vm.cellType,
      cellRender: _vm.cellRender,
      editRender: _vm.editRender,
      contentRender: _vm.contentRender,
      headerExportMethod: _vm.headerExportMethod,
      exportMethod: _vm.exportMethod,
      footerExportMethod: _vm.footerExportMethod,
      titleHelp: _vm.titleHelp,
      titlePrefix: _vm.titlePrefix,
      titleSuffix: _vm.titleSuffix,
      // 自定义参数
      params: _vm.params,
      // 渲染属性
      id: _vm.colId || import_xe_utils2.default.uniqueId("col_"),
      parentId: null,
      visible,
      // 内部属性（一旦被使用，将导致不可升级版本）
      halfVisible: false,
      defaultVisible: visible,
      defaultFixed: _vm.fixed,
      checked: false,
      halfChecked: false,
      disabled: false,
      // 分组层级
      level: 1,
      // 跨行
      rowSpan: 1,
      // 跨列
      colSpan: 1,
      // 数据排序
      order: null,
      sortTime: 0,
      // 列排序
      sortNumber: 0,
      renderSortNumber: 0,
      renderFixed: "",
      renderVisible: false,
      renderWidth: 0,
      renderHeight: 0,
      renderResizeWidth: 0,
      renderAutoWidth: 0,
      resizeWidth: 0,
      renderLeft: 0,
      renderArgs: [],
      model: {},
      renderHeader: renderHeader || _vm.renderHeader,
      renderCell: renderCell || _vm.renderCell,
      renderFooter: renderFooter || _vm.renderFooter,
      renderData,
      // 单元格插槽，只对 grid 有效
      slots: _vm.slots
    });
    if ($xeGrid) {
      const { computeProxyOpts } = $xeGrid.getComputeMaps();
      const proxyOpts = computeProxyOpts.value;
      if (proxyOpts.beforeColumn) {
        proxyOpts.beforeColumn({ $grid: $xeGrid, column: this });
      }
    }
  }
  getTitle() {
    return getFuncText(this.title || (this.type === "seq" ? getI18n("vxe.table.seqTitle") : ""));
  }
  getKey() {
    const { type } = this;
    return this.field || (type ? `type=${type}` : null);
  }
  update(name, value) {
    if (name !== "filters") {
      if (name === "field") {
        this.property = value;
      }
      this[name] = value;
    }
  }
};

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/util.js
var getAllConvertColumns = (columns, parentColumn) => {
  const result = [];
  columns.forEach((column) => {
    column.parentId = parentColumn ? parentColumn.id : null;
    if (column.visible) {
      if (column.children && column.children.length && column.children.some((column2) => column2.visible)) {
        result.push(column);
        result.push(...getAllConvertColumns(column.children, column));
      } else {
        result.push(column);
      }
    }
  });
  return result;
};
var convertHeaderColumnToRows = (originColumns) => {
  let maxLevel = 1;
  const traverse = (column, parent) => {
    if (parent) {
      column.level = parent.level + 1;
      if (maxLevel < column.level) {
        maxLevel = column.level;
      }
    }
    if (column.children && column.children.length && column.children.some((column2) => column2.visible)) {
      let colSpan = 0;
      column.children.forEach((subColumn) => {
        if (subColumn.visible) {
          traverse(subColumn, column);
          colSpan += subColumn.colSpan;
        }
      });
      column.colSpan = colSpan;
    } else {
      column.colSpan = 1;
    }
  };
  originColumns.forEach((column) => {
    column.level = 1;
    traverse(column);
  });
  const rows = [];
  for (let i = 0; i < maxLevel; i++) {
    rows.push([]);
  }
  const allColumns = getAllConvertColumns(originColumns);
  allColumns.forEach((column) => {
    if (column.children && column.children.length && column.children.some((column2) => column2.visible)) {
      column.rowSpan = 1;
    } else {
      column.rowSpan = maxLevel - column.level + 1;
    }
    rows[column.level - 1].push(column);
  });
  return rows;
};
function restoreScrollLocation($xeTable, scrollLeft, scrollTop) {
  const internalData = $xeTable.internalData;
  return $xeTable.clearScroll().then(() => {
    if (scrollLeft || scrollTop) {
      internalData.lastScrollLeft = 0;
      internalData.lastScrollTop = 0;
      internalData.inVirtualScroll = false;
      internalData.inBodyScroll = false;
      internalData.inFooterScroll = false;
      internalData.bodyScrollType = "";
      return $xeTable.scrollTo(scrollLeft, scrollTop);
    }
  });
}
function getRowUniqueId() {
  return import_xe_utils3.default.uniqueId("row_");
}
function getRowkey($xeTable) {
  const { props } = $xeTable;
  const { computeRowOpts } = $xeTable.getComputeMaps();
  const { rowId } = props;
  const rowOpts = computeRowOpts.value;
  return rowId || rowOpts.keyField || "_X_ROW_KEY";
}
function getRowid($xeTable, row) {
  const rowid = import_xe_utils3.default.get(row, getRowkey($xeTable));
  return import_xe_utils3.default.eqNull(rowid) ? "" : encodeURIComponent(rowid);
}
var handleFieldOrColumn = ($xeTable, fieldOrColumn) => {
  if (fieldOrColumn) {
    return import_xe_utils3.default.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;
  }
  return null;
};
function getPaddingLeftRightSize(elem) {
  if (elem) {
    const computedStyle = getComputedStyle(elem);
    const paddingLeft = import_xe_utils3.default.toNumber(computedStyle.paddingLeft);
    const paddingRight = import_xe_utils3.default.toNumber(computedStyle.paddingRight);
    return paddingLeft + paddingRight;
  }
  return 0;
}
function getElementMarginWidth(elem) {
  if (elem) {
    const computedStyle = getComputedStyle(elem);
    const marginLeft = import_xe_utils3.default.toNumber(computedStyle.marginLeft);
    const marginRight = import_xe_utils3.default.toNumber(computedStyle.marginRight);
    return elem.offsetWidth + marginLeft + marginRight;
  }
  return 0;
}
function queryCellElement(cell, selector) {
  return cell.querySelector(".vxe-cell" + selector);
}
function toFilters(filters) {
  if (filters && import_xe_utils3.default.isArray(filters)) {
    return filters.map(({ label, value, data, resetValue, checked }) => {
      return { label, value, data, resetValue, checked: !!checked, _checked: !!checked };
    });
  }
  return filters;
}
function toTreePathSeq(path) {
  return path.map((num, i) => i % 2 === 0 ? Number(num) + 1 : ".").join("");
}
function getCellValue(row, column) {
  return import_xe_utils3.default.get(row, column.field);
}
function setCellValue(row, column, value) {
  return import_xe_utils3.default.set(row, column.field, value);
}
function getRefElem(refEl) {
  if (refEl) {
    const rest = refEl.value;
    if (rest) {
      return rest.$el || rest;
    }
  }
  return null;
}
function getColReMinWidth(params) {
  const { $table, column, cell } = params;
  const { props: tableProps } = $table;
  const { computeResizableOpts } = $table.getComputeMaps();
  const resizableOpts = computeResizableOpts.value;
  const { minWidth: reMinWidth } = resizableOpts;
  if (reMinWidth) {
    const customMinWidth = import_xe_utils3.default.isFunction(reMinWidth) ? reMinWidth(params) : reMinWidth;
    if (customMinWidth !== "auto") {
      return Math.max(1, import_xe_utils3.default.toNumber(customMinWidth));
    }
  }
  const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;
  const { showHeaderOverflow, minWidth: colMinWidth } = column;
  const headOverflow = import_xe_utils3.default.isUndefined(showHeaderOverflow) || import_xe_utils3.default.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;
  const showEllipsis = headOverflow === "ellipsis";
  const showTitle = headOverflow === "title";
  const showTooltip = headOverflow === true || headOverflow === "tooltip";
  const hasEllipsis = showTitle || showTooltip || showEllipsis;
  const minTitleWidth = import_xe_utils3.default.floor((import_xe_utils3.default.toNumber(getComputedStyle(cell).fontSize) || 14) * 1.6);
  const paddingLeftRight = getPaddingLeftRightSize(cell) + getPaddingLeftRightSize(queryCellElement(cell, ""));
  let mWidth = minTitleWidth + paddingLeftRight;
  if (hasEllipsis) {
    const dragIconWidth = getPaddingLeftRightSize(queryCellElement(cell, ">.vxe-cell--drag-handle"));
    const checkboxIconWidth = getPaddingLeftRightSize(queryCellElement(cell, ">.vxe-cell--checkbox"));
    const requiredIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--required-icon"));
    const editIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--edit-icon"));
    const prefixIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell-title-prefix-icon"));
    const suffixIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell-title-suffix-icon"));
    const sortIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--sort"));
    const filterIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--filter"));
    mWidth += dragIconWidth + checkboxIconWidth + requiredIconWidth + editIconWidth + prefixIconWidth + suffixIconWidth + filterIconWidth + sortIconWidth;
  }
  if (colMinWidth) {
    const { refTableBody } = $table.getRefMaps();
    const tableBody = refTableBody.value;
    const bodyElem = tableBody ? tableBody.$el : null;
    if (bodyElem) {
      if (isScale(colMinWidth)) {
        const bodyWidth = bodyElem.clientWidth - 1;
        const meanWidth = bodyWidth / 100;
        return Math.max(mWidth, Math.floor(import_xe_utils3.default.toInteger(colMinWidth) * meanWidth));
      } else if (isPx(colMinWidth)) {
        return Math.max(mWidth, import_xe_utils3.default.toInteger(colMinWidth));
      }
    }
  }
  return mWidth;
}
function isColumnInfo(column) {
  return column && (column.constructor === ColumnInfo || column instanceof ColumnInfo);
}
function createColumn($xeTable, options, renderOptions) {
  return isColumnInfo(options) ? options : reactive(new ColumnInfo($xeTable, options, renderOptions));
}
function watchColumn($xeTable, props, column) {
  Object.keys(props).forEach((name) => {
    watch(() => props[name], (value) => {
      column.update(name, value);
      if ($xeTable) {
        if (name === "filters") {
          $xeTable.setFilter(column, value);
          $xeTable.handleUpdateDataQueue();
        } else if (["visible", "fixed", "width", "minWidth", "maxWidth"].includes(name)) {
          $xeTable.handleRefreshColumnQueue();
        }
      }
    });
  });
}
function assembleColumn($xeTable, elem, column, colgroup) {
  const { reactData } = $xeTable;
  const { staticColumns } = reactData;
  const parentElem = elem.parentNode;
  const parentColumn = colgroup ? colgroup.columnConfig : null;
  const parentCols = parentColumn ? parentColumn.children : staticColumns;
  if (parentElem && parentCols) {
    parentCols.splice(import_xe_utils3.default.arrayIndexOf(parentElem.children, elem), 0, column);
    reactData.staticColumns = staticColumns.slice(0);
  }
}
function destroyColumn($xeTable, column) {
  const { reactData } = $xeTable;
  const { staticColumns } = reactData;
  const matchObj = import_xe_utils3.default.findTree(staticColumns, (item) => item.id === column.id, { children: "children" });
  if (matchObj) {
    matchObj.items.splice(matchObj.index, 1);
  }
  reactData.staticColumns = staticColumns.slice(0);
}
function getRootColumn($xeTable, column) {
  const { internalData } = $xeTable;
  const { fullColumnIdData } = internalData;
  if (!column) {
    return null;
  }
  let parentColId = column.parentId;
  while (fullColumnIdData[parentColId]) {
    const column2 = fullColumnIdData[parentColId].column;
    parentColId = column2.parentId;
    if (!parentColId) {
      return column2;
    }
  }
  return column;
}
function mergeBodyMethod(mergeList, _rowIndex, _columnIndex) {
  for (let mIndex = 0; mIndex < mergeList.length; mIndex++) {
    const { row: mergeRowIndex, col: mergeColIndex, rowspan: mergeRowspan, colspan: mergeColspan } = mergeList[mIndex];
    if (mergeColIndex > -1 && mergeRowIndex > -1 && mergeRowspan && mergeColspan) {
      if (mergeRowIndex === _rowIndex && mergeColIndex === _columnIndex) {
        return { rowspan: mergeRowspan, colspan: mergeColspan };
      }
      if (_rowIndex >= mergeRowIndex && _rowIndex < mergeRowIndex + mergeRowspan && _columnIndex >= mergeColIndex && _columnIndex < mergeColIndex + mergeColspan) {
        return { rowspan: 0, colspan: 0 };
      }
    }
  }
}
function clearTableDefaultStatus($xeTable) {
  const { props, internalData } = $xeTable;
  internalData.initStatus = false;
  $xeTable.clearSort();
  $xeTable.clearCurrentRow();
  $xeTable.clearCurrentColumn();
  $xeTable.clearRadioRow();
  $xeTable.clearRadioReserve();
  $xeTable.clearCheckboxRow();
  $xeTable.clearCheckboxReserve();
  $xeTable.clearRowExpand();
  $xeTable.clearTreeExpand();
  $xeTable.clearTreeExpandReserve();
  $xeTable.clearPendingRow();
  if ($xeTable.clearFilter) {
    $xeTable.clearFilter();
  }
  if ($xeTable.clearSelected && (props.keyboardConfig || props.mouseConfig)) {
    $xeTable.clearSelected();
  }
  if ($xeTable.clearCellAreas && props.mouseConfig) {
    $xeTable.clearCellAreas();
    $xeTable.clearCopyCellArea();
  }
  return $xeTable.clearScroll();
}
function clearTableAllStatus($xeTable) {
  if ($xeTable.clearFilter) {
    $xeTable.clearFilter();
  }
  return clearTableDefaultStatus($xeTable);
}
function rowToVisible($xeTable, row) {
  const { reactData, internalData } = $xeTable;
  const tableProps = $xeTable.props;
  const { showOverflow } = tableProps;
  const { refTableBody } = $xeTable.getRefMaps();
  const { columnStore, scrollYLoad } = reactData;
  const { afterFullData, scrollYStore, fullAllDataRowIdData } = internalData;
  const tableBody = refTableBody.value;
  const { leftList, rightList } = columnStore;
  const bodyElem = tableBody ? tableBody.$el : null;
  const rowid = getRowid($xeTable, row);
  let offsetFixedLeft = 0;
  leftList.forEach((item) => {
    offsetFixedLeft += item.renderWidth;
  });
  let offsetFixedRight = 0;
  rightList.forEach((item) => {
    offsetFixedRight += item.renderWidth;
  });
  if (bodyElem) {
    const bodyHeight = bodyElem.clientHeight;
    const bodyScrollTop = bodyElem.scrollTop;
    const trElem = bodyElem.querySelector(`[rowid="${rowid}"]`);
    if (trElem) {
      const trOffsetParent = trElem.offsetParent;
      const trOffsetTop = trElem.offsetTop + (trOffsetParent ? trOffsetParent.offsetTop : 0);
      const trHeight = trElem.clientHeight;
      if (trOffsetTop < bodyScrollTop || trOffsetTop > bodyScrollTop + bodyHeight) {
        return $xeTable.scrollTo(null, trOffsetTop);
      } else if (trOffsetTop + trHeight >= bodyHeight + bodyScrollTop) {
        return $xeTable.scrollTo(null, bodyScrollTop + trHeight);
      }
    } else {
      if (scrollYLoad) {
        if (showOverflow) {
          return $xeTable.scrollTo(null, ($xeTable.findRowIndexOf(afterFullData, row) - 1) * scrollYStore.rowHeight);
        }
        let scrollTop = 0;
        const rest = fullAllDataRowIdData[rowid];
        const rHeight = rest ? rest.height : 0;
        for (let i = 0; i < afterFullData.length; i++) {
          const currRow = afterFullData[i];
          const currRowid = getRowid($xeTable, currRow);
          if (currRow === row || currRowid === rowid) {
            break;
          }
          const rest2 = fullAllDataRowIdData[currRowid];
          scrollTop += rest2 ? rest2.height : 0;
        }
        if (scrollTop < bodyScrollTop) {
          return $xeTable.scrollTo(null, scrollTop - offsetFixedLeft - 1);
        }
        return $xeTable.scrollTo(null, scrollTop + rHeight - (bodyHeight - offsetFixedRight - 1));
      }
    }
  }
  return Promise.resolve();
}
function colToVisible($xeTable, column, row) {
  const { reactData, internalData } = $xeTable;
  const { refTableBody } = $xeTable.getRefMaps();
  const { columnStore, scrollXLoad } = reactData;
  const { visibleColumn } = internalData;
  const { leftList, rightList } = columnStore;
  const tableBody = refTableBody.value;
  const bodyElem = tableBody ? tableBody.$el : null;
  if (column.fixed) {
    return Promise.resolve();
  }
  let offsetFixedLeft = 0;
  leftList.forEach((item) => {
    offsetFixedLeft += item.renderWidth;
  });
  let offsetFixedRight = 0;
  rightList.forEach((item) => {
    offsetFixedRight += item.renderWidth;
  });
  if (bodyElem) {
    const bodyWidth = bodyElem.clientWidth;
    const bodyScrollLeft = bodyElem.scrollLeft;
    let tdElem = null;
    if (row) {
      const rowid = getRowid($xeTable, row);
      tdElem = bodyElem.querySelector(`[rowid="${rowid}"] .${column.id}`);
    }
    if (!tdElem) {
      tdElem = bodyElem.querySelector(`.${column.id}`);
    }
    if (tdElem) {
      const tdOffsetParent = tdElem.offsetParent;
      const tdOffsetLeft = tdElem.offsetLeft + (tdOffsetParent ? tdOffsetParent.offsetLeft : 0);
      const cellWidth = tdElem.clientWidth;
      if (tdOffsetLeft < bodyScrollLeft + offsetFixedLeft) {
        return $xeTable.scrollTo(tdOffsetLeft - offsetFixedLeft - 1);
      } else if (tdOffsetLeft + cellWidth - bodyScrollLeft > bodyWidth - offsetFixedRight) {
        return $xeTable.scrollTo(tdOffsetLeft + cellWidth - (bodyWidth - offsetFixedRight - 1));
      }
    } else {
      if (scrollXLoad) {
        let scrollLeft = 0;
        const cellWidth = column.renderWidth;
        for (let i = 0; i < visibleColumn.length; i++) {
          const currCol = visibleColumn[i];
          if (currCol === column || currCol.id === column.id) {
            break;
          }
          scrollLeft += currCol.renderWidth;
        }
        if (scrollLeft < bodyScrollLeft) {
          return $xeTable.scrollTo(scrollLeft - offsetFixedLeft - 1);
        }
        return $xeTable.scrollTo(scrollLeft + cellWidth - (bodyWidth - offsetFixedRight - 1));
      }
    }
  }
  return Promise.resolve();
}

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/cell.js
var import_xe_utils4 = __toESM(require_xe_utils());
var { getI18n: getI18n2, getIcon, renderer, formats: formats2, renderEmptyElement } = VxeUI;
function renderTitlePrefixIcon(params) {
  const { $table, column } = params;
  const titlePrefix = column.titlePrefix || column.titleHelp;
  if (titlePrefix) {
    return h("i", {
      class: ["vxe-cell-title-prefix-icon", titlePrefix.icon || getIcon().TABLE_TITLE_PREFIX],
      onMouseenter(evnt) {
        $table.triggerHeaderTitleEvent(evnt, titlePrefix, params);
      },
      onMouseleave(evnt) {
        $table.handleTargetLeaveEvent(evnt);
      }
    });
  }
  return renderEmptyElement($table);
}
function renderTitleSuffixIcon(params) {
  const { $table, column } = params;
  const titleSuffix = column.titleSuffix;
  if (titleSuffix) {
    return h("i", {
      class: ["vxe-cell-title-suffix-icon", titleSuffix.icon || getIcon().TABLE_TITLE_SUFFIX],
      onMouseenter(evnt) {
        $table.triggerHeaderTitleEvent(evnt, titleSuffix, params);
      },
      onMouseleave(evnt) {
        $table.handleTargetLeaveEvent(evnt);
      }
    });
  }
  return renderEmptyElement($table);
}
function renderCellDragIcon(params) {
  const { $table } = params;
  const tableProps = $table.props;
  const { dragConfig } = tableProps;
  const { computeRowDragOpts } = $table.getComputeMaps();
  const rowDragOpts = computeRowDragOpts.value;
  const { icon, trigger, disabledMethod } = rowDragOpts;
  const rDisabledMethod = disabledMethod || (dragConfig ? dragConfig.rowDisabledMethod : null);
  const isDisabled = rDisabledMethod && rDisabledMethod(params);
  const ons = {};
  if (trigger !== "cell") {
    ons.onMousedown = (evnt) => {
      if (!isDisabled) {
        $table.handleCellDragMousedownEvent(evnt, params);
      }
    };
    ons.onMouseup = $table.handleCellDragMouseupEvent;
  }
  return h("span", Object.assign({ key: "dg", class: ["vxe-cell--drag-handle", {
    "is--disabled": isDisabled
  }] }, ons), [
    h("i", {
      class: icon || (dragConfig ? dragConfig.rowIcon : "") || getIcon().TABLE_DRAG_ROW
    })
  ]);
}
function renderCellBaseVNs(params, content) {
  const { $table, column, level } = params;
  const { dragSort } = column;
  const tableProps = $table.props;
  const { treeConfig, dragConfig } = tableProps;
  const { computeRowOpts, computeRowDragOpts } = $table.getComputeMaps();
  const rowOpts = computeRowOpts.value;
  const rowDragOpts = computeRowDragOpts.value;
  const { showIcon, isPeerDrag, isCrossDrag, visibleMethod } = rowDragOpts;
  const rVisibleMethod = visibleMethod || (dragConfig ? dragConfig.rowVisibleMethod : null);
  const vns = import_xe_utils4.default.isArray(content) ? content : [content];
  if (dragSort && rowOpts.drag && ((showIcon || (dragConfig ? dragConfig.showRowIcon : false)) && (!rVisibleMethod || rVisibleMethod(params)))) {
    if (treeConfig) {
      if (isPeerDrag || isCrossDrag || !level) {
        vns.unshift(renderCellDragIcon(params));
      }
    } else {
      vns.unshift(renderCellDragIcon(params));
    }
  }
  return vns;
}
function renderHeaderCellDragIcon(params) {
  const { $table, column } = params;
  const { computeColumnOpts, computeColumnDragOpts } = $table.getComputeMaps();
  const columnOpts = computeColumnOpts.value;
  const columnDragOpts = computeColumnDragOpts.value;
  const { showIcon, icon, trigger, isPeerDrag, isCrossDrag, visibleMethod, disabledMethod } = columnDragOpts;
  if (columnOpts.drag && showIcon && (!visibleMethod || visibleMethod(params))) {
    if (!column.fixed && (isPeerDrag || isCrossDrag || !column.parentId)) {
      const isDisabled = disabledMethod && disabledMethod(params);
      const ons = {};
      if (trigger !== "cell") {
        ons.onMousedown = (evnt) => {
          if (!isDisabled) {
            $table.handleHeaderCellDragMousedownEvent(evnt, params);
          }
        };
        ons.onMouseup = $table.handleHeaderCellDragMouseupEvent;
      }
      return h("span", Object.assign({ key: "dg", class: ["vxe-cell--drag-handle", {
        "is--disabled": isDisabled
      }] }, ons), [
        h("i", {
          class: icon || getIcon().TABLE_DRAG_COLUMN
        })
      ]);
    }
  }
  return renderEmptyElement($table);
}
function renderHeaderCellBaseVNs(params, content) {
  const vns = [
    renderTitlePrefixIcon(params),
    renderHeaderCellDragIcon(params),
    ...import_xe_utils4.default.isArray(content) ? content : [content],
    renderTitleSuffixIcon(params)
  ];
  return vns;
}
function renderTitleContent(params, content) {
  const { $table, column } = params;
  const tableProps = $table.props;
  const tableReactData = $table.reactData;
  const { computeTooltipOpts } = $table.getComputeMaps();
  const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;
  const { type, showHeaderOverflow } = column;
  const tooltipOpts = computeTooltipOpts.value;
  const showAllTip = tooltipOpts.showAll;
  const headOverflow = import_xe_utils4.default.isUndefined(showHeaderOverflow) || import_xe_utils4.default.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;
  const showTitle = headOverflow === "title";
  const showTooltip = headOverflow === true || headOverflow === "tooltip";
  const ons = {};
  if (showTitle || showTooltip || showAllTip) {
    ons.onMouseenter = (evnt) => {
      if (tableReactData._isResize) {
        return;
      }
      if (showTitle) {
        updateCellTitle(evnt.currentTarget, column);
      } else if (showTooltip || showAllTip) {
        $table.triggerHeaderTooltipEvent(evnt, params);
      }
    };
  }
  if (showTooltip || showAllTip) {
    ons.onMouseleave = (evnt) => {
      if (tableReactData._isResize) {
        return;
      }
      if (showTooltip || showAllTip) {
        $table.handleTargetLeaveEvent(evnt);
      }
    };
  }
  return [
    type === "html" && import_xe_utils4.default.isString(content) ? h("span", Object.assign({ class: "vxe-cell--title", innerHTML: content }, ons)) : h("span", Object.assign({ class: "vxe-cell--title" }, ons), getSlotVNs(content))
  ];
}
function formatFooterLabel(footerFormatter, params) {
  if (import_xe_utils4.default.isFunction(footerFormatter)) {
    return `${footerFormatter(params)}`;
  }
  const isArr = import_xe_utils4.default.isArray(footerFormatter);
  const gFormatOpts = isArr ? formats2.get(footerFormatter[0]) : formats2.get(footerFormatter);
  const footerFormatMethod = gFormatOpts ? gFormatOpts.tableFooterCellFormatMethod : null;
  if (footerFormatMethod) {
    return `${isArr ? footerFormatMethod(params, ...footerFormatter.slice(1)) : footerFormatMethod(params)}`;
  }
  return "";
}
function getFooterContent(params) {
  const { $table, column, _columnIndex, items, row } = params;
  const { slots, editRender, cellRender, footerFormatter } = column;
  const renderOpts = editRender || cellRender;
  const footerSlot = slots ? slots.footer : null;
  if (footerSlot) {
    return $table.callSlot(footerSlot, params);
  }
  if (renderOpts) {
    const compConf = renderer.get(renderOpts.name);
    if (compConf) {
      const rtFooter = compConf.renderTableFooter || compConf.renderFooter;
      if (rtFooter) {
        return getSlotVNs(rtFooter(renderOpts, params));
      }
    }
  }
  let itemValue = "";
  if (import_xe_utils4.default.isArray(items)) {
    itemValue = items[_columnIndex];
    return [
      footerFormatter ? formatFooterLabel(footerFormatter, {
        itemValue,
        column,
        row,
        items,
        _columnIndex
      }) : formatText(itemValue, 1)
    ];
  }
  itemValue = import_xe_utils4.default.get(row, column.field);
  return [
    footerFormatter ? formatFooterLabel(footerFormatter, {
      itemValue,
      column,
      row,
      items,
      _columnIndex
    }) : formatText(itemValue, 1)
  ];
}
function getDefaultCellLabel(params) {
  const { $table, row, column } = params;
  return formatText($table.getCellLabel(row, column), 1);
}
var Cell = {
  createColumn($xeTable, columnOpts) {
    const { type, sortable, filters, editRender, treeNode } = columnOpts;
    const { props } = $xeTable;
    const { editConfig } = props;
    const { computeEditOpts, computeCheckboxOpts } = $xeTable.getComputeMaps();
    const checkboxOpts = computeCheckboxOpts.value;
    const editOpts = computeEditOpts.value;
    const renConfs = {
      renderHeader: Cell.renderDefaultHeader,
      renderCell: treeNode ? Cell.renderTreeCell : Cell.renderDefaultCell,
      renderFooter: Cell.renderDefaultFooter
    };
    switch (type) {
      case "seq":
        renConfs.renderHeader = Cell.renderSeqHeader;
        renConfs.renderCell = treeNode ? Cell.renderTreeIndexCell : Cell.renderSeqCell;
        break;
      case "radio":
        renConfs.renderHeader = Cell.renderRadioHeader;
        renConfs.renderCell = treeNode ? Cell.renderTreeRadioCell : Cell.renderRadioCell;
        break;
      case "checkbox":
        renConfs.renderHeader = Cell.renderCheckboxHeader;
        renConfs.renderCell = checkboxOpts.checkField ? treeNode ? Cell.renderTreeSelectionCellByProp : Cell.renderCheckboxCellByProp : treeNode ? Cell.renderTreeSelectionCell : Cell.renderCheckboxCell;
        break;
      case "expand":
        renConfs.renderCell = Cell.renderExpandCell;
        renConfs.renderData = Cell.renderExpandData;
        break;
      case "html":
        renConfs.renderCell = treeNode ? Cell.renderTreeHTMLCell : Cell.renderHTMLCell;
        if (filters && sortable) {
          renConfs.renderHeader = Cell.renderSortAndFilterHeader;
        } else if (sortable) {
          renConfs.renderHeader = Cell.renderSortHeader;
        } else if (filters) {
          renConfs.renderHeader = Cell.renderFilterHeader;
        }
        break;
      default:
        if (editConfig && editRender) {
          renConfs.renderHeader = Cell.renderEditHeader;
          renConfs.renderCell = editOpts.mode === "cell" ? treeNode ? Cell.renderTreeCellEdit : Cell.renderCellEdit : treeNode ? Cell.renderTreeRowEdit : Cell.renderRowEdit;
        } else if (filters && sortable) {
          renConfs.renderHeader = Cell.renderSortAndFilterHeader;
        } else if (sortable) {
          renConfs.renderHeader = Cell.renderSortHeader;
        } else if (filters) {
          renConfs.renderHeader = Cell.renderFilterHeader;
        }
    }
    return createColumn($xeTable, columnOpts, renConfs);
  },
  /**
   * 列头标题
   */
  renderHeaderTitle(params) {
    const { $table, column } = params;
    const { slots, editRender, cellRender } = column;
    const renderOpts = editRender || cellRender;
    const headerSlot = slots ? slots.header : null;
    if (headerSlot) {
      return renderTitleContent(params, $table.callSlot(headerSlot, params));
    }
    if (renderOpts) {
      const compConf = renderer.get(renderOpts.name);
      if (compConf) {
        const rtHeader = compConf.renderTableHeader || compConf.renderHeader;
        if (rtHeader) {
          return renderTitleContent(params, getSlotVNs(rtHeader(renderOpts, params)));
        }
      }
    }
    return renderTitleContent(params, formatText(column.getTitle(), 1));
  },
  renderDefaultHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params));
  },
  renderDefaultCell(params) {
    const { $table, row, column } = params;
    const { slots, editRender, cellRender } = column;
    const renderOpts = editRender || cellRender;
    const defaultSlot = slots ? slots.default : null;
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));
    }
    if (renderOpts) {
      const compConf = renderer.get(renderOpts.name);
      if (compConf) {
        const rtCell = compConf.renderTableCell || compConf.renderCell;
        const rtDefault = compConf.renderTableDefault || compConf.renderDefault;
        const renderFn = editRender ? rtCell : rtDefault;
        if (renderFn) {
          return renderCellBaseVNs(params, getSlotVNs(renderFn(renderOpts, Object.assign({ $type: editRender ? "edit" : "cell" }, params))));
        }
      }
    }
    const cellValue = $table.getCellLabel(row, column);
    const cellPlaceholder = editRender ? editRender.placeholder : "";
    return renderCellBaseVNs(params, [
      h("span", {
        class: "vxe-cell--label"
      }, [
        // 如果设置占位符
        editRender && eqEmptyValue(cellValue) ? h("span", {
          class: "vxe-cell--placeholder"
        }, formatText(getFuncText(cellPlaceholder), 1)) : h("span", formatText(cellValue, 1))
      ])
    ]);
  },
  renderTreeCell(params) {
    return Cell.renderTreeIcon(params, Cell.renderDefaultCell(params));
  },
  renderDefaultFooter(params) {
    return [
      h("span", {
        class: "vxe-cell--item"
      }, getFooterContent(params))
    ];
  },
  /**
   * 树节点
   */
  renderTreeIcon(params, cellVNodes) {
    const { $table, isHidden } = params;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { computeTreeOpts } = $table.getComputeMaps();
    const { treeExpandedMaps, treeExpandLazyLoadedMaps } = tableReactData;
    const { fullAllDataRowIdData } = tableInternalData;
    const treeOpts = computeTreeOpts.value;
    const { row, column, level } = params;
    const { slots } = column;
    const { indent, lazy, trigger, iconLoaded, showIcon, iconOpen, iconClose } = treeOpts;
    const childrenField = treeOpts.children || treeOpts.childrenField;
    const hasChildField = treeOpts.hasChild || treeOpts.hasChildField;
    const rowChilds = row[childrenField];
    const hasChild = rowChilds && rowChilds.length;
    const iconSlot = slots ? slots.icon : null;
    let hasLazyChilds = false;
    let isActive = false;
    let isLazyLoading = false;
    let isLazyLoaded = false;
    const ons = {};
    if (iconSlot) {
      return $table.callSlot(iconSlot, params);
    }
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isActive = !!treeExpandedMaps[rowid];
      if (lazy) {
        const rest = fullAllDataRowIdData[rowid];
        isLazyLoading = !!treeExpandLazyLoadedMaps[rowid];
        hasLazyChilds = row[hasChildField];
        isLazyLoaded = !!rest.treeLoaded;
      }
    }
    if (!trigger || trigger === "default") {
      ons.onClick = (evnt) => {
        $table.triggerTreeExpandEvent(evnt, params);
      };
    }
    return [
      h("div", {
        class: ["vxe-cell--tree-node", {
          "is--active": isActive
        }],
        style: {
          paddingLeft: `${level * indent}px`
        }
      }, [
        showIcon && (lazy ? isLazyLoaded ? hasChild : hasLazyChilds : hasChild) ? [
          h("div", Object.assign({ class: "vxe-tree--btn-wrapper" }, ons), [
            h("i", {
              class: ["vxe-tree--node-btn", isLazyLoading ? iconLoaded || getIcon().TABLE_TREE_LOADED : isActive ? iconOpen || getIcon().TABLE_TREE_OPEN : iconClose || getIcon().TABLE_TREE_CLOSE]
            })
          ])
        ] : null,
        h("div", {
          class: "vxe-tree-cell"
        }, cellVNodes)
      ])
    ];
  },
  /**
   * 序号
   */
  renderSeqHeader(params) {
    const { $table, column } = params;
    const { slots } = column;
    const headerSlot = slots ? slots.header : null;
    return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot ? $table.callSlot(headerSlot, params) : formatText(column.getTitle(), 1)));
  },
  renderSeqCell(params) {
    const { $table, column } = params;
    const tableProps = $table.props;
    const { treeConfig } = tableProps;
    const { computeSeqOpts } = $table.getComputeMaps();
    const seqOpts = computeSeqOpts.value;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));
    }
    const { seq } = params;
    const seqMethod = seqOpts.seqMethod;
    return renderCellBaseVNs(params, [
      h("span", `${formatText(seqMethod ? seqMethod(params) : treeConfig ? seq : (seqOpts.startIndex || 0) + seq, 1)}`)
    ]);
  },
  renderTreeIndexCell(params) {
    return Cell.renderTreeIcon(params, Cell.renderSeqCell(params));
  },
  /**
   * 单选
   */
  renderRadioHeader(params) {
    const { $table, column } = params;
    const { slots } = column;
    const headerSlot = slots ? slots.header : null;
    const titleSlot = slots ? slots.title : null;
    return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot ? $table.callSlot(headerSlot, params) : [
      h("span", {
        class: "vxe-radio--label"
      }, titleSlot ? $table.callSlot(titleSlot, params) : formatText(column.getTitle(), 1))
    ]));
  },
  renderRadioCell(params) {
    const { $table, column, isHidden } = params;
    const tableReactData = $table.reactData;
    const { computeRadioOpts } = $table.getComputeMaps();
    const { selectRadioRow } = tableReactData;
    const radioOpts = computeRadioOpts.value;
    const { slots } = column;
    const { labelField, checkMethod, visibleMethod } = radioOpts;
    const { row } = params;
    const defaultSlot = slots ? slots.default : null;
    const radioSlot = slots ? slots.radio : null;
    const isChecked = $table.eqRow(row, selectRadioRow);
    const isVisible = !visibleMethod || visibleMethod({ row });
    let isDisabled = !!checkMethod;
    let ons;
    if (!isHidden) {
      ons = {
        onClick(evnt) {
          if (!isDisabled && isVisible) {
            $table.triggerRadioRowEvent(evnt, params);
          }
        }
      };
      if (checkMethod) {
        isDisabled = !checkMethod({ row });
      }
    }
    const radioParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible });
    if (radioSlot) {
      return renderCellBaseVNs(params, $table.callSlot(radioSlot, radioParams));
    }
    const radioVNs = [];
    if (isVisible) {
      radioVNs.push(h("span", {
        class: ["vxe-radio--icon", isChecked ? getIcon().TABLE_RADIO_CHECKED : getIcon().TABLE_RADIO_UNCHECKED]
      }));
    }
    if (defaultSlot || labelField) {
      radioVNs.push(h("span", {
        class: "vxe-radio--label"
      }, defaultSlot ? $table.callSlot(defaultSlot, radioParams) : import_xe_utils4.default.get(row, labelField)));
    }
    return renderCellBaseVNs(params, [
      h("span", Object.assign({ class: ["vxe-cell--radio", {
        "is--checked": isChecked,
        "is--disabled": isDisabled
      }] }, ons), radioVNs)
    ]);
  },
  renderTreeRadioCell(params) {
    return Cell.renderTreeIcon(params, Cell.renderRadioCell(params));
  },
  /**
   * 多选
   */
  renderCheckboxHeader(params) {
    const { $table, column, isHidden } = params;
    const tableReactData = $table.reactData;
    const { computeIsAllCheckboxDisabled, computeCheckboxOpts } = $table.getComputeMaps();
    const { isAllSelected: isAllCheckboxSelected, isIndeterminate: isAllCheckboxIndeterminate } = tableReactData;
    const isAllCheckboxDisabled = computeIsAllCheckboxDisabled.value;
    const { slots } = column;
    const headerSlot = slots ? slots.header : null;
    const titleSlot = slots ? slots.title : null;
    const checkboxOpts = computeCheckboxOpts.value;
    const headerTitle = column.getTitle();
    const ons = {};
    if (!isHidden) {
      ons.onClick = (evnt) => {
        if (!isAllCheckboxDisabled) {
          $table.triggerCheckAllEvent(evnt, !isAllCheckboxSelected);
        }
      };
    }
    const checkboxParams = Object.assign(Object.assign({}, params), { checked: isAllCheckboxSelected, disabled: isAllCheckboxDisabled, indeterminate: isAllCheckboxIndeterminate });
    if (headerSlot) {
      return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, $table.callSlot(headerSlot, checkboxParams)));
    }
    if (checkboxOpts.checkStrictly ? !checkboxOpts.showHeader : checkboxOpts.showHeader === false) {
      return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [
        h("span", {
          class: "vxe-checkbox--label"
        }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : headerTitle)
      ]));
    }
    return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [
      h("span", Object.assign({ class: ["vxe-cell--checkbox", {
        "is--checked": isAllCheckboxSelected,
        "is--disabled": isAllCheckboxDisabled,
        "is--indeterminate": isAllCheckboxIndeterminate
      }], title: getI18n2("vxe.table.allTitle") }, ons), [
        h("span", {
          class: ["vxe-checkbox--icon", isAllCheckboxIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isAllCheckboxSelected ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
        })
      ].concat(titleSlot || headerTitle ? [
        h("span", {
          class: "vxe-checkbox--label"
        }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : headerTitle)
      ] : []))
    ]));
  },
  renderCheckboxCell(params) {
    const { $table, row, column, isHidden } = params;
    const tableProps = $table.props;
    const tableReactData = $table.reactData;
    const { treeConfig } = tableProps;
    const { selectCheckboxMaps, treeIndeterminateMaps } = tableReactData;
    const { computeCheckboxOpts } = $table.getComputeMaps();
    const checkboxOpts = computeCheckboxOpts.value;
    const { labelField, checkMethod, visibleMethod } = checkboxOpts;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    const checkboxSlot = slots ? slots.checkbox : null;
    let indeterminate = false;
    let isChecked = false;
    const isVisible = !visibleMethod || visibleMethod({ row });
    let isDisabled = !!checkMethod;
    const ons = {};
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isChecked = !!selectCheckboxMaps[rowid];
      ons.onClick = (evnt) => {
        if (!isDisabled && isVisible) {
          $table.triggerCheckRowEvent(evnt, params, !isChecked);
        }
      };
      if (checkMethod) {
        isDisabled = !checkMethod({ row });
      }
      if (treeConfig) {
        indeterminate = !!treeIndeterminateMaps[rowid];
      }
    }
    const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate });
    if (checkboxSlot) {
      return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));
    }
    const checkVNs = [];
    if (isVisible) {
      checkVNs.push(h("span", {
        class: ["vxe-checkbox--icon", indeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
      }));
    }
    if (defaultSlot || labelField) {
      checkVNs.push(h("span", {
        class: "vxe-checkbox--label"
      }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : import_xe_utils4.default.get(row, labelField)));
    }
    return renderCellBaseVNs(params, [
      h("span", Object.assign({ class: ["vxe-cell--checkbox", {
        "is--checked": isChecked,
        "is--disabled": isDisabled,
        "is--indeterminate": indeterminate,
        "is--hidden": !isVisible
      }] }, ons), checkVNs)
    ]);
  },
  renderTreeSelectionCell(params) {
    return Cell.renderTreeIcon(params, Cell.renderCheckboxCell(params));
  },
  renderCheckboxCellByProp(params) {
    const { $table, row, column, isHidden } = params;
    const tableProps = $table.props;
    const tableReactData = $table.reactData;
    const { treeConfig } = tableProps;
    const { treeIndeterminateMaps } = tableReactData;
    const { computeCheckboxOpts } = $table.getComputeMaps();
    const checkboxOpts = computeCheckboxOpts.value;
    const { labelField, checkField, checkMethod, visibleMethod } = checkboxOpts;
    const indeterminateField = checkboxOpts.indeterminateField || checkboxOpts.halfField;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    const checkboxSlot = slots ? slots.checkbox : null;
    let isIndeterminate = false;
    let isChecked = false;
    const isVisible = !visibleMethod || visibleMethod({ row });
    let isDisabled = !!checkMethod;
    const ons = {};
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isChecked = import_xe_utils4.default.get(row, checkField);
      ons.onClick = (evnt) => {
        if (!isDisabled && isVisible) {
          $table.triggerCheckRowEvent(evnt, params, !isChecked);
        }
      };
      if (checkMethod) {
        isDisabled = !checkMethod({ row });
      }
      if (treeConfig) {
        isIndeterminate = !!treeIndeterminateMaps[rowid];
      }
    }
    const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate: isIndeterminate });
    if (checkboxSlot) {
      return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));
    }
    const checkVNs = [];
    if (isVisible) {
      checkVNs.push(h("span", {
        class: ["vxe-checkbox--icon", isIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
      }));
      if (defaultSlot || labelField) {
        checkVNs.push(h("span", {
          class: "vxe-checkbox--label"
        }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : import_xe_utils4.default.get(row, labelField)));
      }
    }
    return renderCellBaseVNs(params, [
      h("span", Object.assign({ class: ["vxe-cell--checkbox", {
        "is--checked": isChecked,
        "is--disabled": isDisabled,
        "is--indeterminate": indeterminateField && !isChecked ? row[indeterminateField] : isIndeterminate,
        "is--hidden": !isVisible
      }] }, ons), checkVNs)
    ]);
  },
  renderTreeSelectionCellByProp(params) {
    return Cell.renderTreeIcon(params, Cell.renderCheckboxCellByProp(params));
  },
  /**
   * 展开行
   */
  renderExpandCell(params) {
    const { $table, isHidden, row, column } = params;
    const tableReactData = $table.reactData;
    const { rowExpandedMaps, rowExpandLazyLoadedMaps } = tableReactData;
    const { computeExpandOpts } = $table.getComputeMaps();
    const expandOpts = computeExpandOpts.value;
    const { lazy, labelField, iconLoaded, showIcon, iconOpen, iconClose, visibleMethod } = expandOpts;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    const iconSlot = slots ? slots.icon : null;
    let isActive = false;
    let isLazyLoading = false;
    if (iconSlot) {
      return renderCellBaseVNs(params, $table.callSlot(iconSlot, params));
    }
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isActive = !!rowExpandedMaps[rowid];
      if (lazy) {
        isLazyLoading = !!rowExpandLazyLoadedMaps[rowid];
      }
    }
    return renderCellBaseVNs(params, [
      showIcon && (!visibleMethod || visibleMethod(params)) ? h("span", {
        class: ["vxe-table--expanded", {
          "is--active": isActive
        }],
        onClick(evnt) {
          $table.triggerRowExpandEvent(evnt, params);
        }
      }, [
        h("i", {
          class: ["vxe-table--expand-btn", isLazyLoading ? iconLoaded || getIcon().TABLE_EXPAND_LOADED : isActive ? iconOpen || getIcon().TABLE_EXPAND_OPEN : iconClose || getIcon().TABLE_EXPAND_CLOSE]
        })
      ]) : renderEmptyElement($table),
      defaultSlot || labelField ? h("span", {
        class: "vxe-table--expand-label"
      }, defaultSlot ? $table.callSlot(defaultSlot, params) : import_xe_utils4.default.get(row, labelField)) : renderEmptyElement($table)
    ]);
  },
  renderExpandData(params) {
    const { $table, column } = params;
    const { slots, contentRender } = column;
    const contentSlot = slots ? slots.content : null;
    if (contentSlot) {
      return $table.callSlot(contentSlot, params);
    }
    if (contentRender) {
      const compConf = renderer.get(contentRender.name);
      if (compConf) {
        const rtExpand = compConf.renderTableExpand || compConf.renderExpand;
        if (rtExpand) {
          return getSlotVNs(rtExpand(contentRender, params));
        }
      }
    }
    return [];
  },
  /**
   * HTML 标签
   */
  renderHTMLCell(params) {
    const { $table, column } = params;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));
    }
    return renderCellBaseVNs(params, [
      h("span", {
        class: "vxe-cell--html",
        innerHTML: getDefaultCellLabel(params)
      })
    ]);
  },
  renderTreeHTMLCell(params) {
    return Cell.renderTreeIcon(params, Cell.renderHTMLCell(params));
  },
  /**
   * 排序和筛选
   */
  renderSortAndFilterHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params).concat(Cell.renderFilterIcon(params))));
  },
  /**
   * 排序
   */
  renderSortHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params)));
  },
  renderSortIcon(params) {
    const { $table, column } = params;
    const { computeSortOpts } = $table.getComputeMaps();
    const sortOpts = computeSortOpts.value;
    const { showIcon, iconLayout, iconAsc, iconDesc } = sortOpts;
    const { order } = column;
    if (showIcon) {
      return [
        h("span", {
          class: ["vxe-cell--sort", `vxe-cell--sort-${iconLayout}-layout`]
        }, [
          h("i", {
            class: ["vxe-sort--asc-btn", iconAsc || getIcon().TABLE_SORT_ASC, {
              "sort--active": order === "asc"
            }],
            title: getI18n2("vxe.table.sortAsc"),
            onClick(evnt) {
              evnt.stopPropagation();
              $table.triggerSortEvent(evnt, column, "asc");
            }
          }),
          h("i", {
            class: ["vxe-sort--desc-btn", iconDesc || getIcon().TABLE_SORT_DESC, {
              "sort--active": order === "desc"
            }],
            title: getI18n2("vxe.table.sortDesc"),
            onClick(evnt) {
              evnt.stopPropagation();
              $table.triggerSortEvent(evnt, column, "desc");
            }
          })
        ])
      ];
    }
    return [];
  },
  /**
   * 筛选
   */
  renderFilterHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderFilterIcon(params)));
  },
  renderFilterIcon(params) {
    const { $table, column, hasFilter } = params;
    const tableReactData = $table.reactData;
    const { filterStore } = tableReactData;
    const { computeFilterOpts } = $table.getComputeMaps();
    const filterOpts = computeFilterOpts.value;
    const { showIcon, iconNone, iconMatch } = filterOpts;
    return showIcon ? [
      h("span", {
        class: ["vxe-cell--filter", {
          "is--active": filterStore.visible && filterStore.column === column
        }]
      }, [
        h("i", {
          class: ["vxe-filter--btn", hasFilter ? iconMatch || getIcon().TABLE_FILTER_MATCH : iconNone || getIcon().TABLE_FILTER_NONE],
          title: getI18n2("vxe.table.filter"),
          onClick(evnt) {
            if ($table.triggerFilterEvent) {
              $table.triggerFilterEvent(evnt, params.column, params);
            }
          }
        })
      ])
    ] : [];
  },
  /**
   * 可编辑
   */
  renderEditHeader(params) {
    const { $table, column } = params;
    const tableProps = $table.props;
    const { computeEditOpts } = $table.getComputeMaps();
    const { editConfig, editRules } = tableProps;
    const editOpts = computeEditOpts.value;
    const { sortable, filters, editRender } = column;
    let isRequired = false;
    if (editRules) {
      const columnRules = import_xe_utils4.default.get(editRules, column.field);
      if (columnRules) {
        isRequired = columnRules.some((rule) => rule.required);
      }
    }
    let editIconVNs = [];
    if (isEnableConf(editConfig)) {
      editIconVNs = [
        isRequired && editOpts.showAsterisk ? h("i", {
          class: "vxe-cell--required-icon"
        }) : renderEmptyElement($table),
        isEnableConf(editRender) && editOpts.showIcon ? h("i", {
          class: ["vxe-cell--edit-icon", editOpts.icon || getIcon().TABLE_EDIT]
        }) : renderEmptyElement($table)
      ];
    }
    return renderHeaderCellBaseVNs(params, editIconVNs.concat(Cell.renderHeaderTitle(params)).concat(sortable ? Cell.renderSortIcon(params) : []).concat(filters ? Cell.renderFilterIcon(params) : []));
  },
  // 行格编辑模式
  renderRowEdit(params) {
    const { $table, column } = params;
    const tableReactData = $table.reactData;
    const { editStore } = tableReactData;
    const { actived } = editStore;
    const { editRender } = column;
    return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row);
  },
  renderTreeRowEdit(params) {
    return Cell.renderTreeIcon(params, Cell.renderRowEdit(params));
  },
  // 单元格编辑模式
  renderCellEdit(params) {
    const { $table, column } = params;
    const tableReactData = $table.reactData;
    const { editStore } = tableReactData;
    const { actived } = editStore;
    const { editRender } = column;
    return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row && actived.column === params.column);
  },
  renderTreeCellEdit(params) {
    return Cell.renderTreeIcon(params, Cell.renderCellEdit(params));
  },
  runRenderer(params, isEdit) {
    const { $table, column } = params;
    const { slots, editRender, formatter } = column;
    const defaultSlot = slots ? slots.default : null;
    const editSlot = slots ? slots.edit : null;
    const compConf = renderer.get(editRender.name);
    const rtEdit = compConf ? compConf.renderTableEdit || compConf.renderEdit : null;
    const cellParams = Object.assign({ $type: "", isEdit }, params);
    if (isEdit) {
      cellParams.$type = "edit";
      if (editSlot) {
        return $table.callSlot(editSlot, cellParams);
      }
      if (rtEdit) {
        return getSlotVNs(rtEdit(editRender, cellParams));
      }
      return [];
    }
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, cellParams));
    }
    if (formatter) {
      return renderCellBaseVNs(params, [
        h("span", {
          class: "vxe-cell--label"
        }, getDefaultCellLabel(cellParams))
      ]);
    }
    return Cell.renderDefaultCell(cellParams);
  }
};
var cell_default = Cell;

export {
  browse,
  initTpImg,
  getTpImg,
  getPropClass,
  isPx,
  isScale,
  hasClass,
  removeClass,
  addClass,
  getDomNode,
  getOffsetHeight,
  getPaddingTopBottomSize,
  setScrollTop,
  setScrollLeft,
  updateCellTitle,
  getEventTargetNode,
  getOffsetPos,
  getAbsolutePos,
  scrollToView,
  triggerEvent,
  isNodeElement,
  convertHeaderColumnToRows,
  restoreScrollLocation,
  getRowUniqueId,
  getRowkey,
  getRowid,
  handleFieldOrColumn,
  toFilters,
  toTreePathSeq,
  getCellValue,
  setCellValue,
  getRefElem,
  getColReMinWidth,
  isColumnInfo,
  watchColumn,
  assembleColumn,
  destroyColumn,
  getRootColumn,
  mergeBodyMethod,
  clearTableAllStatus,
  rowToVisible,
  colToVisible,
  cell_default
};
//# sourceMappingURL=chunk-YB3DXOVJ.js.map
