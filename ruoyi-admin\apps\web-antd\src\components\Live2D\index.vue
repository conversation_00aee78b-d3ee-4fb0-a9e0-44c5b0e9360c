<template>
  <!-- Live2D容器 -->
  <div
    v-if="!isHidden"
    class="live2d-container"
    :class="{ 'dragging': isDragging }"
    :style="{ right: position.right + 'px', bottom: position.bottom + 'px' }"
    @mousedown="handleMouseDown"
    @click="handleCanvasClick"
    @mouseenter="showToolbarOnHover"
    @mouseleave="hideToolbarOnLeave"
  >
    <canvas ref="liveCanvas"></canvas>

    <!-- 工具栏 - 只在悬停时显示 -->
    <div v-if="showToolbar" class="live2d-toolbar">
      <button @click="startScreenCapture" title="复制并批注">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
          <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"/>
        </svg>
      </button>
      <button @click="hideLive2D" title="隐藏看板娘">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M18 6 6 18"/>
          <path d="m6 6 12 12"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- 不再需要OCR组件 -->

  <!-- 消息气泡 - 跟随Live2D位置 -->
  <div
    v-if="showMessage && !isHidden"
    class="live2d-message-bubble"
    :class="messageType"
    :style="{
      right: (position.right + 50) + 'px',
      bottom: (position.bottom + 200) + 'px'
    }"
  >
    {{ currentMessage }}
  </div>

  <!-- 隐藏时的显示按钮 -->
  <div v-if="isHidden" class="live2d-show-button" @click="showLive2D">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as PIXI from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display/cubism4'
import { live2dListByName, live2dList2 } from '#/api/noval/live2d'
import { message } from 'ant-design-vue'
import { useClipboard } from '@vueuse/core'

// 为了 pixi-live2d-display 内部调用
window.PIXI = PIXI

// 禁用 Pixi.js 的控制台横幅
PIXI.utils.skipHello()

// 响应式数据
const liveCanvas = ref<HTMLCanvasElement>()
const showMessage = ref(false)
const currentMessage = ref('')
const messageType = ref('normal')
const autoMode = ref(true)
// 从localStorage读取Live2D的显示状态
const isHidden = ref(localStorage.getItem('live2d_hidden') === 'true')
const showToolbar = ref(false)

// 剪贴板相关
const { copy } = useClipboard()

// 拖拽相关状态
const isDragging = ref(false)
const dragStarted = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const position = ref({ right: 20, bottom: 20 }) // 初始位置

let app: PIXI.Application | null = null
let model: any = null
let autoTimer: number | null = null
let toolbarTimer: number | null = null

// 丰富的面部表情参数（基于cdi3.json）
const faceExpressions = [
  { name: '开心', params: { 'ParamMouthForm': 1, 'ParamBrowLY': 0.5, 'ParamBrowRY': 0.5 } },
  { name: '生气', params: { 'ParamMouthForm': -0.8, 'ParamBrowLY': -0.8, 'ParamBrowRY': -0.8 } },
  { name: '惊讶', params: { 'ParamMouthOpenY': 0.8, 'ParamBrowLY': 0.8, 'ParamBrowRY': 0.8 } },
  { name: '眨眼', params: { 'ParamEyeLOpen': 0, 'ParamEyeROpen': 0 } },
  { name: '微笑', params: { 'ParamMouthForm': 0.6, 'ParamBrowLY': 0.3, 'ParamBrowRY': 0.3 } },
  { name: '害羞', params: { 'ParamMouthForm': 0.3, 'ParamBrowLY': 0.2, 'ParamBrowRY': 0.2, 'ParamEyeLOpen': 0.7, 'ParamEyeROpen': 0.7 } },
  { name: '困惑', params: { 'ParamMouthForm': -0.3, 'ParamBrowLY': -0.3, 'ParamBrowRY': 0.3 } },
  { name: '得意', params: { 'ParamMouthForm': 0.8, 'ParamBrowLY': 0.6, 'ParamBrowRY': 0.6, 'ParamEyeLOpen': 0.8, 'ParamEyeROpen': 0.8 } },
  { name: '无奈', params: { 'ParamMouthForm': -0.2, 'ParamBrowLY': -0.4, 'ParamBrowRY': -0.4 } },
  { name: '调皮', params: { 'ParamMouthForm': 0.5, 'ParamEyeLOpen': 0.3, 'ParamEyeROpen': 1.0 } },
  { name: '思考', params: { 'ParamMouthForm': 0.1, 'ParamBrowLY': 0.2, 'ParamBrowRY': -0.2 } },
  { name: '撒娇', params: { 'ParamMouthForm': 0.4, 'ParamBrowLY': 0.4, 'ParamBrowRY': 0.4, 'ParamEyeLOpen': 0.6, 'ParamEyeROpen': 0.6 } }
]

// 服装配饰系统（基于exp3文件）
const outfits = [
  { name: 'JK包', file: 'jk包' },
  { name: '戴帽子', file: '戴帽子' },
  { name: '手柄', file: '手柄' },
  { name: '脱外套', file: '脱外套' },
  { name: '裙子', file: '裙子' },
  { name: '马尾L隐藏', file: '马尾L隐藏' },
  { name: '马尾R隐藏', file: '马尾R隐藏' }
]

// 特殊表情（眼睛相关）
const specialExpressions = [
  { name: '圈圈眼', file: '圈圈眼' },
  { name: '爱心眼', file: '爱心眼' },
  { name: '黑化', file: '黑化' }
]

// 身体动作系统
const bodyActions = [
  {
    name: '点头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': 5, 'ParamAngleZ': 0 },
    duration: 800,
    description: '轻轻点头表示同意'
  },
  {
    name: '摇头',
    params: { 'ParamAngleY': 10, 'ParamAngleX': 0, 'ParamAngleZ': 0 },
    duration: 1000,
    description: '左右摇头表示否定'
  },
  {
    name: '歪头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': 0, 'ParamAngleZ': 15 },
    duration: 1500,
    description: '可爱地歪着头思考'
  },
  {
    name: '左看',
    params: { 'ParamAngleY': -20, 'ParamAngleX': 0, 'ParamAngleZ': 0 },
    duration: 1200,
    description: '向左边看去'
  },
  {
    name: '右看',
    params: { 'ParamAngleY': 20, 'ParamAngleX': 0, 'ParamAngleZ': 0 },
    duration: 1200,
    description: '向右边看去'
  },
  {
    name: '抬头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': -10, 'ParamAngleZ': 0 },
    duration: 1000,
    description: '抬头看向上方'
  },
  {
    name: '低头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': 10, 'ParamAngleZ': 0 },
    duration: 1000,
    description: '害羞地低下头'
  }
]

// 复合动作（表情+身体动作）- 更符合新人设的台词
const complexActions = [
  {
    name: '害羞点头',
    faceExpression: { name: '害羞', params: { 'ParamMouthForm': 0.3, 'ParamBrowLY': 0.2, 'ParamBrowRY': 0.2, 'ParamEyeLOpen': 0.7, 'ParamEyeROpen': 0.7 } },
    bodyAction: { name: '点头', params: { 'ParamAngleY': 0, 'ParamAngleX': 5, 'ParamAngleZ': 0 } },
    message: '嗯嗯～人家知道了啦～\n不过...你这样看着我会让我害羞的'
  },
  {
    name: '调皮歪头',
    faceExpression: { name: '调皮', params: { 'ParamMouthForm': 0.5, 'ParamEyeLOpen': 0.3, 'ParamEyeROpen': 1.0 } },
    bodyAction: { name: '歪头', params: { 'ParamAngleY': 0, 'ParamAngleX': 0, 'ParamAngleZ': 15 } },
    message: '嘿嘿～这样看起来怎么样？\n是不是很可爱？还是说...很性感？'
  },
  {
    name: '思考抬头',
    faceExpression: { name: '思考', params: { 'ParamMouthForm': 0.1, 'ParamBrowLY': 0.2, 'ParamBrowRY': -0.2 } },
    bodyAction: { name: '抬头', params: { 'ParamAngleY': 0, 'ParamAngleX': -10, 'ParamAngleZ': 0 } },
    message: '让我想想看...\n该怎么让你更开心呢？'
  },
  {
    name: '得意左看',
    faceExpression: { name: '得意', params: { 'ParamMouthForm': 0.8, 'ParamBrowLY': 0.6, 'ParamBrowRY': 0.6 } },
    bodyAction: { name: '左看', params: { 'ParamAngleY': -20, 'ParamAngleX': 0, 'ParamAngleZ': 0 } },
    message: '哼哼～我很厉害吧！\n这可是我的独门绝技哦～'
  },
  {
    name: '撒娇摇头',
    faceExpression: { name: '撒娇', params: { 'ParamMouthForm': 0.4, 'ParamBrowLY': 0.4, 'ParamBrowRY': 0.4, 'ParamEyeLOpen': 0.6, 'ParamEyeROpen': 0.6 } },
    bodyAction: { name: '摇头', params: { 'ParamAngleY': 10, 'ParamAngleX': 0, 'ParamAngleZ': 0 } },
    message: '不要嘛～人家不想这样...\n除非你答应我一个小小的要求？'
  }
]

// Live2D 配置 - 优先使用本地模型，支持云端模型
const live2dConfig = {
  modelAPI: '/live2d/真夜白音/',  // 本地模型路径
  cloudModelAPI: 'https://ruoyi-1363865599.cos-website.ap-guangzhou.myqcloud.com/live2d/真夜白音/',  // 云端模型路径
  modelId: 1,
  modelTexturesId: 0,
  waifuSize: [300, 400],
  showHitokoto: true,
  showWelcomeMessage: true,
  hitAreas: {
    head: { x: [-0.35, 0.35], y: [0.19, 0.79] },
    body: { x: [-0.3, 0.3], y: [-0.9, 0.3] }
  }
}

// 台词库 - 日欧混血，大大咧咧，稍微有点二次元，有一点点辣妹成分的人设
const messages = {
  welcome: [
    'Ciallo～(∠・ω< )⌒★ 又见面了呢！',
    'Yo！我是真夜白音，叫我白音就好啦～(｡◕‿◕｡)',
    'Hola～今天想和我做什么有趣的事呢？ヾ(≧▽≦*)o',
    'Hey there！准备好被我的魅力征服了吗？(◕‿◕)♡',
    'Bonjour～想不想看看我新学的技能？(⌐■_■)',
    'Guten Tag！今天的我也是超级可爱的呢～(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧'
  ],
  idle: [
    '诶～你在看什么呢？该不会是在偷看我吧？(￣▽￣)~*',
    '无聊死了～要不要我给你表演个什么？(´∀｀)',
    '今天的天气很适合...嗯哼，你懂的～☀️(＾◡＾)',
    '工作什么的，偶尔偷个懒也没关系啦～(◔◡◔)',
    '你知道吗？我可是很受欢迎的哦～想知道为什么吗？(◠‿◠)',
    '要不要一起玩游戏？我可是很厉害的呢！ヾ(≧▽≦*)o',
    '今天也要保持这种...怎么说呢，性感的状态？(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧',
    '你的眼神...嘿嘿，是不是被我迷住了？(๑˃ᴗ˂)ﻭ',
    '想听我唱歌吗？不过可能会让你心跳加速哦～♪(´▽｀)',
    '记得多喝水哦～身体健康才能...咳咳，你懂的(´∩｡• ᵕ •｡∩`)',
    '今天学了什么新的漫画或游戏吗？快告诉我！(◕‿◕)♡',
    '呐呐～要不要我教你一些...特别的技巧？(⁄ ⁄•⁄ω⁄•⁄ ⁄)'
  ],
  touch: [
    '哇！突然袭击是犯规的啦～(>_<)',
    '嘿嘿～手感不错吧？(≧∇≦)',
    '讨厌～这样摸的话...人家会有奇怪的感觉的(⁄ ⁄•⁄ω⁄•⁄ ⁄)',
    '你这个H！不过...我不讨厌就是了(´･ω･`)',
    '再这样的话，我可要反击了哦～(｡>﹏<｡)',
    'Kyaa～你想对我做什么呀？(ﾉ≧∀≦)ﾉ',
    '诶嘿嘿～原来你是这样的人呢～(￣▽￣)',
    '你的手好温暖呢～让我想起了一些事情...(｡♥‿♥｡)',
    '好舒服～再来一次嘛！(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧',
    '这种感觉...嗯～让我想要更多呢(๑•̀ㅂ•́)و✧',
    '你知道吗？这样摸我的话...会让我想入非非的(´∀｀)',
    '哼哼～看来你很懂嘛，不愧是我看中的人～(⌐■_■)'
  ],
  praise: [
    '哇～被夸奖了呢！心情超好的～(｡♥‿♥｡)',
    '嘿嘿～人家当然很棒啦，不过听你这么说还是很开心呢(⁄ ⁄•⁄ω⁄•⁄ ⁄)',
    '你的眼光不错嘛～知道欣赏我的美！(◕‿◕)♡',
    '真的吗？那要不要...更深入地了解我？(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧',
    '你这么会说话，该不会是想泡我吧？(◕‿◕)♡',
    'Amazing！你的嘴真甜呢～(☆▽☆)',
    '不愧是有品味的人～我就知道你懂我(￣▽￣)~*',
    '这种夸奖...让我想要给你一些特别的奖励呢～(⌐■_■)'
  ],
  expression: [
    '这个表情怎么样？是不是很有魅力？(◔◡◔)',
    '我的表情可是经过特训的哦～每一个都很诱人呢(´∀｀)',
    '喜欢这个表情吗？还是说...你更喜欢我其他的表情？(｡◕‿◕｡)',
    '换个心情换个表情！就像换衣服一样简单～(≧∇≦)',
    '每个表情都有不同的感觉哦～想试试哪一种？(◕‿◕)♡',
    '看我的新表情！学会了就能撩到很多人呢～ヾ(≧▽≦*)o',
    '表情管理大师就是我～想学的话可以私下教你哦(⌐■_■)',
    '这样的我可爱吗？还是说...你更喜欢性感一点的？(´∩｡• ᵕ •｡∩`)',
    '表情包又增加了呢！都是我精心研究的杀手锏～(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧'
  ],
  action: [
    '让我做个动作给你看～保证让你印象深刻(◕‿◕)',
    '我会很多有趣的动作哦！有些还挺...刺激的呢(´▽`)',
    '这个动作帅气吗？还是说你更喜欢可爱一点的？(⌐■_■)',
    '看我的特技表演！这可是我的独门绝技～ヾ(≧▽≦*)o',
    '动作演示开始！准备好被我的魅力征服了吗？(๑•̀ㅂ•́)و✧',
    '我的动作库很丰富呢～想看哪一种类型的？(◔◡◔)',
    '要不要再来一个动作？我可以一直表演给你看哦(◕‿◕)♡',
    '这就是我的看家本领！怎么样，是不是很厉害？(￣▽￣)~*'
  ],
  outfit: [
    '这套衣服好看吗？穿上它我感觉自己更性感了呢(｡◕‿◕｡)',
    '换个造型试试～你觉得我适合什么风格？(´∀｀)',
    '我有很多漂亮的衣服呢！每一套都能展现不同的魅力(◕‿◕)♡',
    '今天穿这个怎么样？会不会太诱人了？(◔◡◔)',
    '时尚就是要不断变化！就像我的心情一样多变～ヾ(≧▽≦*)o',
    '这身打扮...是不是让你有点心动了？(⁄ ⁄•⁄ω⁄•⁄ ⁄)'
  ],
  hide: [
    '要休息一下吗？不过...会想我的吧？(´∩｡• ᵕ •｡∩`)',
    '我先隐藏一下～但是心里会一直想着你哦(>_<)',
    '想我的时候就点击召唤我吧！我随时为你待命～(´▽`)',
    'Ciao～记得想我哦！不想的话我会生气的(｡◕‿◕｡)ﾉ',
    'Bonne nuit～做梦的时候记得梦到我(˘▾˘~)',
    '下次见面要更开心哦！说不定我会给你惊喜呢(◕‿◕)♡',
    '我去充电啦～充满电后会更有活力的⚡(◔◡◔)',
    '保重身体哦～身体好了才能...嘿嘿，你懂的(｡•́‿•̀｡)'
  ],
  time: {
    morning: [
      'Guten Morgen！新的一天开始了～今天想和我做什么呢？(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧',
      'Bonjour！今天也要元气满满！不过...也要记得休息哦(´∀｀)♡',
      'おはよう！早上的我特别有精神呢～(｡◕‿◕｡)',
      'Good morning！昨晚有没有梦到我？(⁄ ⁄•⁄ω⁄•⁄ ⁄)'
    ],
    noon: [
      '中午了，记得吃午饭哦～不过我更想...算了，先吃饭吧(´∩｡• ᵕ •｡∩`)',
      '午休时间到了！要不要一起午睡？我可以当你的抱枕哦(￣▽￣)~*',
      '要好好吃饭哦～身体是革命的本钱呢(◕‿◕)♡',
      'Buon pomeriggio！中午的阳光就像你一样温暖呢～(☆▽☆)'
    ],
    afternoon: [
      'Buenas tardes！工作辛苦了～要不要我给你按摩？(｡•́‿•̀｡)',
      '下午茶时间到了！我可以陪你一起享受悠闲时光♪(´▽`)',
      '要不要休息一下呢？我有很多放松的方法哦(◔◡◔)',
      '下午的时光总是让人想要...做一些特别的事情呢(⌐■_■)'
    ],
    evening: [
      'Bonsoir！今天辛苦了～晚上想做什么呢？(｡◕‿◕｡)',
      '该休息了哦～不过在睡前...要不要聊聊天？(˘▾˘~)',
      'Buenas noches！做个好梦～希望能在梦里见到我(´∩｡• ᵕ •｡∩`)',
      'おやすみ～晚安吻送给你哦～(｡♥‿♥｡)',
      '夜晚的时光总是特别浪漫呢～(⁄ ⁄•⁄ω⁄•⁄ ⁄)'
    ]
  },
  encouragement: [
    '你一定可以的！我相信你的实力～(๑•̀ㅂ•́)و✧',
    '加油加油！我会在这里为你加油的ヾ(≧▽≦*)o',
    '相信自己！就像我相信你一样(◕‿◕)♡',
    '不要放弃哦～放弃的话我会很失望的(｡•́‿•̀｡)',
    '你是最棒的！在我心里你就是No.1(◔◡◔)',
    '一步一步来就好～我会陪着你的(´▽`)',
    '我会一直支持你的！无论什么时候都在你身边(｡♥‿♥｡)',
    'ファイト！Fighting！¡Vamos！加油！(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧',
    '累了的话就靠在我身上休息一下吧～(´∩｡• ᵕ •｡∩`)'
  ]
}

// 当前模型信息
const currentModel = ref({
  name: '',
  url: '',
  files: []
})

// 从数据库获取默认模型
const getDefaultModelFromDB = async () => {
  try {
    // 使用统一的list2 API查询指定名字的模型
    const records = await live2dList2({
      name: '真夜白音',
      type: null
    })

    if (records && records.length > 0) {
      // 查找主模型文件（.model3.json）
      const mainModelRecord = records.find(record =>
        record.originalName && record.originalName.endsWith('.model3.json')
      )

      if (mainModelRecord) {
        return {
          modelUrl: mainModelRecord.icon,
          modelName: '真夜白音',
          allFiles: records
        }
      }
    }
    // 如果没有找到记录或主模型文件，返回null以使用本地模型
    return null
  } catch (error) {
    console.error('获取数据库模型失败:', error)
    // 显示错误消息，但不阻止继续使用本地模型
    setTimeout(() => {
      displayMessage('模型数据加载失败，使用本地模型', 'normal', 3000)
    }, 1000)
    // 返回null以使用本地模型
    return null
  }
}

// 尝试抑制Cubism框架的日志消息
const suppressCubismLogs = () => {
  try {
    // 尝试重写console方法来过滤Cubism和Live2D相关的日志
    const originalConsoleLog = console.log
    const originalConsoleInfo = console.info

    console.log = function(...args) {
      // 过滤掉Live2D和Cubism相关的日志
      if (args.length > 0 && typeof args[0] === 'string') {
        const logMessage = args[0]
        if (logMessage.includes('Live2D') ||
            logMessage.includes('Cubism') ||
            logMessage.includes('CSM') ||
            logMessage.includes('PixiJS')) {
          return // 不输出这些日志
        }
      }
      originalConsoleLog.apply(console, args)
    }

    console.info = function(...args) {
      // 过滤掉Live2D和Cubism相关的日志
      if (args.length > 0 && typeof args[0] === 'string') {
        const logMessage = args[0]
        if (logMessage.includes('Live2D') ||
            logMessage.includes('Cubism') ||
            logMessage.includes('CSM') ||
            logMessage.includes('PixiJS')) {
          return // 不输出这些日志
        }
      }
      originalConsoleInfo.apply(console, args)
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 初始化Live2D
const initLive2D = async (modelUrl?: string) => {
  if (!liveCanvas.value) {
    console.error('Live2D Canvas 元素未找到')
    return
  }

  // 抑制Cubism框架的日志消息
  suppressCubismLogs()

  try {
    // 创建PIXI应用
    if (!app) {
      console.log('创建 PIXI 应用...')
      app = new PIXI.Application({
        view: liveCanvas.value,
        autoStart: true,
        backgroundAlpha: 0,
        width: 200,
        height: 250
      })
      console.log('PIXI 应用创建成功')
    }

    // 清除之前的模型
    if (model) {
      app.stage.removeChild(model)
      model.destroy()
      model = null
      console.log('清除旧模型')
    }

    // 使用传入的模型URL或默认本地模型
    const targetModelUrl = modelUrl || `${live2dConfig.modelAPI}真夜白音.model3.json`
    console.log('尝试加载模型:', targetModelUrl)

    // 尝试加载指定的模型
    let loadSuccess = false

    try {
      console.log('开始加载 Live2D 模型...')
      model = await Live2DModel.from(targetModelUrl)
      console.log('Live2D 模型加载完成:', model)

      if (model) {
        app.stage.addChild(model)
        model.scale.set(0.08)  // 进一步减小缩放比例，从0.15改为0.08
        model.x = app.screen.width / 2
        model.y = app.screen.height * 0.85  // 调整Y位置，从0.9改为0.85
        model.anchor.set(0.5, 0.5)
        model.interactive = true
        model.buttonMode = true

        console.log('模型设置完成，位置:', model.x, model.y)

        setupInteraction()
        checkAvailableMotions()

        setTimeout(() => {
          displayMessage(getRandomMessage('welcome'), 'welcome')
        }, 1000)

        startAutoMode()
        loadSuccess = true
        console.log('Live2D 模型初始化成功')
        return // 成功加载，直接返回
      }
    } catch (modelError) {
      console.error('模型加载失败:', targetModelUrl, modelError)
      console.error('错误详情:', modelError.message, modelError.stack)
    }

    // 如果是云端模型加载失败，尝试本地模型
    if (!loadSuccess && modelUrl && !modelUrl.startsWith('/live2d/')) {
      try {
        const localModelUrl = `${live2dConfig.modelAPI}真夜白音.model3.json`
        console.log('尝试回退到本地模型:', localModelUrl)
        model = await Live2DModel.from(localModelUrl)

        if (model) {
          app.stage.addChild(model)
          model.scale.set(0.08)  // 进一步减小缩放比例，从0.15改为0.08
          model.x = app.screen.width / 2
          model.y = app.screen.height * 0.85  // 调整Y位置，从0.9改为0.85
          model.anchor.set(0.5, 0.5)
          model.interactive = true
          model.buttonMode = true

          console.log('本地模型加载成功')
          setupInteraction()
          checkAvailableMotions()

          setTimeout(() => {
            displayMessage('已切换到本地模型', 'normal')
          }, 1000)

          startAutoMode()
          loadSuccess = true
          return
        }
      } catch (localError) {
        console.error('本地模型加载失败:', localError)
        console.error('本地模型错误详情:', localError.message, localError.stack)
      }
    }

    // 如果所有模型都加载失败
    if (!loadSuccess) {
      console.error('所有模型加载失败')
      displayMessage('Live2D模型加载失败，请检查网络连接和模型文件', 'normal', 5000)
      throw new Error('所有模型加载失败')
    }
  } catch (error) {
    console.error('Live2D初始化失败:', error)
    throw error
  }
}

// 监听应用模型事件
const handleApplyModel = async (event: CustomEvent) => {
  try {
    const { modelName, modelUrl, allFiles } = event.detail

    if (!modelUrl) {
      throw new Error('模型URL为空')
    }

    currentModel.value = {
      name: modelName,
      url: modelUrl,
      files: allFiles
    }

    // 显示加载中消息
    displayMessage(`正在加载 "${modelName}" 模型...`, 'normal', 3000)

    // 重新初始化模型
    await initLive2D(modelUrl)
    displayMessage(`已切换到 "${modelName}" 模型`, 'normal', 3000)
  } catch (error) {
    console.error('应用模型失败:', error)
    // 如果应用云端模型失败，尝试回退到本地模型
    try {
      await initLive2D()
      displayMessage('云端模型应用失败，已回退到本地模型', 'normal', 5000)
    } catch (fallbackError) {
      console.error('回退到本地模型也失败:', fallbackError)
      displayMessage('模型加载失败，请刷新页面重试', 'normal', 5000)
    }
  }
}



// 初始化
onMounted(async () => {
  console.log('Live2D 组件开始挂载...')

  // 监听应用模型事件
  window.addEventListener('applyLive2DModel', handleApplyModel as EventListener)
  // 添加文字复制触发事件监听器
  window.addEventListener('live2d-screenshot-trigger', handleScreenshotTrigger);

  try {
    console.log('开始初始化 Live2D...')
    // 默认使用本地模型，不再初始化时获取云端模型
    await initLive2D()
    console.log('Live2D 初始化完成')
  } catch (error) {
    console.error('Live2D初始化失败:', error)
    console.error('错误堆栈:', error.stack)
    // 显示错误消息
    setTimeout(() => {
      displayMessage('模型加载失败，请刷新页面重试', 'normal', 5000)
    }, 1000)
  }
})

// 检查可用的动作和表情
const checkAvailableMotions = () => {
  if (!model) return

  try {
    // 尝试播放默认表情
    setTimeout(() => {
      playRandomExpression()
    }, 500)

  } catch (error) {
    // 静默处理错误
  }
}

// 获取随机消息
const getRandomMessage = (category: string, subcategory?: string): string => {
  const msgs = messages as any
  let messageArray: string[] = []

  if (subcategory && msgs[category] && msgs[category][subcategory]) {
    messageArray = msgs[category][subcategory]
  } else if (msgs[category]) {
    messageArray = Array.isArray(msgs[category]) ? msgs[category] : []
  }

  if (messageArray.length === 0) return '...'
  return messageArray[Math.floor(Math.random() * messageArray.length)]
}

// 显示消息
const displayMessage = (message: string, type: string = 'normal', duration: number = 3000) => {
  currentMessage.value = message
  messageType.value = type
  showMessage.value = true

  setTimeout(() => {
    showMessage.value = false
  }, duration)
}

// 设置交互
const setupInteraction = () => {
  if (!model || !liveCanvas.value) return

  // 交互现在由容器的点击事件处理，不需要在canvas上设置
  // 移除鼠标悬停触发，只保留点击触发
}

// 播放面部表情（通过参数控制）
const playFaceExpression = (expressionData: any) => {
  if (!model || !model.internalModel) {
    return
  }

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      // 重置所有表情参数
      resetFaceParameters()

      // 设置新的表情参数
      for (const [paramId, value] of Object.entries(expressionData.params)) {
        const paramIndex = coreModel.getParameterIndex(paramId)
        if (paramIndex >= 0) {
          coreModel.setParameterValueByIndex(paramIndex, value as number)
        }
      }

      displayMessage(`${expressionData.name}的表情~`, 'expression')
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 重置面部参数
const resetFaceParameters = () => {
  if (!model || !model.internalModel) return

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      // 重置表情相关参数到默认值
      const resetParams = {
        'ParamMouthForm': 0,
        'ParamMouthOpenY': 0,
        'ParamBrowLY': 0,
        'ParamBrowRY': 0,
        'ParamBrowLForm': 0,
        'ParamBrowRForm': 0
      }

      for (const [paramId, value] of Object.entries(resetParams)) {
        const paramIndex = coreModel.getParameterIndex(paramId)
        if (paramIndex >= 0) {
          coreModel.setParameterValueByIndex(paramIndex, value)
        }
      }
    }
  } catch (error) {
    console.warn('Error resetting face parameters:', error)
  }
}

// 重置身体参数
const resetBodyParameters = () => {
  if (!model || !model.internalModel) return

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      // 重置身体动作相关参数到默认值
      const resetParams = {
        'ParamAngleX': 0,
        'ParamAngleY': 0,
        'ParamAngleZ': 0,
        'ParamBodyAngleX': 0,
        'ParamBodyAngleY': 0,
        'ParamBodyAngleZ': 0
      }

      for (const [paramId, value] of Object.entries(resetParams)) {
        const paramIndex = coreModel.getParameterIndex(paramId)
        if (paramIndex >= 0) {
          coreModel.setParameterValueByIndex(paramIndex, value)
        }
      }
    }
  } catch (error) {
    console.warn('Error resetting body parameters:', error)
  }
}

// 播放身体动作
const playBodyAction = (actionData: any) => {
  if (!model || !model.internalModel) {
    return
  }

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      // 重置身体参数
      resetBodyParameters()

      // 设置新的身体动作参数
      for (const [paramId, value] of Object.entries(actionData.params)) {
        try {
          const paramIndex = coreModel.getParameterIndex(paramId)
          if (paramIndex >= 0) {
            coreModel.setParameterValueByIndex(paramIndex, value as number)
          }
        } catch (paramError) {
          // 静默处理参数设置错误
        }
      }

      displayMessage(`${actionData.description}`, 'action')

      // 动作持续时间后重置
      if (actionData.duration) {
        setTimeout(() => {
          resetBodyParameters()
        }, actionData.duration)
      }
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 播放复合动作（表情+身体动作）
const playComplexAction = (complexActionData: any) => {
  if (!model || !model.internalModel) {
    return
  }

  // 同时播放表情和身体动作
  playFaceExpression(complexActionData.faceExpression)

  setTimeout(() => {
    playBodyAction(complexActionData.bodyAction)
  }, 200) // 稍微延迟身体动作，让效果更自然

  // 显示专属消息
  setTimeout(() => {
    displayMessage(complexActionData.message, 'action')
  }, 400)
}

// 播放特殊表情（通过exp3文件）
const playSpecialExpression = (expressionFile: string) => {
  if (!model) return

  try {
    // 简化的表情播放
    if (typeof model.expression === 'function') {
      model.expression(expressionFile)
      return
    }

    if (model.internalModel?.expressionManager?.setExpression) {
      model.internalModel.expressionManager.setExpression(expressionFile)
      return
    }

    if (typeof model.motion === 'function') {
      model.motion(expressionFile)
      return
    }

  } catch (error) {
    // 静默处理错误
  }
}

// 换装功能
const changeOutfit = () => {
  const randomOutfit = outfits[Math.floor(Math.random() * outfits.length)]
  playSpecialExpression(randomOutfit.file)
  displayMessage(getRandomMessage('outfit'), 'praise')
}

// 随机动作表演
const playRandomAction = () => {
  const random = Math.random()

  if (random < 0.3) {
    // 30% 概率播放面部表情
    const randomFaceExpression = faceExpressions[Math.floor(Math.random() * faceExpressions.length)]
    playFaceExpression(randomFaceExpression)
  } else if (random < 0.5) {
    // 20% 概率播放身体动作
    const randomBodyAction = bodyActions[Math.floor(Math.random() * bodyActions.length)]
    playBodyAction(randomBodyAction)
  } else if (random < 0.7) {
    // 20% 概率播放复合动作
    const randomComplexAction = complexActions[Math.floor(Math.random() * complexActions.length)]
    playComplexAction(randomComplexAction)
  } else if (random < 0.9) {
    // 20% 概率播放特殊表情
    const randomSpecialExpression = specialExpressions[Math.floor(Math.random() * specialExpressions.length)]
    playSpecialExpression(randomSpecialExpression.file)
    displayMessage(`${randomSpecialExpression.name}的特效~`, 'expression')
  } else {
    // 10% 概率换装
    changeOutfit()
  }
}

// 保持原有的随机表情函数（用于初始化）
const playRandomExpression = () => {
  const randomFaceExpression = faceExpressions[Math.floor(Math.random() * faceExpressions.length)]
  playFaceExpression(randomFaceExpression)
}

// 拖拽功能
const handleMouseDown = (event: MouseEvent) => {
  const container = event.currentTarget as HTMLElement
  const rect = container.getBoundingClientRect()

  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  dragStarted.value = false // 重置拖拽开始标志

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  event.preventDefault()
}

const handleMouseMove = (event: MouseEvent) => {
  // 只有移动了一定距离才算开始拖拽
  if (!dragStarted.value) {
    dragStarted.value = true
    isDragging.value = true
  }

  if (!isDragging.value) return

  const newRight = window.innerWidth - event.clientX + dragOffset.value.x - 200 // 200是容器宽度
  const newBottom = window.innerHeight - event.clientY + dragOffset.value.y - 250 // 250是容器高度

  // 限制在屏幕范围内
  position.value.right = Math.max(0, Math.min(newRight, window.innerWidth - 200))
  position.value.bottom = Math.max(0, Math.min(newBottom, window.innerHeight - 250))
}

const handleMouseUp = () => {
  // 延迟重置拖拽状态，避免立即触发点击事件
  setTimeout(() => {
    isDragging.value = false
    dragStarted.value = false
  }, 100)

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 处理点击事件（区分拖拽和点击）
const handleCanvasClick = (event: MouseEvent) => {
  // 如果发生了拖拽，不触发点击事件
  if (isDragging.value || dragStarted.value) return

  // 触发Live2D交互
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 只显示触摸反应消息
  const touchMessage = getRandomMessage('touch')
  displayMessage(touchMessage, 'touch')
}

// 隐藏Live2D
const hideLive2D = () => {
  displayMessage(getRandomMessage('hide'), 'normal', 2000)
  setTimeout(() => {
    isHidden.value = true
    stopAutoMode()

    // 保存状态到localStorage
    localStorage.setItem('live2d_hidden', 'true')

    // 分发自定义事件，通知app.vue更新状态
    window.dispatchEvent(new CustomEvent('live2d-visibility-changed', {
      detail: { hidden: true }
    }))
  }, 2000)
}

// 显示Live2D
const showLive2D = () => {
  isHidden.value = false
  displayMessage('我回来啦！想我了吗？', 'welcome')

  // 保存状态到localStorage
  localStorage.setItem('live2d_hidden', 'false')

  // 分发自定义事件，通知app.vue更新状态
  window.dispatchEvent(new CustomEvent('live2d-visibility-changed', {
    detail: { hidden: false }
  }))
}

// 鼠标悬停显示工具栏
const showToolbarOnHover = () => {
  showToolbar.value = true
  // 清除之前的隐藏定时器
  if (toolbarTimer) {
    clearTimeout(toolbarTimer)
    toolbarTimer = null
  }
}

// 鼠标离开后延迟隐藏工具栏
const hideToolbarOnLeave = () => {
  // 3秒后隐藏工具栏
  toolbarTimer = window.setTimeout(() => {
    showToolbar.value = false
    toolbarTimer = null
  }, 3000)
}

// 测试消息函数已移除

// 随机台词
const sayRandomMessage = () => {
  const hour = new Date().getHours()
  let category = 'idle'

  if (hour >= 6 && hour < 12) category = 'morning'
  else if (hour >= 12 && hour < 14) category = 'noon'
  else if (hour >= 14 && hour < 18) category = 'afternoon'
  else if (hour >= 18 || hour < 6) category = 'evening'

  const message = hour >= 6 && hour <= 22
    ? getRandomMessage('time', category)
    : getRandomMessage('idle')

  displayMessage(message, 'normal')
}

// 自动随机动作模式（已默认开启）

const startAutoMode = () => {
  if (autoTimer) return

  autoTimer = window.setInterval(() => {
    const random = Math.random()

    if (random < 0.4) {
      // 40% 概率播放表情/动作
      playRandomAction()
    } else if (random < 0.7) {
      // 30% 概率说随机台词
      sayRandomMessage()
    } else if (random < 0.9) {
      // 20% 概率换装
      changeOutfit()
    }
    // 10% 概率什么都不做，保持自然
  }, 30000) // 每30秒执行一次
}

const stopAutoMode = () => {
  if (autoTimer) {
    clearInterval(autoTimer)
    autoTimer = null
  }
}

// 清理
// 监听文字复制触发事件
const handleScreenshotTrigger = (event: Event) => {
  // 检查事件是否包含详情信息
  const customEvent = event as CustomEvent;
  const detail = customEvent.detail;

  // 如果有详情信息且来自起点中文网，记录相关信息
  if (detail && detail.isQidian) {
    console.log('收到起点中文网文字复制请求:', detail);

    // 可以在这里保存起点中文网相关信息，以便在复制时使用
    sessionStorage.setItem('current_qidian_info', JSON.stringify(detail));

    // 显示特殊消息
    displayMessage('准备复制并添加批注...', 'normal');
  }

  // 启动文字复制功能
  startScreenCapture();
};



onBeforeUnmount(() => {
  stopAutoMode()
  if (toolbarTimer) {
    clearTimeout(toolbarTimer)
    toolbarTimer = null
  }
  // 移除事件监听器
  window.removeEventListener('applyLive2DModel', handleApplyModel as EventListener)
  window.removeEventListener('live2d-screenshot-trigger', handleScreenshotTrigger)
  model?.destroy()
  app?.destroy()
})

// 直接读取用户选中的文字并跳转到批注页面
const startScreenCapture = () => {
  // 获取用户选中的文字
  const selectedText = window.getSelection()?.toString() || '';

  if (selectedText.trim()) {
    // 如果有选中文字，复制并准备批注
    copy(selectedText);
    message.success(`已复制 ${selectedText.length} 个字符并准备添加批注`);
    displayMessage(`已复制「${selectedText.length > 10 ? selectedText.substring(0, 10) + '...' : selectedText}」并跳转到批注页面`, 'praise');

    // 获取当前小说信息（如果有）
    const qidianInfo = sessionStorage.getItem('current_qidian_info');
    let novalName = '未知小说';
    let novalChapter = '未知章节';
    let novalId = 0;

    if (qidianInfo) {
      try {
        const info = JSON.parse(qidianInfo);
        novalName = info.bookName || '未知小说';
        novalChapter = info.chapterName || '未知章节';
        novalId = info.bookId || 0;
      } catch (e) {
        console.error('解析起点中文网信息失败:', e);
      }
    }

    // 将选中的文字和小说信息存储到 sessionStorage
    const exegesisData = {
      novalContent: selectedText,
      novalName,
      novalChapter,
      novalId
    };
    sessionStorage.setItem('exegesis_data', JSON.stringify(exegesisData));

    // 跳转到批注页面
    window.open('/noval/exegesis', '_blank');
  } else {
    // 如果没有选中文字，提示用户
    message.warning('请先选择要复制的文字');
    displayMessage('请先选择要复制的文字', 'normal');
  }
};

// 不再需要OCR完成事件处理

// 不再需要批注相关代码

// 暴露方法
defineExpose({
  showMessage: displayMessage,
  playFaceExpression,
  playSpecialExpression,
  changeOutfit,
  sayRandomMessage,
  hideLive2D,
  showLive2D,
  startScreenCapture
})
</script>

<style scoped>
.live2d-container {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
  width: 200px;
  height: 250px;
  overflow: hidden;
  cursor: move;
  transition: transform 0.1s ease;
  user-select: none;
}

.live2d-container.dragging {
  cursor: grabbing;
  transform: scale(1.02);
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.live2d-container canvas {
  pointer-events: none; /* 禁用canvas的事件，让容器处理拖拽 */
  width: 100%;
  height: 100%;
}

/* 消息气泡 - 独立定位，自适应大小 */
.live2d-message-bubble {
  position: fixed;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 14px 18px;
  border-radius: 20px;
  font-size: 14px;
  white-space: pre-wrap; /* 支持换行 */
  word-wrap: break-word; /* 自动换行 */
  min-width: 120px;
  max-width: 320px; /* 增大最大宽度 */
  width: auto; /* 自适应宽度 */
  text-align: left; /* 左对齐，更适合多行文本 */
  line-height: 1.4; /* 增加行高 */
  animation: bubbleSlideIn 0.4s ease-out;
  pointer-events: none;
  z-index: 99999;  /* 超高z-index */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease; /* 添加过渡动画 */
  backdrop-filter: blur(10px); /* 添加背景模糊效果 */
}

/* 气泡尾巴 */
.live2d-message-bubble::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(0, 0, 0, 0.9);
}

.live2d-message-bubble.welcome {
  background: linear-gradient(45deg, #ff6b6b, #feca57) !important;
  border-color: rgba(255, 107, 107, 0.5) !important;
}

.live2d-message-bubble.welcome::after {
  border-top-color: #ff6b6b !important;
}

.live2d-message-bubble.touch {
  background: linear-gradient(45deg, #ff9ff3, #f368e0) !important;
  border-color: rgba(255, 159, 243, 0.5) !important;
}

.live2d-message-bubble.touch::after {
  border-top-color: #ff9ff3 !important;
}

.live2d-message-bubble.normal {
  background: rgba(0, 0, 0, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.live2d-message-bubble.normal::after {
  border-top-color: rgba(0, 0, 0, 0.9) !important;
}

.live2d-message-bubble.expression {
  background: linear-gradient(45deg, #48cae4, #0077b6) !important;
  border-color: rgba(72, 202, 228, 0.5) !important;
}

.live2d-message-bubble.expression::after {
  border-top-color: #48cae4 !important;
}

.live2d-message-bubble.praise {
  background: linear-gradient(45deg, #54a0ff, #5f27cd) !important;
  border-color: rgba(84, 160, 255, 0.5) !important;
}

.live2d-message-bubble.praise::after {
  border-top-color: #54a0ff !important;
}

.live2d-message-bubble.action {
  background: linear-gradient(45deg, #a8e6cf, #88d8a3) !important;
  border-color: rgba(168, 230, 207, 0.5) !important;
}

.live2d-message-bubble.action::after {
  border-top-color: #a8e6cf !important;
}

/* 工具栏 */
.live2d-toolbar {
  position: absolute;
  top: 100px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: auto;
  opacity: 0.9;
  transition: opacity 0.3s ease;
  z-index: 10000;
}

.live2d-toolbar:hover {
  opacity: 1;
}

.live2d-toolbar button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 16px;
}

.live2d-toolbar button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.live2d-toolbar button.active {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
}

/* 气泡动画 */
@keyframes bubbleSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 显示按钮（隐藏时） */
.live2d-show-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 9999;
  animation: pulse 2s infinite;
}

.live2d-show-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.live2d-show-button svg {
  color: white;
  stroke: white;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 107, 107, 0.4);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .live2d-container {
    width: 160px;
    height: 180px;
    bottom: 0;
    right: 10px;
  }

  .live2d-toolbar button {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .live2d-message-bubble {
    font-size: 12px;
    max-width: 180px;
  }

  .live2d-show-button {
    width: 50px;
    height: 50px;
    bottom: 15px;
    right: 15px;
  }

  .live2d-show-button svg {
    width: 20px;
    height: 20px;
  }
}
</style>
