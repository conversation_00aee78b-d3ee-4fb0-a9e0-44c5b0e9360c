import type { DirectoryVO, DirectoryQuery } from './noval-directory-model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  novalDirectoryExport = '/noval/directory/export',
  novalDirectoryList = '/noval/directory/list',
  root = '/noval/directory',
}

/**
 * 查询小说章节目录详情
 * @param id 小说ID
 * @returns 章节目录数据
 */
export function novalDirectoryInfo(id: string) {
  return requestClient.get<DirectoryVO[]>(`${Api.root}/id/${id}`);
}

/**
 * 查询小说章节目录列表
 * @param params 查询参数
 * @returns 章节目录列表
 */
export function novalDirectoryList(params?: DirectoryQuery) {
  if (!params?.id) {
    return Promise.resolve({ rows: [], total: 0 });
  }
  return requestClient.get<DirectoryVO[]>(`${Api.root}/id/${params.id}`).then(data => ({
    rows: data || [],
    total: (data || []).length
  }));
}

/**
 * 导出字典数据
 * @param data 表单参数
 * @returns blob
 */
export function novalDirectoryExport(data: Partial<DirectoryVO>) {
  return commonExport(Api.novalDirectoryExport, data);
}

/**
 * 删除
 * @param createDepts 字典ID Array
 * @returns void
 */
export function novalDirectoryRemove(createDepts: IDS, chapter: string) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${createDepts}/${chapter}`);
}

/**
 * 新增
 * @param data 表单参数
 * @returns void
 */
export function novalDirectoryAdd(data: Partial<DirectoryVO>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改
 * @param data 表单参数
 * @returns void
 */
export function novalDirectoryUpdate(data: Partial<DirectoryVO>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

// /**
//  * 查询字典数据详细
//  * @param dictCode 字典编码
//  * @returns 字典数据
//  */
// export function dictDetailInfo(dictCode: ID) {
//   return requestClient.get<DirectoryVO>(`${Api.root}/${dictCode}`);
// }
