import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@iconify+icons-streamline@1.2.3/node_modules/@iconify/icons-streamline/interface-login-dial-pad-finger-password-dial-pad-dot-finger.js
var require_interface_login_dial_pad_finger_password_dial_pad_dot_finger = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-streamline@1.2.3/node_modules/@iconify/icons-streamline/interface-login-dial-pad-finger-password-dial-pad-dot-finger.js"(exports) {
    var data = {
      "width": 14,
      "height": 14,
      "body": '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M7 13.5V8.25A1.25 1.25 0 0 1 8.25 7h0A1.25 1.25 0 0 1 9.5 8.25V11h2a2 2 0 0 1 2 2v.5"/><circle cx="1" cy="1" r=".5"/><circle cx="5" cy="1" r=".5"/><circle cx="9" cy="1" r=".5"/><circle cx="1" cy="4.5" r=".5"/><circle cx="5" cy="4.5" r=".5"/><circle cx="9" cy="4.5" r=".5"/><circle cx="1" cy="8" r=".5"/><path d="M5 8.5a.5.5 0 0 1 0-1Z"/></g>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_interface_login_dial_pad_finger_password_dial_pad_dot_finger();
//# sourceMappingURL=@iconify_icons-streamline_interface-login-dial-pad-finger-password-dial-pad-dot-finger.js.map
