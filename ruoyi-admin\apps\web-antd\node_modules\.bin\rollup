#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/dist/bin/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/dist/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/dist/bin/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/dist/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/rollup@4.37.0/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/dist/bin/rollup" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/rollup@4.37.0/node_modules/rollup/dist/bin/rollup" "$@"
fi
