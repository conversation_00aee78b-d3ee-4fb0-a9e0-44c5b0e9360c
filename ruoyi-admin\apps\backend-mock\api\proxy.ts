import { defineEventHandler, getQuery } from 'h3';
import axios from 'axios';

/**
 * 代理请求外部网站内容
 * 用于解决前端跨域问题
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query = getQuery(event);
    const url = query.url as string;

    if (!url) {
      return {
        code: 400,
        message: '缺少URL参数',
      };
    }

    // 验证URL格式
    try {
      new URL(url);
    } catch (e) {
      return {
        code: 400,
        message: 'URL格式无效',
      };
    }

    // 设置通用请求头，模拟浏览器行为
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'max-age=0',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Referer': new URL(url).origin,
    };

    // 检查是否是起点网站
    const isQidian = url.includes('qidian.com');
    // 检查是否是新版起点章节页面格式
    const isNewQidianChapter = url.includes('qidian.com/chapter/');

    // 发送请求获取内容
    const response = await axios.get(url, {
      headers,
      timeout: 15000, // 15秒超时
      // 某些网站可能需要跟随重定向
      maxRedirects: 5,
    });

    // 提取内容
    const html = response.data;
    let content = '';
    let title = '';

    // 根据不同网站使用不同的内容提取逻辑
    if (typeof html === 'string') {
      // 提取标题
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (titleMatch && titleMatch[1]) {
        title = titleMatch[1].trim();
      }

      // 针对起点网站的特殊处理
      if (isQidian) {
        // 针对新版起点章节页面的特殊处理
        if (isNewQidianChapter) {
          try {
            // 尝试提取小说章节内容
            let chapterContent = '';
            let chapterTitle = '';

            // 提取章节标题
            const titleSelectors = [
              /<h[1-3][^>]*class="[^"]*chapter-title[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<h[1-3][^>]*class="[^"]*j_chapterName[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<h[1-3][^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<div[^>]*class="[^"]*chapter-name[^"]*"[^>]*>([^<]+)<\/div>/i,
              /<div[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/div>/i,
              /<span[^>]*class="[^"]*content-wrap[^"]*"[^>]*>([^<]+)<\/span>/i,
              /<span[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/span>/i,
              /<span[^>]*class="[^"]*chapter[^"]*"[^>]*>([^<]+)<\/span>/i
            ];

            // 如果常规选择器没有找到标题，尝试从URL中提取
            if (!chapterTitle) {
              const urlMatch = url.match(/\/chapter\/\d+\/(\d+)\/?/);
              if (urlMatch && urlMatch[1]) {
                // 尝试在页面中查找包含这个章节ID的标题元素
                const chapterIdPattern = new RegExp(`data-chapter-id="${urlMatch[1]}"[^>]*>([^<]+)<`, 'i');
                const chapterIdMatch = html.match(chapterIdPattern);
                if (chapterIdMatch && chapterIdMatch[1]) {
                  chapterTitle = chapterIdMatch[1].trim();
                }
              }
            }

            for (const selector of titleSelectors) {
              const match = html.match(selector);
              if (match && match[1]) {
                chapterTitle = match[1].trim();
                if (chapterTitle) break;
              }
            }

            // 如果找到标题，使用它
            if (chapterTitle) {
              title = chapterTitle;
            }

            // 新版起点章节内容选择器
            const contentSelectors = [
              { regex: /<div[^>]*class="[^"]*content-text[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' },
              { regex: /<div[^>]*class="[^"]*read-content[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' },
              { regex: /<div[^>]*class="[^"]*chapter-content[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' },
              { regex: /<div[^>]*id="content"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' },
              { regex: /<div[^>]*class="[^"]*main-text-wrap[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' },
              { regex: /<div[^>]*class="[^"]*content-wrap[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' },
              { regex: /<div[^>]*class="[^"]*j_readContent[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: 'div' }
            ];

            // 尝试每个选择器
            for (const selector of contentSelectors) {
              const match = html.match(selector.regex);
              if (match && match[1]) {
                chapterContent = match[1];

                // 提取段落
                const paragraphs = chapterContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                if (paragraphs && paragraphs.length > 0) {
                  // 将段落转换为HTML格式，保留段落结构
                  const processedParagraphs = paragraphs
                    .map(p => {
                      // 移除HTML标签，保留文本
                      const text = p.replace(/<[^>]+>/g, '').trim();
                      // 过滤掉广告和无关内容
                      if (text &&
                          !text.includes('起点') &&
                          !text.includes('广告') &&
                          !text.includes('推荐') &&
                          !text.includes('手机') &&
                          !text.includes('APP') &&
                          !text.includes('请记住本站') &&
                          !text.includes('网址') &&
                          !text.includes('收藏本站')) {
                        return `<p>${text}</p>`;
                      }
                      return '';
                    })
                    .filter(p => p); // 过滤空段落

                  if (processedParagraphs.length > 0) {
                    content = processedParagraphs.join('\n');
                    break;
                  }
                }
              }
            }

            // 尝试从meta标签中提取内容
            if (!content) {
              try {
                // 查找meta标签中的描述信息
                const metaDescriptionMatch = html.match(/<meta[^>]*name="description"[^>]*content="([^"]+)"[^>]*>/i) ||
                                           html.match(/<meta[^>]*property="og:description"[^>]*content="([^"]+)"[^>]*>/i);

                if (metaDescriptionMatch && metaDescriptionMatch[1]) {
                  const description = metaDescriptionMatch[1].trim();

                  // 确保描述足够长，可能是章节内容的摘要
                  if (description.length > 50 &&
                      !description.includes('起点中文网') &&
                      !description.includes('小说阅读') &&
                      !description.includes('精彩小说')) {

                    // 将描述作为段落
                    content = `<p>${description}</p>`;

                    // 如果描述中包含"章节内容由"等提示信息，则提示用户
                    if (description.includes('章节内容由') ||
                        description.includes('内容更新') ||
                        description.includes('最新章节')) {
                      content += '\n<p>注意：当前页面可能需要登录或者通过其他方式访问才能查看完整内容。</p>';
                    }
                  }
                }
              } catch (e) {
                console.error('从meta标签提取内容失败:', e);
              }
            }

            // 尝试从JSON-LD结构化数据中提取内容
            if (!content) {
              try {
                // 查找JSON-LD脚本标签
                const jsonLdMatch = html.match(/<script[^>]*type="application\/ld\+json"[^>]*>([\s\S]*?)<\/script>/i);
                if (jsonLdMatch && jsonLdMatch[1]) {
                  try {
                    const jsonLdData = JSON.parse(jsonLdMatch[1]);

                    // 尝试从不同的属性中提取内容
                    let articleBody = '';
                    if (jsonLdData.articleBody) {
                      articleBody = jsonLdData.articleBody;
                    } else if (jsonLdData.mainEntity && jsonLdData.mainEntity.articleBody) {
                      articleBody = jsonLdData.mainEntity.articleBody;
                    } else if (jsonLdData.description) {
                      articleBody = jsonLdData.description;
                    }

                    if (articleBody && articleBody.length > 100) {
                      // 将内容分割成段落
                      const paragraphs = articleBody
                        .split(/\n+|<br\s*\/?>|\r\n/)
                        .filter(p => p.trim().length > 5)
                        .map(p => `<p>${p.trim()}</p>`);

                      if (paragraphs.length > 0) {
                        content = paragraphs.join('\n');
                      }
                    }
                  } catch (jsonError) {
                    console.error('解析JSON-LD数据失败:', jsonError);
                  }
                }
              } catch (e) {
                console.error('从JSON-LD提取内容失败:', e);
              }
            }

            // 尝试从data属性中提取内容
            if (!content) {
              try {
                // 查找包含data-eid属性的元素
                const dataElements = html.match(/<[^>]*data-eid="[^"]*"[^>]*>([\s\S]*?)<\/[^>]*>/gi);
                if (dataElements && dataElements.length > 0) {
                  // 查找最长的元素内容
                  let longestContent = '';
                  for (const element of dataElements) {
                    const contentMatch = element.match(/>([\s\S]*?)<\//i);
                    if (contentMatch && contentMatch[1]) {
                      const elementContent = contentMatch[1].trim();
                      if (elementContent.length > longestContent.length &&
                          elementContent.length > 100 &&
                          !elementContent.includes('广告') &&
                          !elementContent.includes('起点')) {
                        longestContent = elementContent;
                      }
                    }
                  }

                  if (longestContent) {
                    // 将内容分割成段落
                    const paragraphs = longestContent
                      .split(/\n+|<br\s*\/?>|\r\n/)
                      .filter(p => p.trim().length > 5)
                      .map(p => `<p>${p.trim()}</p>`);

                    if (paragraphs.length > 0) {
                      content = paragraphs.join('\n');
                    }
                  }
                }
              } catch (e) {
                console.error('从data属性提取内容失败:', e);
              }
            }

            // 尝试从JavaScript数据中提取内容
            if (!content) {
              try {
                // 尝试查找包含章节内容的JavaScript数据
                const scriptDataMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({[\s\S]*?});/i) ||
                                      html.match(/g_data\s*=\s*({[\s\S]*?});/i) ||
                                      html.match(/chapter\s*:\s*({[\s\S]*?}),/i) ||
                                      html.match(/chapterInfo\s*:\s*({[\s\S]*?}),/i);

                if (scriptDataMatch && scriptDataMatch[1]) {
                  const scriptDataText = scriptDataMatch[1];

                  // 尝试提取章节内容
                  const contentMatch = scriptDataText.match(/content\s*:\s*['"]([\s\S]*?)['"]/) ||
                                      scriptDataText.match(/chapterContent\s*:\s*['"]([\s\S]*?)['"]/) ||
                                      scriptDataText.match(/body\s*:\s*['"]([\s\S]*?)['"]/);

                  if (contentMatch && contentMatch[1]) {
                    let extractedContent = contentMatch[1];

                    // 尝试解析JSON数据
                    try {
                      // 尝试将提取的内容解析为JSON对象
                      const jsonData = JSON.parse(`{"content":"${extractedContent}"}`);
                      if (jsonData && jsonData.content) {
                        extractedContent = jsonData.content;
                      }
                    } catch (jsonError) {
                      // JSON解析失败，继续使用原始提取内容
                      console.error('JSON解析失败，使用原始内容:', jsonError);
                    }

                    // 处理可能的转义字符
                    extractedContent = extractedContent
                      .replace(/\\n/g, '\n')
                      .replace(/\\r/g, '')
                      .replace(/\\t/g, '')
                      .replace(/\\\\/g, '\\')
                      .replace(/\\'/g, "'")
                      .replace(/\\"/g, '"');

                    // 将内容分割成段落
                    const paragraphs = extractedContent
                      .split(/\n+/)
                      .filter(p => p.trim().length > 5)
                      .map(p => `<p>${p.trim()}</p>`);

                    if (paragraphs.length > 0) {
                      content = paragraphs.join('\n');
                    }
                  }
                }
              } catch (e) {
                console.error('从JavaScript数据提取内容失败:', e);
              }
            }

            // 尝试使用新的方法提取新版起点章节内容
            if (!content) {
              // 尝试查找特定的内容容器
              const contentContainerMatch = html.match(/<div[^>]*class="[^"]*main-text-wrap[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                                          html.match(/<div[^>]*class="[^"]*content-wrap[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                                          html.match(/<div[^>]*class="[^"]*j_readContent[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                                          html.match(/<article[^>]*class="[^"]*article-content[^"]*"[^>]*>([\s\S]*?)<\/article>/i);

              if (contentContainerMatch && contentContainerMatch[1]) {
                // 从容器中提取段落
                const containerContent = contentContainerMatch[1];
                const paragraphMatches = containerContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);

                if (paragraphMatches && paragraphMatches.length > 0) {
                  const filteredParagraphs = paragraphMatches
                    .map(p => {
                      const text = p.replace(/<[^>]+>/g, '').trim();
                      if (text &&
                          text.length > 5 &&
                          !text.includes('起点') &&
                          !text.includes('广告') &&
                          !text.includes('推荐') &&
                          !text.includes('手机') &&
                          !text.includes('APP') &&
                          !text.includes('请记住本站') &&
                          !text.includes('网址') &&
                          !text.includes('收藏本站')) {
                        return `<p>${text}</p>`;
                      }
                      return '';
                    })
                    .filter(p => p);

                  if (filteredParagraphs.length > 0) {
                    content = filteredParagraphs.join('\n');
                  }
                }
              }
            }

            // 如果上述方法都没有提取到内容，尝试直接提取所有段落
            if (!content) {
              // 清理HTML，移除干扰元素
              let cleanHtml = html
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
                .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '')
                .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '')
                .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')
                .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '');

              // 尝试提取所有段落
              const allParagraphs = cleanHtml.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
              if (allParagraphs && allParagraphs.length > 0) {
                // 过滤掉广告和无关内容的段落，并保留HTML格式
                const filteredParagraphs = allParagraphs
                  .map(p => {
                    const text = p.replace(/<[^>]+>/g, '').trim();
                    if (text &&
                        text.length > 5 && // 过滤掉太短的段落
                        !text.includes('起点') &&
                        !text.includes('广告') &&
                        !text.includes('推荐') &&
                        !text.includes('手机') &&
                        !text.includes('APP') &&
                        !text.includes('请记住本站') &&
                        !text.includes('网址') &&
                        !text.includes('收藏本站')) {
                      return `<p>${text}</p>`;
                    }
                    return '';
                  })
                  .filter(p => p);

                if (filteredParagraphs.length > 0) {
                  content = filteredParagraphs.join('\n');
                }
              }

              // 如果仍然没有内容，尝试提取文本节点
              if (!content) {
                // 尝试提取所有文本节点并组织成段落
                const textNodes = cleanHtml.match(/>[^<]{10,}</g);
                if (textNodes && textNodes.length > 0) {
                  const paragraphs = textNodes
                    .map(node => node.slice(1, -1).trim())
                    .filter(text =>
                      text &&
                      text.length > 10 &&
                      !text.includes('起点') &&
                      !text.includes('广告') &&
                      !text.includes('推荐') &&
                      !text.includes('手机') &&
                      !text.includes('APP') &&
                      !text.includes('请记住本站') &&
                      !text.includes('网址') &&
                      !text.includes('收藏本站')
                    )
                    .map(text => `<p>${text}</p>`);

                  if (paragraphs.length > 0) {
                    content = paragraphs.join('\n');
                  }
                }
              }
            }
          } catch (e) {
            console.error('提取新版起点章节内容失败:', e);
          }

          // 尝试从iframe中提取内容
            if (!content && isNewQidianChapter) {
              try {
                // 查找iframe标签
                const iframeMatches = html.match(/<iframe[^>]*src="([^"]+)"[^>]*>/gi);
                if (iframeMatches && iframeMatches.length > 0) {
                  // 提取iframe的src属性
                  for (const iframeTag of iframeMatches) {
                    const srcMatch = iframeTag.match(/src="([^"]+)"/i);
                    if (srcMatch && srcMatch[1]) {
                      const iframeSrc = srcMatch[1];
                      // 如果iframe的src是相对路径，转换为绝对路径
                      const absoluteUrl = iframeSrc.startsWith('http') ?
                        iframeSrc :
                        new URL(iframeSrc, new URL(url).origin).toString();

                      console.log('尝试从iframe获取内容:', absoluteUrl);
                      // 这里我们不能直接请求iframe内容，因为这会导致递归调用
                      // 但我们可以记录这个URL，提示用户直接访问iframe的URL
                      if (absoluteUrl.includes('qidian.com') || absoluteUrl.includes('chapter')) {
                        content = `<p>检测到内容可能在iframe中，请直接访问: <a href="${absoluteUrl}" target="_blank">${absoluteUrl}</a></p>`;
                        break;
                      }
                    }
                  }
                }
              } catch (e) {
                console.error('从iframe提取内容失败:', e);
              }
            }

            // 尝试从HTML注释中提取内容
            if (!content && isNewQidianChapter) {
              try {
                // 查找HTML注释中可能包含的章节内容
                const commentMatches = html.match(/<!--([\s\S]*?)-->/g);
                if (commentMatches && commentMatches.length > 0) {
                  // 查找最长的注释，可能包含章节内容
                  let longestComment = '';
                  for (const comment of commentMatches) {
                    const cleanComment = comment.replace(/<!--/, '').replace(/-->/, '').trim();
                    if (cleanComment.length > longestComment.length &&
                        cleanComment.includes('<p>') &&
                        !cleanComment.includes('广告') &&
                        !cleanComment.includes('起点')) {
                      longestComment = cleanComment;
                    }
                  }

                  if (longestComment && longestComment.length > 100) {
                    // 提取段落
                    const paragraphs = longestComment.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                    if (paragraphs && paragraphs.length > 0) {
                      const filteredParagraphs = paragraphs
                        .map(p => {
                          const text = p.replace(/<[^>]+>/g, '').trim();
                          if (text &&
                              text.length > 5 &&
                              !text.includes('起点') &&
                              !text.includes('广告')) {
                            return `<p>${text}</p>`;
                          }
                          return '';
                        })
                        .filter(p => p);

                      if (filteredParagraphs.length > 0) {
                        content = filteredParagraphs.join('\n');
                      }
                    }
                  }
                }
              } catch (e) {
                console.error('从HTML注释提取内容失败:', e);
              }
            }

            // 尝试从隐藏元素中提取内容
            if (!content && isNewQidianChapter) {
              try {
                // 查找可能被隐藏的内容元素
                const hiddenElements = html.match(/<div[^>]*style="[^"]*display:\s*none[^"]*"[^>]*>([\s\S]*?)<\/div>/gi) ||
                                     html.match(/<div[^>]*class="[^"]*hidden[^"]*"[^>]*>([\s\S]*?)<\/div>/gi);

                if (hiddenElements && hiddenElements.length > 0) {
                  // 查找最长的隐藏元素
                  let longestHiddenContent = '';
                  for (const element of hiddenElements) {
                    // 提取元素内容
                    const contentMatch = element.match(/>([\s\S]*?)<\/div>/i);
                    if (contentMatch && contentMatch[1]) {
                      const hiddenContent = contentMatch[1].trim();
                      // 检查内容是否可能是章节内容
                      if (hiddenContent.length > longestHiddenContent.length &&
                          hiddenContent.length > 200 &&
                          hiddenContent.includes('<p>') &&
                          !hiddenContent.includes('广告') &&
                          !hiddenContent.includes('起点')) {
                        longestHiddenContent = hiddenContent;
                      }
                    }
                  }

                  if (longestHiddenContent) {
                    // 提取段落
                    const paragraphs = longestHiddenContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                    if (paragraphs && paragraphs.length > 0) {
                      const filteredParagraphs = paragraphs
                        .map(p => {
                          const text = p.replace(/<[^>]+>/g, '').trim();
                          if (text && text.length > 5) {
                            return `<p>${text}</p>`;
                          }
                          return '';
                        })
                        .filter(p => p);

                      if (filteredParagraphs.length > 0) {
                        content = filteredParagraphs.join('\n');
                      }
                    }
                  }
                }
              } catch (e) {
                console.error('从隐藏元素提取内容失败:', e);
              }
            }

            // 如果所有方法都失败，尝试最简单的文本提取方法
            if (!content && isNewQidianChapter) {
              // 添加提示信息
              content = '<p>无法提取章节内容，可能原因：</p>\n' +
                       '<p>1. 该章节需要登录后才能阅读</p>\n' +
                       '<p>2. 该章节可能是付费内容</p>\n' +
                       '<p>3. 网站结构已更新，提取方法需要更新</p>\n' +
                       `<p>请尝试直接访问原网页: <a href="${url}" target="_blank">${url}</a></p>`;

              // 继续尝试提取，如果成功则覆盖提示信息
              try {
                // 清理HTML，移除所有可能的干扰元素
                let cleanHtml = html
                  .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                  .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
                  .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '')
                  .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '')
                  .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')
                  .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '')
                  .replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, '')
                  .replace(/<button\b[^<]*(?:(?!<\/button>)<[^<]*)*<\/button>/gi, '')
                  .replace(/<input\b[^<]*(?:(?!<\/input>)<[^<]*)*<\/input>/gi, '')
                  .replace(/<select\b[^<]*(?:(?!<\/select>)<[^<]*)*<\/select>/gi, '')
                  .replace(/<textarea\b[^<]*(?:(?!<\/textarea>)<[^<]*)*<\/textarea>/gi, '');

              // 提取所有文本并按句子分割
              const plainText = cleanHtml
                .replace(/<[^>]+>/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();

              if (plainText && plainText.length > 100) { // 确保有足够长的文本
                // 将文本分割成段落
                const sentences = plainText.split(/\.\s+|。\s*|!\s*|！\s*|\?\s*|？\s*/);
                const paragraphs = [];

                // 将句子组合成段落
                let currentParagraph = '';
                for (const sentence of sentences) {
                  const trimmedSentence = sentence.trim();
                  if (trimmedSentence.length > 10) {
                    if (currentParagraph.length < 100) {
                      currentParagraph += trimmedSentence + '。';
                    } else {
                      paragraphs.push(`<p>${currentParagraph}</p>`);
                      currentParagraph = trimmedSentence + '。';
                    }
                  }
                }

                // 添加最后一个段落
                if (currentParagraph.length > 0) {
                  paragraphs.push(`<p>${currentParagraph}</p>`);
                }

                if (paragraphs.length > 0) {
                  content = paragraphs.join('\n');
                }
              }
            } catch (e) {
              console.error('最终文本提取方法失败:', e);
            }
          }
        } else {
        try {
          // 尝试提取小说章节内容
          // 起点网站的章节内容通常在特定的div中
          let chapterContent = '';
          let chapterTitle = '';

          // 尝试多种选择器来提取起点小说内容
          const qidianSelectors = [
            // 主要内容选择器
            { regex: /<div[^>]*class="[^"]*read-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: 'div' },
            { regex: /<div[^>]*id="content"[^>]*>([\s\S]*?)<\/div>/i, type: 'div' },
            { regex: /<div[^>]*class="[^"]*content-text[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: 'div' },
            { regex: /<div[^>]*class="[^"]*chapter-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: 'div' },
            // 2023年新版起点网站结构
            { regex: /<div[^>]*class="[^"]*content-wrap[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: 'div' },
            // 老版本结构
            { regex: /<div[^>]*class="[^"]*box-center[^"]*"[^>]*>([\s\S]*?)<div[^>]*class="[^"]*chapter-control[^"]*"[^>]*>/i, type: 'content' }
          ];

          // 尝试提取章节标题
          const titleSelectors = [
            /<h[1-3][^>]*class="[^"]*j_chapterName[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
            /<h[1-3][^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
            /<h[1-3][^>]*>([^<]+)<\/h[1-3]>/i,
            /<div[^>]*class="[^"]*chapter-name[^"]*"[^>]*>([^<]+)<\/div>/i,
            /<div[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/div>/i
          ];

          for (const selector of titleSelectors) {
            const match = html.match(selector);
            if (match && match[1]) {
              chapterTitle = match[1].trim();
              if (chapterTitle) break;
            }
          }

          // 如果找到标题，使用它
          if (chapterTitle) {
            title = chapterTitle;
          }

          // 尝试每个选择器
          for (const selector of qidianSelectors) {
            const match = html.match(selector.regex);
            if (match && match[1]) {
              chapterContent = match[1];

              // 根据不同类型的内容区域进行不同处理
              if (selector.type === 'div') {
                // 提取段落
                const paragraphs = chapterContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                if (paragraphs && paragraphs.length > 0) {
                  // 将段落转换为HTML格式，保留段落结构
                  const processedParagraphs = paragraphs
                    .map(p => {
                      // 移除HTML标签，保留文本
                      const text = p.replace(/<[^>]+>/g, '').trim();
                      // 过滤掉广告和无关内容
                      if (text &&
                          !text.includes('起点') &&
                          !text.includes('广告') &&
                          !text.includes('推荐') &&
                          !text.includes('手机') &&
                          !text.includes('APP') &&
                          !text.includes('请记住本站') &&
                          !text.includes('网址') &&
                          !text.includes('收藏本站')) {
                        return `<p>${text}</p>`;
                      }
                      return '';
                    })
                    .filter(p => p); // 过滤空段落

                  if (processedParagraphs.length > 0) {
                    content = processedParagraphs.join('\n');
                    break;
                  }
                }
              } else if (selector.type === 'content') {
                // 处理整个内容区域
                let processedContent = chapterContent
                  .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除脚本
                  .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')   // 移除样式
                  .replace(/<div[^>]*class="[^"]*chapter-control[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '') // 移除章节控制区
                  .replace(/<div[^>]*class="[^"]*ad-content[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '') // 移除广告
                  .replace(/<div[^>]*class="[^"]*banner[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '') // 移除横幅
                  .replace(/<div[^>]*class="[^"]*footer[^"]*"[^>]*>[\s\S]*?<\/div>/gi, ''); // 移除页脚

                // 尝试提取段落
                const paragraphs = processedContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                if (paragraphs && paragraphs.length > 0) {
                  // 将段落转换为HTML格式，保留段落结构
                  const processedParagraphs = paragraphs
                    .map(p => {
                      // 移除HTML标签，保留文本
                      const text = p.replace(/<[^>]+>/g, '').trim();
                      // 过滤掉广告和无关内容
                      if (text &&
                          !text.includes('起点') &&
                          !text.includes('广告') &&
                          !text.includes('推荐') &&
                          !text.includes('手机') &&
                          !text.includes('APP') &&
                          !text.includes('请记住本站') &&
                          !text.includes('网址') &&
                          !text.includes('收藏本站')) {
                        return `<p>${text}</p>`;
                      }
                      return '';
                    })
                    .filter(p => p); // 过滤空段落

                  if (processedParagraphs.length > 0) {
                    content = processedParagraphs.join('\n');
                    break;
                  }
                } else {
                  // 如果没有找到段落，尝试直接处理内容
                  content = processedContent
                    .replace(/<[^>]+>/g, '\n') // 将HTML标签替换为换行
                    .replace(/\n+/g, '\n\n')  // 将连续换行替换为段落分隔
                    .replace(/\s{2,}/g, ' ')   // 合并多个空格
                    .trim();

                  // 将纯文本内容转换为HTML段落格式
                  if (content) {
                    const paragraphs = content.split('\n\n')
                      .filter(p => p.trim() && p.trim().length > 5) // 过滤空段落和太短的段落
                      .map(p => `<p>${p.trim()}</p>`);

                    if (paragraphs.length > 0) {
                      content = paragraphs.join('\n');
                      break;
                    }
                  }
                }
              }
            }
          }

          // 如果上述方法都没有提取到内容，尝试直接提取所有段落
          if (!content) {
            const allParagraphs = html.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
            if (allParagraphs && allParagraphs.length > 0) {
              // 过滤掉广告和无关内容的段落，并保留HTML格式
              const filteredParagraphs = allParagraphs
                .map(p => {
                  const text = p.replace(/<[^>]+>/g, '').trim();
                  if (text &&
                      text.length > 5 && // 过滤掉太短的段落
                      !text.includes('起点') &&
                      !text.includes('广告') &&
                      !text.includes('推荐') &&
                      !text.includes('手机') &&
                      !text.includes('APP') &&
                      !text.includes('请记住本站') &&
                      !text.includes('网址') &&
                      !text.includes('收藏本站')) {
                    return `<p>${text}</p>`;
                  }
                  return '';
                })
                .filter(p => p);

              if (filteredParagraphs.length > 0) {
                content = filteredParagraphs.join('\n');
              }
            }
          }

          // 如果仍然没有提取到内容，尝试使用更通用的方法
          if (!content) {
            // 移除所有可能的干扰元素
            let cleanHtml = html
              .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
              .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
              .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '')
              .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '')
              .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')
              .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '')
              .replace(/<div[^>]*class="[^"]*header[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')
              .replace(/<div[^>]*class="[^"]*footer[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')
              .replace(/<div[^>]*class="[^"]*nav[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')
              .replace(/<div[^>]*class="[^"]*menu[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')
              .replace(/<div[^>]*class="[^"]*ad[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '');

            // 尝试提取所有文本节点并组织成段落
            const textNodes = cleanHtml.match(/>[^<]{10,}</g);
            if (textNodes && textNodes.length > 0) {
              const paragraphs = textNodes
                .map(node => node.slice(1, -1).trim())
                .filter(text =>
                  text &&
                  text.length > 10 &&
                  !text.includes('起点') &&
                  !text.includes('广告') &&
                  !text.includes('推荐') &&
                  !text.includes('手机') &&
                  !text.includes('APP') &&
                  !text.includes('请记住本站') &&
                  !text.includes('网址') &&
                  !text.includes('收藏本站')
                )
                .map(text => `<p>${text}</p>`);

              if (paragraphs.length > 0) {
                content = paragraphs.join('\n');
              }
            }
          }
        } catch (e) {
            console.error('提取起点内容失败:', e);
          }
        }
      }

      // 如果没有提取到内容或不是特殊网站，使用通用方法
      if (!content) {
        // 通用提取方法 - 先清理HTML
        const cleanHtml = html
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除脚本
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')   // 移除样式
          .replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, '')     // 移除头部
          .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')       // 移除导航
          .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '') // 移除页眉
          .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '') // 移除页脚
          .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '')   // 移除侧边栏
          .replace(/<div[^>]*class="[^"]*header[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '') // 移除header类的div
          .replace(/<div[^>]*class="[^"]*footer[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '') // 移除footer类的div
          .replace(/<div[^>]*class="[^"]*nav[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')    // 移除nav类的div
          .replace(/<div[^>]*class="[^"]*ad[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')     // 移除广告类的div
          .replace(/<div[^>]*class="[^"]*comment[^"]*"[^>]*>[\s\S]*?<\/div>/gi, ''); // 移除评论区

        // 尝试提取段落
        const paragraphs = cleanHtml.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
        if (paragraphs && paragraphs.length > 0) {
          // 将段落转换为HTML格式，保留段落结构
          const processedParagraphs = paragraphs
            .map(p => {
              // 移除HTML标签，保留文本
              const text = p.replace(/<[^>]+>/g, '').trim();
              // 过滤掉太短的段落
              if (text && text.length > 5) {
                return `<p>${text}</p>`;
              }
              return '';
            })
            .filter(p => p); // 过滤空段落

          if (processedParagraphs.length > 0) {
            content = processedParagraphs.join('\n');
          }
        }

        // 如果没有找到段落，尝试提取所有文本节点
        if (!content) {
          // 尝试提取所有文本节点并组织成段落
          const textNodes = cleanHtml.match(/>[^<]{10,}</g);
          if (textNodes && textNodes.length > 0) {
            const paragraphs = textNodes
              .map(node => node.slice(1, -1).trim())
              .filter(text => text && text.length > 10)
              .map(text => `<p>${text}</p>`);

            if (paragraphs.length > 0) {
              content = paragraphs.join('\n');
            }
          }
        }

        // 如果仍然没有内容，回退到简单文本提取
        if (!content) {
          const plainText = cleanHtml
            .replace(/<[^>]+>/g, ' ')  // 移除HTML标签
            .replace(/\s+/g, ' ')      // 合并空白字符
            .trim();

          if (plainText) {
            // 将纯文本分割成段落
            const paragraphs = plainText
              .split(/\.\s+|。\s*|!\s*|！\s*|\?\s*|？\s*/)
              .filter(p => p.trim().length > 10)
              .map(p => `<p>${p.trim()}.</p>`);

            if (paragraphs.length > 0) {
              content = paragraphs.join('\n');
            } else {
              // 如果无法分割成段落，直接使用整个文本
              content = `<p>${plainText}</p>`;
            }
          }
        }
      }
    }

    return {
      code: 200,
      data: {
        title,
        content,
        url,
      },
    };
  } catch (error: any) {
    console.error('代理请求失败:', error);
    return {
      code: 500,
      message: `请求失败: ${error.message || '未知错误'}`,
    };
  }
});
