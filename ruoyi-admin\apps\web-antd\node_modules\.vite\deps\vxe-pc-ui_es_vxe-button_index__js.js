import {
  button_default
} from "./chunk-FNX7TMJH.js";
import "./chunk-ITALV4IT.js";
import "./chunk-WWZC6IIL.js";
import "./chunk-MJMV2QSJ.js";
import "./chunk-XKDGJOZF.js";
import {
  dynamicApp
} from "./chunk-URMRKTZM.js";
import {
  VxeUI
} from "./chunk-BCXSQRR2.js";
import "./chunk-PJ5U4TG7.js";
import "./chunk-GE6DY3YU.js";
import "./chunk-KT3WABTJ.js";
import "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/vxe-button/index.js
var vxe_button_default = button_default2;
export {
  Button,
  VxeButton,
  vxe_button_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-button_index__js.js.map
