import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { noticeData } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    pageNum = 1,
    pageSize = 20,
    noticeTitle,
    createBy,
    noticeType,
  } = getQuery(event);

  let listData = structuredClone(noticeData);

  // 根据查询条件筛选
  if (noticeTitle) {
    listData = listData.filter(item =>
      item.noticeTitle.includes(noticeTitle as string),
    );
  }

  if (createBy) {
    listData = listData.filter(item =>
      item.createBy.includes(createBy as string),
    );
  }

  if (noticeType) {
    listData = listData.filter(item => item.noticeType === noticeType);
  }

  // 按创建时间倒序排序
  listData.sort((a, b) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
  });

  // 分页处理
  const total = listData.length;
  const pageNumInt = parseInt(pageNum as string);
  const pageSizeInt = parseInt(pageSize as string);
  const startIndex = (pageNumInt - 1) * pageSizeInt;
  const endIndex = startIndex + pageSizeInt;
  const rows = listData.slice(startIndex, endIndex);

  return useResponseSuccess({
    total,
    rows,
    pageNum: pageNumInt,
    pageSize: pageSizeInt,
  });
});
