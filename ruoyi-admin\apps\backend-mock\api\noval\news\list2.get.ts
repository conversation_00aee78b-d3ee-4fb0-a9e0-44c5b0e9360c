import { unAuthorizedResponse } from '~/utils/response';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { newsData } from './data';

export default eventHandler(async (event) => {
  console.log('=== list2.get.ts 接收到请求 ===');
  console.log('请求URL:', event.node.req.url);
  console.log('请求方法:', event.node.req.method);

  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    console.log('用户认证失败');
    return unAuthorizedResponse(event);
  }
  console.log('用户认证成功:', userinfo);

  const query = getQuery(event);
  const MyId = query.MyId;
  const otherId = query.otherId;
  const type = query.type;

  console.log('后端接收到的请求参数:', { MyId, otherId, type });
  console.log('原始查询参数:', query);
  console.log('参数类型检查:', {
    MyId: typeof MyId,
    otherId: typeof otherId,
    type: typeof type
  });

  if (!MyId || !otherId || type === undefined) {
    console.error('缺少必要的参数:', { MyId, otherId, type });
    throw createError({
      statusCode: 400,
      statusMessage: '缺少必要的参数: MyId, otherId, type',
    });
  }

  let filteredData = structuredClone(newsData);

  // 过滤双向消息：(MyId = 查询MyId AND otherId = 查询otherId) OR (MyId = 查询otherId AND otherId = 查询MyId), AND type = 查询type
  filteredData = filteredData.filter((item) =>
    String(item.type) === String(type) &&
    (
      (String(item.MyId) === String(MyId) && String(item.otherId) === String(otherId)) ||
      (String(item.MyId) === String(otherId) && String(item.otherId) === String(MyId))
    )
  );

  // 按创建时间正序排列
  filteredData.sort((a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime());

  // After filtering and sorting
  filteredData = filteredData.map(item => ({
    ...item,
    id: String(item.id),
    otherId: String(item.otherId),
    MyId: String(item.MyId),
    type: String(item.type),
  }));

  console.log('后端返回的数据:', filteredData);

  return {
    code: 200,
    data: filteredData,
    msg: 'ok',
  };
});
