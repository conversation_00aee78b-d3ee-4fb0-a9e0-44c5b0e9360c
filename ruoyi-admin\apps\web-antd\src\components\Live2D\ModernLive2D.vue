<template>
  <div class="modern-live2d-container" :class="{ 'is-mobile': isMobile }">
    <!-- Live2D Canvas -->
    <div ref="live2dContainer" class="live2d-canvas-container"></div>

    <!-- 提示框 -->
    <div ref="tipsContainer" class="live2d-tips" v-show="showTips">
      {{ currentTip }}
    </div>

    <!-- 工具栏 -->
    <div class="live2d-toolbar" v-show="mergedConfig.showToolbar">
      <button @click="switchModel" title="切换模型">
        <i class="icon-refresh"></i>
      </button>
      <button @click="switchTexture" title="切换衣服">
        <i class="icon-user"></i>
      </button>
      <button @click="showRandomMessage" title="一言">
        <i class="icon-chat"></i>
      </button>
      <button @click="takeScreenshot" title="截图">
        <i class="icon-camera"></i>
      </button>
      <button @click="toggleVisibility" title="隐藏">
        <i class="icon-close"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Application } from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display'
import { LIVE2D_CONFIG, getRandomMessage, getTimeBasedMessage, getDeviceConfig } from './config'

// 启用 Live2D 的 Cubism 2 和 Cubism 4 支持
import * as PIXI from 'pixi.js'
window.PIXI = PIXI

interface Props {
  config?: any
  modelPath?: string
  width?: number
  height?: number
  showToolbar?: boolean
  autoPlay?: boolean
  scale?: number
  position?: { x: number; y: number }
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  modelPath: '/live2d/真夜白音/真夜白音.model3.json',
  width: LIVE2D_CONFIG.width,
  height: LIVE2D_CONFIG.height,
  showToolbar: LIVE2D_CONFIG.showToolbar,
  autoPlay: LIVE2D_CONFIG.autoPlay,
  scale: LIVE2D_CONFIG.scale,
  position: () => LIVE2D_CONFIG.position
})

// 合并配置，优先使用config对象中的配置
const mergedConfig = {
  modelPath: props.config.modelPath || props.modelPath,
  width: props.config.width || props.width,
  height: props.config.height || props.height,
  showToolbar: props.config.showToolbar !== undefined ? props.config.showToolbar : props.showToolbar,
  autoPlay: props.config.autoPlay !== undefined ? props.config.autoPlay : props.autoPlay,
  scale: props.config.scale || props.scale,
  position: props.config.position || props.position,
  messages: props.config.messages || LIVE2D_CONFIG.messages,
  enableInteraction: props.config.enableInteraction !== undefined ? props.config.enableInteraction : LIVE2D_CONFIG.enableInteraction,
  enableMessages: props.config.enableMessages !== undefined ? props.config.enableMessages : LIVE2D_CONFIG.enableMessages
}

// 响应式数据
const live2dContainer = ref<HTMLElement>()
const tipsContainer = ref<HTMLElement>()
const showTips = ref(false)
const currentTip = ref('')
const isMobile = ref(false)

// PIXI 应用和模型
let app: Application | null = null
let model: Live2DModel | null = null
let currentModelIndex = 0
let currentTextureIndex = 0

// 可用的模型列表
const modelList = LIVE2D_CONFIG.models.map(m => m.path)

// 检测移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768

  // 根据设备调整配置
  const deviceConfig = getDeviceConfig()
  if (app && model) {
    model.scale.set(deviceConfig.scale)
    app.renderer.resize(deviceConfig.width, deviceConfig.height)
  }
}

// 初始化 PIXI 应用
const initPixiApp = async () => {
  if (!live2dContainer.value) return

  try {
    // 创建 PIXI 应用
    app = new Application({
      width: mergedConfig.width,
      height: mergedConfig.height,
      backgroundColor: 0x000000,
      backgroundAlpha: 0,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      autoDensity: true
    })

    // 将 canvas 添加到容器
    live2dContainer.value.appendChild(app.view as HTMLCanvasElement)

    // 加载 Live2D 模型
    await loadModel(mergedConfig.modelPath)

  } catch (error) {
    console.error('Failed to initialize PIXI app:', error)
  }
}

// 加载 Live2D 模型
const loadModel = async (modelPath: string) => {
  if (!app) return

  try {
    // 移除旧模型
    if (model) {
      app.stage.removeChild(model)
      model.destroy()
    }

    // 加载新模型
    model = await Live2DModel.from(modelPath)

    if (model) {
      // 设置模型属性
      model.scale.set(mergedConfig.scale)
      model.position.set(mergedConfig.position.x, mergedConfig.position.y)

      // 添加到舞台
      app.stage.addChild(model)

      // 设置交互
      if (mergedConfig.enableInteraction) {
        setupModelInteraction()
      }

      // 自动播放动画
      if (mergedConfig.autoPlay && model.internalModel?.motionManager) {
        model.motion('idle')
      }

      console.log('Live2D model loaded successfully')
      if (mergedConfig.enableMessages) {
        showMessage(getRandomMessage('system', 'modelLoaded'))
      }
    }

  } catch (error) {
    console.error('Failed to load Live2D model:', error)
    if (mergedConfig.enableMessages) {
      showMessage(getRandomMessage('system', 'modelLoadFailed'))
    }
  }
}

// 设置模型交互
const setupModelInteraction = () => {
  if (!model) return

  model.interactive = true
  model.buttonMode = true

  // 点击事件
  model.on('pointerdown', (event) => {
    const point = event.data.global

    // 检测点击区域
    if (model?.hitTest('head', point.x, point.y)) {
      if (mergedConfig.enableMessages) {
        // 优先使用配置中的消息，如果没有则使用默认消息
        const messages = mergedConfig.messages?.click?.head || LIVE2D_CONFIG.messages.click.head
        showMessage(messages[Math.floor(Math.random() * messages.length)])
      }
      model?.motion(LIVE2D_CONFIG.animations.tapHead)
    } else if (model?.hitTest('body', point.x, point.y)) {
      if (mergedConfig.enableMessages) {
        const messages = mergedConfig.messages?.click?.body || LIVE2D_CONFIG.messages.click.body
        showMessage(messages[Math.floor(Math.random() * messages.length)])
      }
      model?.motion(LIVE2D_CONFIG.animations.tapBody)
    }
  })
}

// 显示消息
const showMessage = (message: string, duration = 3000) => {
  currentTip.value = message
  showTips.value = true

  setTimeout(() => {
    showTips.value = false
  }, duration)
}

// 显示随机消息
const showRandomMessage = () => {
  if (!mergedConfig.enableMessages) return

  // 优先使用配置中的随机消息，如果没有则使用默认随机消息
  const messages = mergedConfig.messages?.random || LIVE2D_CONFIG.messages.random
  const randomTip = messages[Math.floor(Math.random() * messages.length)]
  showMessage(randomTip)
}

// 切换模型
const switchModel = async () => {
  if (modelList.length <= 1) {
    showMessage('暂时只有一个模型哦~')
    return
  }

  currentModelIndex = (currentModelIndex + 1) % modelList.length
  await loadModel(modelList[currentModelIndex])
  showMessage(getRandomMessage('system', 'modelSwitched'))
}

// 切换材质
const switchTexture = () => {
  if (!model || !model.internalModel.textures) {
    showMessage('当前模型不支持换装~')
    return
  }

  const textures = model.internalModel.textures
  if (textures.length <= 1) {
    showMessage('暂时没有其他衣服~')
    return
  }

  currentTextureIndex = (currentTextureIndex + 1) % textures.length
  // 这里需要根据具体的 Live2D 版本实现材质切换
  showMessage('换了新衣服！')
}

// 截图
const takeScreenshot = () => {
  if (!app) return

  try {
    // 获取用户选中的文字
    const selectedText = window.getSelection()?.toString() || '';

    const canvas = app.view as HTMLCanvasElement
    const link = document.createElement('a')

    // 如果有选中文字，则将文件名设置为选中的文字（限制长度防止文件名过长）
    const filename = selectedText
      ? `${selectedText.substring(0, 30)}.png`
      : 'live2d-screenshot.png';

    link.download = filename
    link.href = canvas.toDataURL()
    link.click()

    // 根据是否有选中文字显示不同的消息
    if (selectedText) {
      showMessage(`已保存「${selectedText.length > 10 ? selectedText.substring(0, 10) + '...' : selectedText}」的截图！`)
    } else {
      showMessage('截图保存成功！')
    }
  } catch (error) {
    console.error('Screenshot failed:', error)
    showMessage('截图失败了~')
  }
}

// 切换显示/隐藏
const toggleVisibility = () => {
  if (live2dContainer.value) {
    const container = live2dContainer.value.parentElement
    if (container) {
      const willBeHidden = container.style.display !== 'none'
      container.style.display = willBeHidden ? 'none' : 'block'

      // 保存状态到localStorage
      localStorage.setItem('live2d_hidden', willBeHidden ? 'true' : 'false')

      // 分发自定义事件，通知app.vue更新状态
      window.dispatchEvent(new CustomEvent('live2d-visibility-changed', {
        detail: { hidden: willBeHidden }
      }))

      if (willBeHidden) {
        showMessage('我先隐藏一下~')
      }
    }
  }
}

// 窗口大小改变处理（添加防抖）
let resizeTimer: number | null = null
const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = window.setTimeout(() => {
    checkMobile()
    if (app) {
      // 可以在这里调整 canvas 大小
    }
  }, 250) // 250ms 防抖
}



// 组件挂载
onMounted(async () => {
  // 初始化为默认模型的索引
  currentModelIndex = LIVE2D_CONFIG.models.findIndex(m => m.id === LIVE2D_CONFIG.defaultModel)
  if (currentModelIndex === -1) currentModelIndex = 0
  
  checkMobile()
  window.addEventListener('resize', handleResize)

  await nextTick()
  await initPixiApp()

  // 检查localStorage中的显示状态
  if (live2dContainer.value) {
    const container = live2dContainer.value.parentElement
    const hiddenState = localStorage.getItem('live2d_hidden')
    console.log('Live2D hidden state from localStorage:', hiddenState)
    if (container) {
      console.log('Live2D container display style:', container.style.display)
      if (hiddenState === 'true') {
        console.log('Live2D is hidden, showing it now')
        container.style.display = 'block'
        localStorage.setItem('live2d_hidden', 'false')
      }
    } else {
      console.error('Live2D container parent element not found')
    }
  } else {
    console.error('Live2D container element not found')
  }

  // 显示欢迎消息（仅当Live2D可见时）
  if (mergedConfig.enableMessages && mergedConfig.messages?.welcome &&
      live2dContainer.value &&
      live2dContainer.value.parentElement &&
      live2dContainer.value.parentElement.style.display !== 'none') {
    setTimeout(() => {
      const welcomeMessages = mergedConfig.messages.welcome;
      const randomWelcome = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)];
      showMessage(randomWelcome, 5000);
    }, 1000);
  }
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer)
    resizeTimer = null
  }

  if (model) {
    model.destroy()
  }

  if (app) {
    app.destroy(true)
  }
})

// 暴露方法给父组件
defineExpose({
  loadModel,
  showMessage,
  switchModel,
  switchTexture,
  takeScreenshot
})
</script>

<style scoped>
.modern-live2d-container {
  position: fixed;
  bottom: 0;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
  width: 300px;
  height: 400px;
  border: 1px solid red; /* 调试用边框 */
}

.live2d-canvas-container {
  position: relative;
  pointer-events: auto;
  width: 100%;
  height: 100%;
  border: 1px solid blue; /* 调试用边框 */
}

.live2d-tips {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  white-space: nowrap;
  max-width: 200px;
  text-align: center;
  animation: fadeInOut 0.3s ease-in-out;
  pointer-events: none;
}

.live2d-toolbar {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: auto;
}

.live2d-toolbar button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.live2d-toolbar button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

/* 移动端适配 */
.is-mobile {
  right: 10px;
  bottom: 10px;
}

.is-mobile .live2d-canvas-container {
  transform: scale(0.8);
  transform-origin: bottom right;
}

.is-mobile .live2d-toolbar {
  top: 5px;
  right: 5px;
}

.is-mobile .live2d-toolbar button {
  width: 28px;
  height: 28px;
}

/* 图标样式 */
.icon-refresh::before { content: "🔄"; }
.icon-user::before { content: "👕"; }
.icon-chat::before { content: "💬"; }
.icon-camera::before { content: "📷"; }
.icon-close::before { content: "❌"; }

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
  100% { opacity: 1; transform: translateX(-50%) translateY(0); }
}
</style>
