/**
 * Live2D External Embed Script
 * 用于在外部网站中嵌入Live2D模型
 */

(function() {
  // 配置参数
  const config = {
    // iframe的基本样式
    iframeStyle: {
      position: 'fixed',
      right: '0',
      bottom: '0',
      width: '280px',
      height: '300px',
      border: 'none',
      zIndex: '1000',
      pointerEvents: 'none' // 默认不拦截鼠标事件
    },
    // 浏览器窗口容器样式
    browserContainerStyle: {
      position: 'fixed',
      right: '20px',
      bottom: '20px',
      width: '80%',
      height: '80%',
      maxWidth: '800px',
      maxHeight: '600px',
      border: '1px solid #ccc',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      backgroundColor: '#fff',
      zIndex: '1001',
      overflow: 'hidden',
      display: 'none' // 默认隐藏
    },
    // 浏览器窗口标题栏样式
    browserTitleBarStyle: {
      height: '36px',
      backgroundColor: '#f5f5f5',
      borderBottom: '1px solid #e0e0e0',
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 10px',
      cursor: 'move'
    },
    // iframe的源URL
    srcUrl: '',
    // 是否自动加载
    autoLoad: true,
    // 是否显示工具栏
    showToolbar: true,
    // 默认模型ID
    defaultModelId: '',
    // 父页面的域名，用于安全验证
    parentDomain: window.location.origin
  };

  // 获取当前脚本的URL
  const getCurrentScriptUrl = function() {
    const scripts = document.getElementsByTagName('script');
    const currentScript = scripts[scripts.length - 1];
    return currentScript.src;
  };

  // 解析URL参数
  const parseUrlParams = function(url) {
    const params = {};
    const queryString = url.split('?')[1];
    if (queryString) {
      const pairs = queryString.split('&');
      for (let i = 0; i < pairs.length; i++) {
        const pair = pairs[i].split('=');
        params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
      }
    }
    return params;
  };

  // 从localStorage获取Live2D配置
  const getLive2DConfig = function() {
    console.log('live2d-external.js: 尝试从localStorage获取Live2D配置');
    try {
      // 尝试从live2dConfig键获取
      let configStr = localStorage.getItem('live2dConfig');
      if (configStr) {
        const config = JSON.parse(configStr);
        console.log('live2d-external.js: 从live2dConfig键获取到配置:', config);
        return config;
      } else {
        console.log('live2d-external.js: live2dConfig键中没有找到配置');
      }

      // 尝试从live2d_config键获取（兼容旧版本）
      configStr = localStorage.getItem('live2d_config');
      if (configStr) {
        const config = JSON.parse(configStr);
        console.log('live2d-external.js: 从live2d_config键获取到配置:', config);
        // 同时更新到新的键名
        localStorage.setItem('live2dConfig', configStr);
        return config;
      } else {
        console.log('live2d-external.js: live2d_config键中也没有找到配置');
      }
    } catch (e) {
      console.error('live2d-external.js: 解析Live2D配置时出错:', e);
    }
    console.log('live2d-external.js: 未找到任何Live2D配置');
    return null;
  };

  // 初始化配置
  const initConfig = function() {
    console.log('live2d-external.js: 开始初始化配置');

    const currentScriptUrl = getCurrentScriptUrl();
    console.log('live2d-external.js: 当前脚本URL:', currentScriptUrl);

    const urlParams = parseUrlParams(currentScriptUrl);
    console.log('live2d-external.js: URL参数:', urlParams);

    // 从URL参数更新配置
    if (urlParams.srcUrl) {
      config.srcUrl = urlParams.srcUrl;
      console.log('live2d-external.js: 设置srcUrl:', config.srcUrl);
    }
    if (urlParams.autoLoad) {
      config.autoLoad = urlParams.autoLoad !== 'false';
      console.log('live2d-external.js: 设置autoLoad:', config.autoLoad);
    }
    if (urlParams.showToolbar) {
      config.showToolbar = urlParams.showToolbar !== 'false';
      console.log('live2d-external.js: 设置showToolbar:', config.showToolbar);
    }
    if (urlParams.defaultModelId) {
      config.defaultModelId = urlParams.defaultModelId;
      console.log('live2d-external.js: 设置defaultModelId:', config.defaultModelId);
    }
    if (urlParams.width) {
      config.iframeStyle.width = urlParams.width + 'px';
      console.log('live2d-external.js: 设置width:', config.iframeStyle.width);
    }
    if (urlParams.height) {
      config.iframeStyle.height = urlParams.height + 'px';
      console.log('live2d-external.js: 设置height:', config.iframeStyle.height);
    }
    if (urlParams.right) {
      config.iframeStyle.right = urlParams.right + 'px';
      console.log('live2d-external.js: 设置right:', config.iframeStyle.right);
    }
    if (urlParams.bottom) {
      config.iframeStyle.bottom = urlParams.bottom + 'px';
      console.log('live2d-external.js: 设置bottom:', config.iframeStyle.bottom);
    }
    if (urlParams.zIndex) {
      config.iframeStyle.zIndex = urlParams.zIndex;
      console.log('live2d-external.js: 设置zIndex:', config.iframeStyle.zIndex);
    }

    // 如果没有指定srcUrl，则使用默认的
    if (!config.srcUrl) {
      // 获取当前脚本所在的基础URL
      const baseUrl = currentScriptUrl.substring(0, currentScriptUrl.lastIndexOf('/'));
      config.srcUrl = baseUrl + '/live2d-embed.html';
      console.log('live2d-external.js: 使用默认srcUrl:', config.srcUrl);
    }

    console.log('live2d-external.js: 配置初始化完成，最终配置:', JSON.stringify(config));
  };

  // 创建iframe元素
  const createIframe = function() {
    const iframe = document.createElement('iframe');
    iframe.id = 'live2d-external-iframe';

    // 设置iframe的样式
    Object.keys(config.iframeStyle).forEach(key => {
      iframe.style[key] = config.iframeStyle[key];
    });

    // 检查是否在起点中文网
    const isQidian = window.location.hostname.includes('qidian.com');
    console.log('live2d-external.js: 是否在起点中文网:', isQidian);

    // 构建iframe的src URL
    let srcUrl = config.srcUrl;

    // 如果在起点中文网，确保使用完整URL
     if (isQidian && !srcUrl.startsWith('http')) {
       // 使用与当前页面相同的协议
       const protocol = window.location.protocol;

       // 获取当前应用的实际域名，而不是硬编码
       // 在起点中文网环境下，使用固定的应用域名
       const appDomain = 'ruoyi-admin-production.up.railway.app';

       console.log('live2d-external.js: 使用的应用域名:', appDomain);
       srcUrl = `${protocol}//${appDomain}/live2d-embed.html`;
       console.log('live2d-external.js: 在起点中文网使用完整URL:', srcUrl);
     }

    if (config.defaultModelId) {
      srcUrl += (srcUrl.includes('?') ? '&' : '?') + 'modelId=' + encodeURIComponent(config.defaultModelId);
    }
    if (!config.showToolbar) {
      srcUrl += (srcUrl.includes('?') ? '&' : '?') + 'showToolbar=false';
    }

    iframe.src = srcUrl;
    iframe.frameBorder = '0';
    iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
    iframe.crossOrigin = 'anonymous'; // 添加跨域支持

    console.log('live2d-external.js: 创建iframe，src:', srcUrl);
    return iframe;
  };

  // 创建浏览器窗口容器
  const createBrowserContainer = function() {
    // 创建主容器
    const container = document.createElement('div');
    container.id = 'live2d-browser-container';

    // 设置容器样式
    Object.keys(config.browserContainerStyle).forEach(key => {
      container.style[key] = config.browserContainerStyle[key];
    });

    // 创建标题栏
    const titleBar = document.createElement('div');
    titleBar.id = 'live2d-browser-titlebar';

    // 设置标题栏样式
    Object.keys(config.browserTitleBarStyle).forEach(key => {
      titleBar.style[key] = config.browserTitleBarStyle[key];
    });

    // 添加标题文本
    const titleText = document.createElement('div');
    titleText.id = 'live2d-browser-title';
    titleText.textContent = '外部内容';
    titleText.style.fontWeight = 'bold';
    titleText.style.fontSize = '14px';
    titleText.style.overflow = 'hidden';
    titleText.style.textOverflow = 'ellipsis';
    titleText.style.whiteSpace = 'nowrap';
    titleText.style.maxWidth = 'calc(100% - 80px)';

    // 创建控制按钮容器
    const controlsContainer = document.createElement('div');
    controlsContainer.style.display = 'flex';
    controlsContainer.style.alignItems = 'center';

    // 添加最小化按钮
    const minButton = document.createElement('div');
    minButton.innerHTML = '&#8211;';
    minButton.title = '最小化';
    minButton.style.fontSize = '16px';
    minButton.style.cursor = 'pointer';
    minButton.style.width = '24px';
    minButton.style.height = '24px';
    minButton.style.display = 'flex';
    minButton.style.alignItems = 'center';
    minButton.style.justifyContent = 'center';
    minButton.style.borderRadius = '50%';
    minButton.style.transition = 'background-color 0.2s';
    minButton.style.marginRight = '4px';

    minButton.addEventListener('mouseover', function() {
      minButton.style.backgroundColor = '#e0e0e0';
    });

    minButton.addEventListener('mouseout', function() {
      minButton.style.backgroundColor = 'transparent';
    });

    minButton.addEventListener('click', function() {
      // 最小化窗口逻辑 - 暂时只是隐藏
      container.style.display = 'none';
    });

    // 添加最大化/还原按钮
    const maxButton = document.createElement('div');
    maxButton.innerHTML = '&#9744;';
    maxButton.title = '最大化';
    maxButton.style.fontSize = '14px';
    maxButton.style.cursor = 'pointer';
    maxButton.style.width = '24px';
    maxButton.style.height = '24px';
    maxButton.style.display = 'flex';
    maxButton.style.alignItems = 'center';
    maxButton.style.justifyContent = 'center';
    maxButton.style.borderRadius = '50%';
    maxButton.style.transition = 'background-color 0.2s';
    maxButton.style.marginRight = '4px';

    // 保存窗口原始尺寸和位置的变量
    let originalStyles = {};
    let isMaximized = false;

    maxButton.addEventListener('mouseover', function() {
      maxButton.style.backgroundColor = '#e0e0e0';
    });

    maxButton.addEventListener('mouseout', function() {
      maxButton.style.backgroundColor = 'transparent';
    });

    maxButton.addEventListener('click', function() {
      if (!isMaximized) {
        // 保存原始样式
        originalStyles = {
          width: container.style.width,
          height: container.style.height,
          top: container.style.top,
          left: container.style.left,
          right: container.style.right,
          bottom: container.style.bottom
        };

        // 最大化窗口
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.top = '0';
        container.style.left = '0';
        container.style.right = '0';
        container.style.bottom = '0';
        container.style.borderRadius = '0';
        container.style.maxWidth = 'none';
        container.style.maxHeight = 'none';

        // 更新按钮图标和提示
        maxButton.innerHTML = '&#9633;';
        maxButton.title = '还原';

        // 更新状态
        isMaximized = true;
      } else {
        // 还原窗口
        Object.keys(originalStyles).forEach(key => {
          container.style[key] = originalStyles[key];
        });
        container.style.borderRadius = '8px';

        // 更新按钮图标和提示
        maxButton.innerHTML = '&#9744;';
        maxButton.title = '最大化';

        // 更新状态
        isMaximized = false;
      }
    });

    // 添加关闭按钮
    const closeButton = document.createElement('div');
    closeButton.innerHTML = '&times;';
    closeButton.title = '关闭';
    closeButton.style.fontSize = '20px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.width = '24px';
    closeButton.style.height = '24px';
    closeButton.style.display = 'flex';
    closeButton.style.alignItems = 'center';
    closeButton.style.justifyContent = 'center';
    closeButton.style.borderRadius = '50%';
    closeButton.style.transition = 'background-color 0.2s';

    closeButton.addEventListener('mouseover', function() {
      closeButton.style.backgroundColor = '#e0e0e0';
    });

    closeButton.addEventListener('mouseout', function() {
      closeButton.style.backgroundColor = 'transparent';
    });

    closeButton.addEventListener('click', function() {
      container.style.display = 'none';
    });

    // 将控制按钮添加到控制容器
    controlsContainer.appendChild(minButton);
    controlsContainer.appendChild(maxButton);
    controlsContainer.appendChild(closeButton);

    // 将标题文本和控制按钮添加到标题栏
    titleBar.appendChild(titleText);
    titleBar.appendChild(controlsContainer);

    // 添加标题栏到容器
    container.appendChild(titleBar);

    // 创建加载指示器
    const loadingIndicator = document.createElement('div');
    loadingIndicator.id = 'live2d-browser-loading';
    loadingIndicator.style.position = 'absolute';
    loadingIndicator.style.top = '50%';
    loadingIndicator.style.left = '50%';
    loadingIndicator.style.transform = 'translate(-50%, -50%)';
    loadingIndicator.style.fontSize = '16px';
    loadingIndicator.style.color = '#666';
    loadingIndicator.textContent = '加载中...';
    loadingIndicator.style.display = 'none';

    container.appendChild(loadingIndicator);

    // 创建内容iframe
    const contentIframe = document.createElement('iframe');
    contentIframe.id = 'live2d-browser-content';
    contentIframe.style.width = '100%';
    contentIframe.style.height = 'calc(100% - 36px)';
    contentIframe.style.border = 'none';
    contentIframe.frameBorder = '0';
    contentIframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';

    // 添加iframe加载事件
    contentIframe.addEventListener('load', function() {
      loadingIndicator.style.display = 'none';
      contentIframe.style.display = 'block';

      // 更新标题
      try {
        const iframeTitle = contentIframe.contentDocument.title;
        if (iframeTitle) {
          titleText.textContent = iframeTitle;
          titleText.title = iframeTitle; // 添加悬停提示
        }
      } catch (e) {
        // 跨域限制可能会导致无法访问contentDocument
        console.log('无法获取iframe标题:', e);
      }
    });

    contentIframe.addEventListener('error', function() {
      loadingIndicator.textContent = '加载失败';
    });

    // 添加内容iframe到容器
    container.appendChild(contentIframe);

    // 实现拖动功能
    let isDragging = false;
    let offsetX, offsetY;

    titleBar.addEventListener('mousedown', function(e) {
      // 如果点击的是控制按钮，不启动拖动
      if (e.target === minButton || e.target === maxButton || e.target === closeButton) {
        return;
      }

      isDragging = true;
      offsetX = e.clientX - container.getBoundingClientRect().left;
      offsetY = e.clientY - container.getBoundingClientRect().top;

      // 防止拖动时选中文本
      e.preventDefault();
    });

    document.addEventListener('mousemove', function(e) {
      if (isDragging) {
        // 如果窗口最大化状态，则不允许拖动
        if (isMaximized) return;

        container.style.left = (e.clientX - offsetX) + 'px';
        container.style.top = (e.clientY - offsetY) + 'px';
        // 移动时取消right和bottom定位
        container.style.right = 'auto';
        container.style.bottom = 'auto';
      }
    });

    document.addEventListener('mouseup', function() {
      isDragging = false;
    });

    // 添加双击标题栏最大化/还原功能
    titleBar.addEventListener('dblclick', function(e) {
      // 如果点击的是控制按钮，不处理双击
      if (e.target === minButton || e.target === maxButton || e.target === closeButton) {
        return;
      }

      // 触发最大化按钮的点击事件
      maxButton.click();
    });

    return container;
  };

  // 添加iframe到页面
  const addIframeToPage = function() {
    const iframe = createIframe();
    document.body.appendChild(iframe);

    // 创建浏览器容器并添加到页面
    const browserContainer = createBrowserContainer();
    document.body.appendChild(browserContainer);

    // 设置iframe的鼠标事件处理
    iframe.addEventListener('mouseenter', function() {
      iframe.style.pointerEvents = 'auto'; // 鼠标悬停时允许交互
    });

    iframe.addEventListener('mouseleave', function() {
      iframe.style.pointerEvents = 'none'; // 鼠标离开时禁止交互
    });

    // 点击iframe时打开浏览器窗口
    iframe.addEventListener('click', function() {
      // 获取当前页面URL
      const currentUrl = window.location.href;

      // 设置浏览器窗口内容
      const contentIframe = document.getElementById('live2d-browser-content');
      if (contentIframe) {
        contentIframe.src = currentUrl;
        
        // 显示浏览器窗口
        const browserContainer = document.getElementById('live2d-browser-container');
        if (browserContainer) {
          browserContainer.style.display = 'block';
        }
      }
    });

    return iframe;
  };

  // 与iframe通信
  const setupCommunication = function(iframe) {
    console.log('live2d-external.js: 设置与iframe的通信');

    // 监听来自iframe的消息
    window.addEventListener('message', function(event) {
      console.log('live2d-external.js: 收到消息:', event.data);

      // 安全检查：确保消息来自我们的iframe或浏览器窗口iframe
      const browserContentIframe = document.getElementById('live2d-browser-content');
      if (event.source !== iframe.contentWindow &&
          (!browserContentIframe || event.source !== browserContentIframe.contentWindow)) {
        console.log('live2d-external.js: 消息不是来自我们的iframe，忽略');
        return;
      }

      // 处理不同类型的消息
      if (event.data && event.data.type) {
        console.log('live2d-external.js: 处理消息类型:', event.data.type);

        switch (event.data.type) {
          case 'live2d-loaded':
            console.log('live2d-external.js: Live2D模型已加载:', event.data.modelName);

            // 获取Live2D配置
            const config = getLive2DConfig() || {};
            console.log('live2d-external.js: 获取到的Live2D配置:', config);

            // 检查是否在起点中文网
            const isQidian = window.location.hostname.includes('qidian.com');

            // 显示欢迎消息
            if (config.bookName && config.chapterName) {
              console.log('live2d-external.js: 准备显示书名和章节名消息');
              setTimeout(() => {
                try {
                  iframe.contentWindow.postMessage({
                    type: 'show-message',
                    text: `正在阅读《${config.bookName}》${config.chapterName}`,
                    timeout: 5000
                  }, '*');
                  console.log('live2d-external.js: 已发送欢迎消息');
                } catch (e) {
                  console.error('live2d-external.js: 发送消息时出错:', e);
                }
              }, 1000);
            } else if (isQidian) {
              // 在起点中文网尝试获取书名和章节名
              console.log('live2d-external.js: 在起点中文网尝试获取书名和章节名');
              const bookTitle = document.querySelector('.book-info-title') ?
                              document.querySelector('.book-info-title').textContent.trim() : '';
              const chapterTitle = document.querySelector('.j_chapterName') ?
                                 document.querySelector('.j_chapterName').textContent.trim() : '';

              if (bookTitle && chapterTitle) {
                console.log('live2d-external.js: 从页面获取到书名和章节名:', bookTitle, chapterTitle);
                setTimeout(() => {
                  try {
                    iframe.contentWindow.postMessage({
                      type: 'show-message',
                      text: `正在阅读《${bookTitle}》${chapterTitle}`,
                      timeout: 5000
                    }, '*');
                    console.log('live2d-external.js: 已发送起点中文网欢迎消息');
                  } catch (e) {
                    console.error('live2d-external.js: 发送消息时出错:', e);
                  }
                }, 1000);
              }
            }
            break;
          // 可以添加更多消息类型的处理
          default:
            console.log('live2d-external.js: 未处理的消息类型:', event.data.type);
        }
      } else {
        console.log('live2d-external.js: 收到的消息没有type字段或为空');
      }
    });

    // 提供API方法
    console.log('live2d-external.js: 创建API方法');

    // 安全发送消息的辅助函数
    const safelySendMessage = function(message) {
      try {
        if (iframe && iframe.contentWindow) {
          console.log('live2d-external.js: 发送消息:', message);
          iframe.contentWindow.postMessage(message, '*');
          return true;
        } else {
          console.error('live2d-external.js: iframe或contentWindow不存在');
          return false;
        }
      } catch (e) {
        console.error('live2d-external.js: 发送消息时出错:', e);
        return false;
      }
    };

    // 打开浏览器窗口并加载URL
    const openBrowserWindow = function(url) {
      const browserContainer = document.getElementById('live2d-browser-container');
      const contentIframe = document.getElementById('live2d-browser-content');
      const loadingIndicator = document.getElementById('live2d-browser-loading');
      const titleText = document.getElementById('live2d-browser-title');

      if (browserContainer && contentIframe) {
        // 显示加载指示器
        if (loadingIndicator) {
          loadingIndicator.textContent = '加载中...';
          loadingIndicator.style.display = 'block';
        }

        // 更新标题
        if (titleText) {
          titleText.textContent = '加载中...';
        }

        // 设置URL
        contentIframe.src = url || window.location.href;

        // 显示容器
        browserContainer.style.display = 'block';

        // 如果容器已经有位置信息，则不重新定位
        if (!browserContainer.style.top && !browserContainer.style.left) {
          // 居中显示窗口
          const windowWidth = window.innerWidth;
          const windowHeight = window.innerHeight;
          const containerWidth = parseInt(browserContainer.style.width) || windowWidth * 0.8;
          const containerHeight = parseInt(browserContainer.style.height) || windowHeight * 0.8;

          browserContainer.style.left = Math.max(0, (windowWidth - containerWidth) / 2) + 'px';
          browserContainer.style.top = Math.max(0, (windowHeight - containerHeight) / 2) + 'px';
          browserContainer.style.right = 'auto';
          browserContainer.style.bottom = 'auto';
        }

        return true;
      }
      return false;
    };

    return {
      // 切换到指定模型
      switchToModel: function(modelId) {
        console.log('live2d-external.js: 切换到模型:', modelId);
        return safelySendMessage({
          type: 'switch-model',
          modelId: modelId
        });
      },
      // 显示消息
      showMessage: function(text, timeout) {
        console.log('live2d-external.js: 显示消息:', text, '超时:', timeout || 3000);
        return safelySendMessage({
          type: 'show-message',
          text: text,
          timeout: timeout || 3000
        });
      },
      // 切换显示/隐藏
      toggleVisibility: function() {
        console.log('live2d-external.js: 切换显示/隐藏');
        return safelySendMessage({
          type: 'toggle-visibility'
        });
      },
      // 截图
      takeScreenshot: function() {
        console.log('live2d-external.js: 截图');
        return safelySendMessage({
          type: 'take-screenshot'
        });
      },
      // 获取iframe元素
      getIframe: function() {
        return iframe;
      },
      // 检查是否已加载
      isLoaded: function() {
        return !!(iframe && iframe.contentWindow);
      },
      // 打开浏览器窗口
      openBrowserWindow: function(url) {
        console.log('live2d-external.js: 打开浏览器窗口，URL:', url || '当前页面');
        return openBrowserWindow(url);
      },
      // 关闭浏览器窗口
      closeBrowserWindow: function() {
        console.log('live2d-external.js: 关闭浏览器窗口');
        const browserContainer = document.getElementById('live2d-browser-container');
        if (browserContainer) {
          browserContainer.style.display = 'none';
          return true;
        }
        return false;
      }
    };
  };

  // 初始化Live2D
  const initLive2D = function() {
    console.log('live2d-external.js: 开始初始化Live2D');

    try {
      // 初始化配置
      initConfig();
      console.log('live2d-external.js: 配置初始化完成:', JSON.stringify(config));

      // 检查是否在起点中文网
      const isQidian = window.location.hostname.includes('qidian.com');
      console.log('live2d-external.js: 是否在起点中文网:', isQidian);

      // 如果不自动加载，则返回
      if (!config.autoLoad) {
        console.log('live2d-external.js: autoLoad为false，不自动加载Live2D模型');
        return null;
      }

      // 添加iframe到页面
      console.log('live2d-external.js: 准备添加iframe到页面');
      const iframe = addIframeToPage();
      console.log('live2d-external.js: iframe已添加到页面');

      // 添加iframe加载事件监听
      iframe.onload = function() {
        console.log('live2d-external.js: iframe已加载完成');
      };

      iframe.onerror = function(error) {
        console.error('live2d-external.js: iframe加载失败:', error);
      };

      // 设置通信并返回API
      console.log('live2d-external.js: 设置iframe通信');
      const api = setupCommunication(iframe);

      // 在起点中文网上，添加额外的处理
      if (isQidian) {
        console.log('live2d-external.js: 在起点中文网上添加额外处理');

        // 确保iframe可见
        setTimeout(() => {
          if (iframe) {
            console.log('live2d-external.js: 确保iframe在起点中文网上可见');
            iframe.style.display = 'block';
            iframe.style.visibility = 'visible';
            iframe.style.opacity = '1';
          }
        }, 2000);
      }

      // 添加点击事件提示
      setTimeout(() => {
        if (api) {
          api.showMessage('点击我可以打开浏览器窗口哦！', 5000);
        }
      }, 3000);

      console.log('live2d-external.js: Live2D初始化完成');
      return api;
    } catch (e) {
      console.error('live2d-external.js: 初始化Live2D时出错:', e);
      return null;
    }
  };

  // 将API暴露到全局
  window.Live2DExternal = initLive2D();

  // 添加全局快捷方式
  window.openLive2DBrowser = function(url) {
    if (window.Live2DExternal && window.Live2DExternal.openBrowserWindow) {
      return window.Live2DExternal.openBrowserWindow(url);
    }
    return false;
  };

  window.closeLive2DBrowser = function() {
    if (window.Live2DExternal && window.Live2DExternal.closeBrowserWindow) {
      return window.Live2DExternal.closeBrowserWindow();
    }
    return false;
  };
})();
