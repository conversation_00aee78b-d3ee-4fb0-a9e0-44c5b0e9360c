import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:book-open',
      order: -1,
      title: $t('page.noval.title'),
    },
    name: 'Nova<PERSON>',
    path: '/noval',
    children: [
      {
        name: 'NovalForYh',
        path: '/noval/NovalForYh',
        component: () => import('#/views/noval/NovalForYh/index.vue'),
        meta: {
          icon: 'lucide:library',
          title: $t('page.noval.list'),
        },
      },
      {
        name: 'NovalDetail',
        path: '/noval/detail/:id',
        component: () => import('#/views/noval/detail/index.vue'),
        meta: {
          icon: 'lucide:book',
          title: $t('page.noval.detail'),
          hideInMenu: true,
        },
      },
      {
        name: 'NovalForYhWithParams',
        path: '/noval/NovalForYh/:platform/:type',
        component: () => import('#/views/noval/NovalForYh/index.vue'),
        meta: {
          icon: 'lucide:library',
          title: $t('page.noval.list'),
          hideInMenu: true,
        },
      },
      {
        name: 'CommentSection',
        path: '/noval/comment-section',
        component: () => import('#/views/noval/comment-section/index.vue'),
        meta: {
          icon: 'lucide:message-circle',
          title: '评论区',
          hideInMenu: true,
        },
      },
    ],
  },
  {
    meta: {
      icon: 'lucide:message-circle',
      title: '评论区',
      hideInMenu: true,
    },
    name: 'CommentForYh',
    path: '/CommentForYh',
    children: [
      {
        name: 'CommentForYhSection',
        path: '/CommentForYh/comment-section',
        component: () => import('#/views/noval/comment-section/index.vue'),
        meta: {
          icon: 'lucide:message-circle',
          title: '评论区',
          hideInMenu: true,
        },
      },
    ],
  },
  {
    meta: {
      hideInMenu: true,
    },
    name: 'ExternalLink',
    path: '/noval/external-link',
    component: () => import('#/views/noval/external-link/index.vue'),
  },
  {
    meta: {
      hideInMenu: true,
    },
    name: 'ExternalLinkWithParam',
    path: '/test/external-link/:url',
    component: () => import('#/views/noval/external-link/index.vue'),
  },
  {
    meta: {
      title: '外链嵌入示例',
    },
    name: 'ExternalLinkDemo',
    path: '/noval/external-link-demo',
    component: () => import('#/views/noval/external-link/demo.vue'),
  },
  {
    meta: {
      hideInMenu: true,
    },
    name: 'AjkxReader',
    path: '/noval/ajkx',
    component: () => import('#/views/noval/external-link/index.vue'),
  },
  {
    meta: {
      hideInMenu: true,
      title: '章节内容',
    },
    name: 'ChapterContent',
    path: '/noval/chapter/:ossId',
    component: () => import('#/views/noval/chapter-content/index.vue'),
  },
];

export default routes;
