import {
  column_default
} from "./chunk-O43HGY5K.js";
import "./chunk-YB3DXOVJ.js";
import "./chunk-X4FLHRLX.js";
import "./chunk-EKP7TM2V.js";
import {
  VxeUI
} from "./chunk-BCXSQRR2.js";
import "./chunk-PJ5U4TG7.js";
import "./chunk-GE6DY3YU.js";
import "./chunk-KT3WABTJ.js";
import "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/column/index.js
var VxeColumn = Object.assign({}, column_default, {
  install(app) {
    app.component(column_default.name, column_default);
    app.component("VxeTableColumn", column_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(column_default.name, column_default);
  VxeUI.dynamicApp.component("VxeTableColumn", column_default);
}
VxeUI.component(column_default);
var Column = VxeColumn;
var column_default2 = VxeColumn;

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/vxe-column/index.js
var vxe_column_default = column_default2;
export {
  Column,
  VxeColumn,
  vxe_column_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-column_index__js.js.map
