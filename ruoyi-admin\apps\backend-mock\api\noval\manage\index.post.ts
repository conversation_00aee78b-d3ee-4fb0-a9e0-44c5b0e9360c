import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { getNextManageId, addManage } from './data';
import type { ManageVO } from '#/api/noval/manage/model';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);

  const newId = getNextManageId();

  const newManage: ManageVO = {
    id: newId,
    name: body.name || '',
    flag: body.flag || '',
    author: body.author || '',
    createTime: new Date().toISOString(),
  };

  addManage(newManage);

  return { code: 200, data: newManage, msg: 'ok' };
});
