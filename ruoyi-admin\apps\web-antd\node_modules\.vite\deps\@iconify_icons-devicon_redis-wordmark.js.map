{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@iconify+icons-devicon@1.2.17/node_modules/@iconify/icons-devicon/redis-wordmark.js"], "sourcesContent": ["const data = {\n\t\"width\": 128,\n\t\"height\": 128,\n\t\"body\": \"<path fill=\\\"#636466\\\" d=\\\"M21.4 97.6c0 1.8-1.5 3.5-3.5 3.5c-1.5 0-2.8.4-4 1.3c-1.3.8-2.2 1.9-3 3.2c-1.6 2.1-2.4 4.6-2.7 5.5v12.5c0 1.9-1.6 3.5-3.6 3.5c-1.9 0-3.5-1.6-3.5-3.5v-26c0-1.9 1.6-3.4 3.5-3.4c2 0 3.6 1.5 3.6 3.4v.5c.4-.5.9-1 1.4-1.3c2.2-1.4 5-2.6 8.3-2.6c2 0 3.5 1.5 3.5 3.4zm-1.9 13c.1-9 7-16.5 16.1-16.5c8.6 0 15.3 6.4 15.9 15.3v.3c0 .1 0 .5-.1.6c-.2 1.6-1.6 2.6-3.4 2.6H27c.3 1.5 1.1 3.2 2.2 4.3c1.4 1.6 4 2.8 6.3 3c2.4.2 5.2-.4 6.8-1.6c1.4-1.4 4.1-1.3 4.9-.2c.9.9 1.5 2.9 0 4.3c-3.2 3-7.1 4.3-11.8 4.3c-8.9 0-15.9-7.5-15.9-16.4zm7.1-3.2h18.6c-.7-2.6-4-6.5-9.7-7c-5.6.2-8.3 4.3-8.9 7zm58.3 16.1c0 1.9-1.6 3.6-3.6 3.6c-1.8 0-3.2-1.3-3.5-2.8c-2.5 1.7-5.7 2.8-9 2.8c-8.9 0-16-7.5-16-16.4c0-9 7.1-16.5 16-16.5c3.2 0 6.4 1.1 8.8 2.8V84.5c0-1.9 1.6-3.6 3.6-3.6s3.6 1.6 3.6 3.6v26.2l.1 12.8zm-16-22.2c-2.4 0-4.5 1-6.2 2.7c-1.6 1.6-2.6 4-2.6 6.6c0 2.5 1 4.9 2.6 6.5c1.6 1.7 3.8 2.7 6.2 2.7c2.4 0 4.5-1 6.2-2.7c1.6-1.6 2.6-4 2.6-6.5c0-2.6-1-5-2.6-6.6c-1.6-1.7-3.7-2.7-6.2-2.7zm28.6-15.4c0 2-1.5 3.6-3.6 3.6c-2 0-3.6-1.6-3.6-3.6v-1.4c0-2 1.6-3.6 3.6-3.6s3.6 1.6 3.6 3.6v1.4zm0 11.9v25.7c0 2-1.5 3.6-3.6 3.6c-2 0-3.6-1.6-3.6-3.6V97.8c0-2.1 1.6-3.6 3.6-3.6c2.1 0 3.6 1.5 3.6 3.6zm4.5 19.8c1.2-1.6 3.5-1.8 4.9-.5c1.7 1.4 4.7 3 7.2 2.9c1.8 0 3.4-.6 4.5-1.3c.9-.8 1.2-1.4 1.2-2c0-.3-.1-.5-.2-.7c-.1-.2-.3-.5-.9-.8c-.9-.7-2.9-1.4-5.3-1.8h-.1c-2-.4-4-.9-5.7-1.7c-1.8-.9-3.4-2-4.5-3.8c-.7-1.2-1.1-2.6-1.1-4.1c0-3 1.7-5.6 3.9-7.2c2.3-1.6 5.1-2.4 8.1-2.4c4.5 0 7.8 2.2 9.9 3.6c1.6 1.1 2 3.2 1.1 4.9c-1.1 1.6-3.2 2-4.9.9c-2.1-1.4-4-2.4-6.1-2.4c-1.6 0-3.1.5-4 1.2c-.9.6-1.1 1.2-1.1 1.5c0 .3 0 .3.1.5c.1.1.3.4.7.7c.9.6 2.6 1.2 4.8 1.6l.1.1h.1c2.2.4 4.2 1 6.1 1.9c1.8.8 3.6 2 4.7 3.9c.8 1.3 1.3 2.8 1.3 4.3c0 3.2-1.8 5.9-4.1 7.6c-2.4 1.6-5.3 2.6-8.6 2.6c-5.1-.1-9.1-2.4-11.7-4.5c-1.4-1.3-1.6-3.5-.4-5z\\\"/><path fill=\\\"#A41E11\\\" d=\\\"M106.9 62.7c-5 2.6-30.7 13.2-36.2 16c-5.5 2.9-8.5 2.8-12.8.8c-4.4-2.1-31.7-13.1-36.7-15.5c-2.5-1.2-3.8-2.2-3.8-3.1v-9.4s35.6-7.8 41.4-9.8c5.8-2.1 7.8-2.1 12.6-.3c4.9 1.8 34.2 7.1 39 8.8v9.3c.1.9-1 1.9-3.5 3.2z\\\"/><path fill=\\\"#D82C20\\\" d=\\\"M106.9 53.3c-5 2.6-30.7 13.2-36.2 16c-5.5 2.9-8.5 2.8-12.8.8C53.5 68 26.2 57 21.2 54.6c-4.9-2.4-5-4-.2-5.9c4.8-1.9 32.1-12.6 37.8-14.6c5.8-2.1 7.8-2.1 12.6-.3c4.9 1.8 30.5 12 35.3 13.7c5 1.8 5.2 3.2.2 5.8z\\\"/><path fill=\\\"#A41E11\\\" d=\\\"M106.9 47.4c-5 2.6-30.7 13.2-36.2 16c-5.5 2.9-8.5 2.8-12.8.8c-4.4-2.1-31.7-13.2-36.7-15.5c-2.5-1.2-3.8-2.2-3.8-3.1v-9.4s35.6-7.8 41.4-9.8c5.8-2.1 7.8-2.1 12.6-.3c4.9 1.8 34.2 7.1 39 8.8v9.3c.1.9-1 1.9-3.5 3.2z\\\"/><path fill=\\\"#D82C20\\\" d=\\\"M106.9 38c-5 2.6-30.7 13.2-36.2 16c-5.5 2.9-8.5 2.8-12.8.8c-4.3-2.1-31.7-13.1-36.6-15.5c-4.9-2.4-5-4-.2-5.9c4.8-1.9 32.1-12.6 37.8-14.6c5.8-2.1 7.8-2.1 12.6-.3c4.9 1.8 30.5 12 35.3 13.7c4.9 1.7 5.1 3.2.1 5.8z\\\"/><path fill=\\\"#A41E11\\\" d=\\\"M106.9 31.5c-5 2.6-30.7 13.2-36.2 16c-5.5 2.9-8.5 2.8-12.8.8c-4.3-2.1-31.7-13.1-36.6-15.5c-2.5-1.2-3.8-2.2-3.8-3.1v-9.4s35.6-7.8 41.4-9.8c5.8-2.1 7.8-2.1 12.6-.3c4.9 1.8 34.2 7.1 39 8.8v9.3c0 .8-1.1 1.9-3.6 3.2z\\\"/><path fill=\\\"#D82C20\\\" d=\\\"M106.9 22.1c-5 2.6-30.7 13.2-36.2 16c-5.5 2.9-8.5 2.8-12.8.8c-4.3-2.1-31.7-13.1-36.6-15.5s-5-4-.2-5.9c4.8-1.9 32.1-12.6 37.8-14.6C64.7.8 66.7.8 71.5 2.6c4.9 1.8 30.5 12 35.3 13.7c4.9 1.7 5.1 3.2.1 5.8z\\\"/><path fill=\\\"#fff\\\" d=\\\"m76.2 13l-8.1.8l-1.8 4.4l-2.9-4.9l-9.3-.8L61 10l-2-3.8l6.5 2.5l6.1-2l-1.7 4zM65.8 34.1l-15-6.3l21.6-3.3z\\\"/><ellipse cx=\\\"45\\\" cy=\\\"19.9\\\" fill=\\\"#fff\\\" rx=\\\"11.5\\\" ry=\\\"4.5\\\"/><path fill=\\\"#7A0C00\\\" d=\\\"m85.7 14.2l12.8 5l-12.8 5.1z\\\"/><path fill=\\\"#AD2115\\\" d=\\\"m71.6 19.8l14.1-5.6v10.1l-1.3.5z\\\"/>\"\n};\nexports.__esModule = true;\nexports.default = data;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAM,OAAO;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AACA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAAA;AAAA;", "names": []}