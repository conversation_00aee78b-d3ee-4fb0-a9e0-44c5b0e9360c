{"Version": 3, "Meta": {"Duration": 3.2, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 15, "TotalSegmentCount": 237, "TotalPointCount": 672, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param122", "Segments": [0, 12, 0, 1.067, 24, 0, 2.133, 36, 0, 3.2, 48]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 12, 0, 1.067, 24, 0, 2.133, 36, 0, 3.2, 48]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 1, 1, 0.089, 1, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 1, 0.533, 1, 1, 0.622, 1, 0.711, 0, 0.8, 0, 1, 0.889, 0, 0.978, 1, 1.067, 1, 1, 1.156, 1, 1.244, 0, 1.333, 0, 1, 1.422, 0, 1.511, 1, 1.6, 1, 1, 1.689, 1, 1.778, 0, 1.867, 0, 1, 1.956, 0, 2.044, 1, 2.133, 1, 1, 2.222, 1, 2.311, 0, 2.4, 0, 1, 2.489, 0, 2.578, 1, 2.667, 1, 1, 2.756, 1, 2.844, 0, 2.933, 0, 1, 3.022, 0, 3.111, 1, 3.2, 1]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0.908, 1, 0.017, 0.965, 0.033, 1, 0.05, 1, 1, 0.139, 1, 0.228, 0, 0.317, 0, 1, 0.406, 0, 0.494, 1, 0.583, 1, 1, 0.672, 1, 0.761, 0, 0.85, 0, 1, 0.939, 0, 1.028, 1, 1.117, 1, 1, 1.206, 1, 1.294, 0, 1.383, 0, 1, 1.472, 0, 1.561, 1, 1.65, 1, 1, 1.739, 1, 1.828, 0, 1.917, 0, 1, 2.006, 0, 2.094, 1, 2.183, 1, 1, 2.272, 1, 2.361, 0, 2.45, 0, 1, 2.539, 0, 2.628, 1, 2.717, 1, 1, 2.806, 1, 2.894, 0, 2.983, 0, 1, 3.056, 0, 3.128, 0.66, 3.2, 0.908]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0.407, 1, 0.039, 0.191, 0.078, 0, 0.117, 0, 1, 0.206, 0, 0.294, 1, 0.383, 1, 1, 0.472, 1, 0.561, 0, 0.65, 0, 1, 0.739, 0, 0.828, 1, 0.917, 1, 1, 1.006, 1, 1.094, 0, 1.183, 0, 1, 1.272, 0, 1.361, 1, 1.45, 1, 1, 1.539, 1, 1.628, 0, 1.717, 0, 1, 1.806, 0, 1.894, 1, 1.983, 1, 1, 2.072, 1, 2.161, 0, 2.25, 0, 1, 2.339, 0, 2.428, 1, 2.517, 1, 1, 2.606, 1, 2.694, 0, 2.783, 0, 1, 2.872, 0, 2.961, 1, 3.05, 1, 1, 3.1, 1, 3.15, 0.684, 3.2, 0.407]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0.156, 1, 0.067, 0.438, 0.133, 1, 0.2, 1, 1, 0.289, 1, 0.378, 0, 0.467, 0, 1, 0.556, 0, 0.644, 1, 0.733, 1, 1, 0.822, 1, 0.911, 0, 1, 0, 1, 1.089, 0, 1.178, 1, 1.267, 1, 1, 1.356, 1, 1.444, 0, 1.533, 0, 1, 1.622, 0, 1.711, 1, 1.8, 1, 1, 1.889, 1, 1.978, 0, 2.067, 0, 1, 2.156, 0, 2.244, 1, 2.333, 1, 1, 2.422, 1, 2.511, 0, 2.6, 0, 1, 2.689, 0, 2.778, 1, 2.867, 1, 1, 2.956, 1, 3.044, 0, 3.133, 0, 1, 3.156, 0, 3.178, 0.063, 3.2, 0.156]}, {"Target": "Parameter", "Id": "Param309", "Segments": [0, 0.684, 1, 0.033, 0.859, 0.067, 1, 0.1, 1, 1, 0.189, 1, 0.278, 0, 0.367, 0, 1, 0.456, 0, 0.544, 1, 0.633, 1, 1, 0.722, 1, 0.811, 0, 0.9, 0, 1, 0.989, 0, 1.078, 1, 1.167, 1, 1, 1.256, 1, 1.344, 0, 1.433, 0, 1, 1.522, 0, 1.611, 1, 1.7, 1, 1, 1.789, 1, 1.878, 0, 1.967, 0, 1, 2.056, 0, 2.144, 1, 2.233, 1, 1, 2.322, 1, 2.411, 0, 2.5, 0, 1, 2.589, 0, 2.678, 1, 2.767, 1, 1, 2.856, 1, 2.944, 0, 3.033, 0, 1, 3.089, 0, 3.144, 0.391, 3.2, 0.684]}, {"Target": "Parameter", "Id": "Param311", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1, 0.267, 1, 1, 0.356, 1, 0.444, 0, 0.533, 0, 1, 0.622, 0, 0.711, 1, 0.8, 1, 1, 0.889, 1, 0.978, 0, 1.067, 0, 1, 1.156, 0, 1.244, 1, 1.333, 1, 1, 1.422, 1, 1.511, 0, 1.6, 0, 1, 1.689, 0, 1.778, 1, 1.867, 1, 1, 1.956, 1, 2.044, 0, 2.133, 0, 1, 2.222, 0, 2.311, 1, 2.4, 1, 1, 2.489, 1, 2.578, 0, 2.667, 0, 1, 2.756, 0, 2.844, 1, 2.933, 1, 1, 3.022, 1, 3.111, 0, 3.2, 0]}, {"Target": "Parameter", "Id": "Param312", "Segments": [0, 0.407, 1, 0.039, 0.191, 0.078, 0, 0.117, 0, 1, 0.206, 0, 0.294, 1, 0.383, 1, 1, 0.472, 1, 0.561, 0, 0.65, 0, 1, 0.739, 0, 0.828, 1, 0.917, 1, 1, 1.006, 1, 1.094, 0, 1.183, 0, 1, 1.272, 0, 1.361, 1, 1.45, 1, 1, 1.539, 1, 1.628, 0, 1.717, 0, 1, 1.806, 0, 1.894, 1, 1.983, 1, 1, 2.072, 1, 2.161, 0, 2.25, 0, 1, 2.339, 0, 2.428, 1, 2.517, 1, 1, 2.606, 1, 2.694, 0, 2.783, 0, 1, 2.872, 0, 2.961, 1, 3.05, 1, 1, 3.1, 1, 3.15, 0.684, 3.2, 0.407]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 8, 1, 0.011, 8.05, 0.022, 10, 0.033, 10, 3, 0.05, 0, 3, 0.083, 0, 1, 0.183, 0, 0.283, 0.114, 0.383, 1, 1, 0.433, 1.443, 0.483, 4.748, 0.533, 5, 1, 0.628, 5.475, 0.722, 5.602, 0.817, 6, 1, 0.867, 6.211, 0.917, 6.765, 0.967, 7, 1, 1.05, 7.392, 1.133, 7.605, 1.217, 8, 1, 1.233, 8.079, 1.25, 10, 1.267, 10, 3, 1.283, 0, 3, 1.333, 0, 1, 1.417, 0, 1.5, 0.097, 1.583, 1, 1, 1.656, 1.783, 1.728, 4.468, 1.8, 5, 1, 1.872, 5.532, 1.944, 5.692, 2.017, 6, 1, 2.1, 6.355, 2.183, 6.602, 2.267, 7, 1, 2.311, 7.212, 2.356, 7.594, 2.4, 8, 1, 2.411, 8.102, 2.422, 10, 2.433, 10, 3, 2.45, 0, 3, 2.5, 1, 1, 2.589, 2.475, 2.678, 4.001, 2.767, 5, 1, 2.811, 5.499, 2.856, 5.726, 2.9, 6, 1, 2.961, 6.377, 3.022, 6.612, 3.083, 7, 1, 3.122, 7.247, 3.161, 7.585, 3.2, 8]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 7, 1, 0.039, 7.366, 0.078, 7.734, 0.117, 8, 1, 0.172, 8.38, 0.228, 8.569, 0.283, 9, 1, 0.306, 9.172, 0.328, 10, 0.35, 10, 2, 0.367, 0, 1, 0.378, 0, 0.389, 0, 0.4, 0, 1, 0.494, 0, 0.589, 0.094, 0.683, 1, 1, 0.761, 1.746, 0.839, 4.163, 0.917, 5, 1, 1, 5.896, 1.083, 6.602, 1.167, 7, 1, 1.261, 7.451, 1.356, 7.638, 1.45, 8, 1, 1.528, 8.298, 1.606, 8.605, 1.683, 9, 1, 1.7, 9.085, 1.717, 10, 1.733, 10, 2, 1.767, 0, 1, 1.772, 0, 1.778, 0, 1.783, 0, 1, 1.878, 0, 1.972, 0.136, 2.067, 1, 1, 2.106, 1.356, 2.144, 4.214, 2.183, 5, 1, 2.228, 5.899, 2.272, 6.599, 2.317, 7, 1, 2.367, 7.451, 2.417, 7.736, 2.467, 8, 1, 2.539, 8.381, 2.611, 8.585, 2.683, 9, 1, 2.706, 9.128, 2.728, 10, 2.75, 10, 2, 2.767, 0, 1, 2.778, 0, 2.789, 0.84, 2.8, 1, 1, 2.9, 2.441, 3, 3.546, 3.1, 5, 1, 3.133, 5.485, 3.167, 6.489, 3.2, 7]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 1, 1, 0.083, 2.414, 0.167, 3.586, 0.25, 5, 1, 0.283, 5.566, 0.317, 6.489, 0.35, 7, 1, 0.378, 7.426, 0.406, 7.817, 0.433, 8, 1, 0.494, 8.402, 0.556, 8.575, 0.617, 9, 1, 0.639, 9.155, 0.661, 10, 0.683, 10, 3, 0.7, 0, 3, 0.733, 0, 1, 0.822, 0, 0.911, 0.092, 1, 1, 1, 1.067, 1.681, 1.133, 3.903, 1.2, 5, 1, 1.25, 5.823, 1.3, 6.709, 1.35, 7, 1, 1.428, 7.453, 1.506, 7.69, 1.583, 8, 1, 1.672, 8.354, 1.761, 8.584, 1.85, 9, 1, 1.878, 9.13, 1.906, 10, 1.933, 10, 1, 1.944, 10, 1.956, 0, 1.967, 0, 1, 1.978, 0, 1.989, 0, 2, 0, 1, 2.072, 0, 2.144, 0.099, 2.217, 1, 1, 2.261, 1.555, 2.306, 4.655, 2.35, 5, 1, 2.461, 5.861, 2.572, 6.266, 2.683, 7, 1, 2.717, 7.22, 2.75, 7.828, 2.783, 8, 1, 2.861, 8.402, 2.939, 8.589, 3.017, 9, 1, 3.039, 9.117, 3.061, 10, 3.083, 10, 2, 3.1, 0, 1, 3.133, 0, 3.167, 0.333, 3.2, 1]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 1, 0.061, 0, 0.122, 0.095, 0.183, 1, 1, 0.233, 1.74, 0.283, 4.618, 0.333, 5, 1, 0.4, 5.509, 0.467, 5.598, 0.533, 6, 1, 0.561, 6.168, 0.589, 6.845, 0.617, 7, 1, 0.689, 7.402, 0.761, 7.54, 0.833, 8, 1, 0.867, 8.212, 0.9, 10, 0.933, 10, 3, 0.95, 0, 1, 0.961, 0, 0.972, 0, 0.983, 0, 1, 1.083, 0, 1.183, 0.091, 1.283, 1, 1, 1.356, 1.656, 1.428, 4.608, 1.5, 5, 1, 1.594, 5.512, 1.689, 5.62, 1.783, 6, 1, 1.85, 6.268, 1.917, 6.639, 1.983, 7, 1, 2.039, 7.301, 2.094, 7.486, 2.15, 8, 1, 2.194, 8.411, 2.239, 10, 2.283, 10, 3, 2.317, 0, 1, 2.328, 0, 2.339, 0, 2.35, 0, 1, 2.422, 0, 2.494, 0.099, 2.567, 1, 1, 2.611, 1.555, 2.656, 4.389, 2.7, 5, 1, 2.739, 5.535, 2.778, 5.713, 2.817, 6, 1, 2.867, 6.369, 2.917, 6.703, 2.967, 7, 1, 3.028, 7.363, 3.089, 7.611, 3.15, 8, 1, 3.161, 8.071, 3.172, 10, 3.183, 10, 3, 3.2, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 10, 1, 0.017, 10, 0.033, 10, 0.05, 10, 3, 0.067, 0, 1, 0.083, 0, 0.1, 0, 0.117, 0, 1, 0.2, 0, 0.283, 0.093, 0.367, 1, 1, 0.433, 1.726, 0.5, 4.359, 0.567, 5, 1, 0.661, 5.908, 0.756, 6.374, 0.85, 7, 1, 0.906, 7.368, 0.961, 7.699, 1.017, 8, 1, 1.083, 8.361, 1.15, 8.547, 1.217, 9, 1, 1.272, 9.378, 1.328, 10, 1.383, 10, 3, 1.4, 0, 1, 1.411, 0, 1.422, 0, 1.433, 0, 1, 1.511, 0, 1.589, 0.096, 1.667, 1, 1, 1.717, 1.581, 1.767, 4.026, 1.817, 5, 1, 1.861, 5.865, 1.906, 6.599, 1.95, 7, 1, 2, 7.451, 2.05, 7.703, 2.1, 8, 1, 2.161, 8.363, 2.222, 8.599, 2.283, 9, 1, 2.306, 9.146, 2.328, 9.5, 2.35, 9.5, 1, 2.367, 9.5, 2.383, 9.5, 2.4, 9.5, 1, 2.417, 9.5, 2.433, 10, 2.45, 10, 3, 2.467, 0, 1, 2.478, 0, 2.489, 0.733, 2.5, 1, 1, 2.561, 2.47, 2.622, 3.795, 2.683, 5, 1, 2.722, 5.767, 2.761, 6.648, 2.8, 7, 1, 2.889, 7.803, 2.978, 8.261, 3.067, 9, 1, 3.083, 9.139, 3.1, 9.5, 3.117, 9.5, 1, 3.128, 9.5, 3.139, 9.5, 3.15, 9.5, 1, 3.167, 9.5, 3.183, 10, 3.2, 10]}, {"Target": "Parameter", "Id": "Param295", "Segments": [0, 5, 1, 0.083, 5.852, 0.167, 6.58, 0.25, 7, 1, 0.339, 7.449, 0.428, 7.598, 0.517, 8, 1, 0.55, 8.151, 0.583, 8.559, 0.617, 9, 1, 0.633, 9.221, 0.65, 10, 0.667, 10, 3, 0.683, 0, 1, 0.694, 0, 0.706, 0, 0.717, 0, 1, 0.783, 0, 0.85, 0.196, 0.917, 1, 1, 1.011, 2.139, 1.106, 3.969, 1.2, 5, 1, 1.278, 5.849, 1.356, 6.467, 1.433, 7, 1, 1.494, 7.418, 1.556, 7.711, 1.617, 8, 1, 1.694, 8.368, 1.772, 8.589, 1.85, 9, 1, 1.872, 9.117, 1.894, 10, 1.917, 10, 3, 1.95, 0, 1, 1.961, 0, 1.972, 0, 1.983, 0, 1, 2.05, 0, 2.117, 0.103, 2.183, 1, 1, 2.222, 1.523, 2.261, 4.115, 2.3, 5, 1, 2.339, 5.885, 2.378, 6.556, 2.417, 7, 1, 2.456, 7.444, 2.494, 7.8, 2.533, 8, 1, 2.611, 8.4, 2.689, 8.605, 2.767, 9, 1, 2.783, 9.085, 2.8, 10, 2.817, 10, 3, 2.833, 0, 1, 2.861, 0, 2.889, 0.568, 2.917, 1, 1, 3.011, 2.47, 3.106, 3.53, 3.2, 5]}]}