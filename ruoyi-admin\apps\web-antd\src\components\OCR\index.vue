<template>
  <div class="ocr-container">
    <!-- 截图区域 -->
    <div v-if="isCapturing" class="screenshot-area" ref="screenshotArea">
      <div class="screenshot-overlay">
        <div class="screenshot-message">请选择要识别的区域</div>
        <div 
          class="screenshot-selection" 
          :style="selectionStyle"
          v-show="isSelecting"
        ></div>
      </div>
    </div>

    <!-- 识别结果弹窗 -->
    <Modal
      v-model:open="showResultModal"
      title="文字识别与批注"
      :footer="null"
      width="600px"
    >
      <div class="ocr-result-container">
        <!-- 加载状态 -->
        <div v-if="isProcessing" class="ocr-loading">
          <Spin size="large" />
          <p>正在识别文字，请稍候...</p>
          <div class="ocr-cancel-action">
            <Button @click="cancelOCR" type="default">取消识别</Button>
          </div>
        </div>
        
        <!-- 识别结果 -->
        <div v-else>
          <!-- 小说信息显示（只读） -->
           <div class="novel-info">
             <div class="info-row">
               <Input
                 :value="novalName"
                 placeholder="小说名字（自动填充）"
                 readonly
                 style="margin-right: 8px;"
               />
               <Input
                 :value="novalChapter"
                 placeholder="小说章节（自动填充）"
                 readonly
               />
             </div>
           </div>
          
          <!-- 识别的文字内容 -->
          <div class="text-section">
            <label class="section-label">识别的文字内容：</label>
            <Textarea
              v-model:value="recognizedText"
              :rows="6"
              placeholder="识别结果将显示在这里..."
              readonly
            />
          </div>
          
          <!-- 批注输入 -->
          <div class="exegesis-section">
            <label class="section-label">批注内容：</label>
            <Textarea
              v-model:value="exegesisText"
              :rows="4"
              placeholder="请输入您的批注..."
              :maxlength="500"
              show-count
            />
          </div>
          
          <div class="ocr-actions">
            <Button @click="copyToClipboard" type="default">复制文字</Button>
            <Button 
              @click="submitExegesis" 
              type="primary" 
              :loading="isSubmitting"
              :disabled="!recognizedText.trim() || !exegesisText.trim()"
            >
              提交批注
            </Button>
            <Button @click="closeResultModal" type="default">关闭</Button>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { Modal, message, Spin, Button, Textarea, Input } from 'ant-design-vue';
import { useClipboard } from '@vueuse/core';
import { createWorker } from 'tesseract.js';
import html2canvas from 'html2canvas';
import { exegesisAdd } from '#/api/noval/exegesis';
import { useNovelStore } from '#/stores/novel';
import type { ExegesisForm } from '#/api/noval/exegesis/model';

// 定义组件属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  language: {
    type: String,
    default: 'chi_sim+eng' // 默认中文简体+英文
  }
});

// 定义组件事件
const emit = defineEmits(['update:open', 'capture-complete', 'ocr-complete', 'exegesis-complete']);

// 状态变量
const isCapturing = ref(false);
const isSelecting = ref(false);
const isProcessing = ref(false);
const showResultModal = ref(false);
const recognizedText = ref('');
const screenshotArea = ref<HTMLElement | null>(null);

// 选择区域的坐标
const selectionStart = ref({ x: 0, y: 0 });
const selectionEnd = ref({ x: 0, y: 0 });

// 计算选择区域的样式
const selectionStyle = computed(() => {
  const left = Math.min(selectionStart.value.x, selectionEnd.value.x);
  const top = Math.min(selectionStart.value.y, selectionEnd.value.y);
  const width = Math.abs(selectionEnd.value.x - selectionStart.value.x);
  const height = Math.abs(selectionEnd.value.y - selectionStart.value.y);

  return {
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
  };
});

// 剪贴板功能
const { copy } = useClipboard();

// 批注相关状态
const exegesisText = ref(''); // 批注内容
const isSubmitting = ref(false); // 提交状态
const novelStore = useNovelStore(); // 全局小说状态

// 从全局状态获取书名和章节名（只读）
const novalName = computed(() => {
  // 首先尝试从全局状态获取
  if (novelStore.currentBookName) {
    return novelStore.currentBookName;
  }
  
  // 检查是否在起点中文网环境
  const isQidian = window.location.hostname.includes('qidian.com');
  if (isQidian) {
    // 尝试从页面元素获取书籍名称
    const bookSelectors = [
      '.book-info h1', '.book-title', '.book-info-title', 
      '.book-detail-info .book-name', '#bookName', '.book-name'
    ];
    
    for (const selector of bookSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        return element.textContent.trim();
      }
    }
    
    // 尝试从window对象获取
    if (window.g_data && window.g_data.bookInfo && window.g_data.bookInfo.bookName) {
      return window.g_data.bookInfo.bookName;
    }
  }
  
  return '';
});

const novalChapter = computed(() => {
  // 首先尝试从全局状态获取
  if (novelStore.currentChapterName) {
    return novelStore.currentChapterName;
  }
  
  // 检查是否在起点中文网环境
  const isQidian = window.location.hostname.includes('qidian.com');
  if (isQidian) {
    // 尝试从页面元素获取章节名称
    const chapterSelectors = [
      '.j_chapterName', '.chapter-name', '.read-chapter-name', 
      '.chapter_name', '#chapterName', '.chapter-title'
    ];
    
    for (const selector of chapterSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        return element.textContent.trim();
      }
    }
    
    // 尝试从window对象获取
    if (window.g_data && window.g_data.chapter && window.g_data.chapter.chapterName) {
      return window.g_data.chapter.chapterName;
    }
  }
  
  return '';
});

const novalId = computed(() => {
  // 首先尝试从全局状态获取
  if (novelStore.currentNovelId) {
    return novelStore.currentNovelId;
  }
  
  // 检查是否在起点中文网环境
  const isQidian = window.location.hostname.includes('qidian.com');
  if (isQidian) {
    // 尝试从URL获取书籍ID
    const urlMatch = window.location.href.match(/book\/(\d+)/i);
    if (urlMatch && urlMatch[1]) {
      return parseInt(urlMatch[1]);
    }
    
    // 尝试从meta标签获取
    const metaBookId = document.querySelector('meta[property="og:novel:book_id"]') ||
                     document.querySelector('meta[name="bookId"]');
    if (metaBookId && metaBookId.getAttribute('content')) {
      return parseInt(metaBookId.getAttribute('content'));
    }
    
    // 尝试从window对象获取
    if (window.g_data && window.g_data.bookInfo && window.g_data.bookInfo.bookId) {
      return parseInt(window.g_data.bookInfo.bookId);
    }
  }
  
  return 0;
});

// 存储当前worker的引用
let currentWorker: any = null;

// 监听open属性变化
watch(() => props.open, (newVal) => {
  if (newVal && !showResultModal.value) {
    startCapture();
  } else if (!newVal && !showResultModal.value) {
    cancelCapture();
  }
});

// 开始截图
const startCapture = () => {
  cleanupEventListeners();
  isCapturing.value = true;
  document.body.style.cursor = 'crosshair';
  document.addEventListener('mousedown', handleMouseDown);
};

// 取消截图
const cancelCapture = () => {
  isCapturing.value = false;
  isSelecting.value = false;
  cleanupEventListeners();
  emit('update:open', false);
};

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  isSelecting.value = true;
  selectionStart.value = { x: e.clientX, y: e.clientY };
  selectionEnd.value = { x: e.clientX, y: e.clientY };
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (isSelecting.value) {
    selectionEnd.value = { x: e.clientX, y: e.clientY };
  }
};

// 鼠标释放事件
const handleMouseUp = async () => {
  if (isSelecting.value) {
    cleanupEventListeners();
    
    const width = Math.abs(selectionEnd.value.x - selectionStart.value.x);
    const height = Math.abs(selectionEnd.value.y - selectionStart.value.y);
    
    if (width > 10 && height > 10) {
      await captureSelectedArea();
    } else {
      message.warning('请选择更大的区域');
      isSelecting.value = false;
      document.addEventListener('mousedown', handleMouseDown);
      document.body.style.cursor = 'crosshair';
    }
  }
};

// 清理事件监听器
const cleanupEventListeners = () => {
  document.removeEventListener('mousedown', handleMouseDown);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.body.style.cursor = 'default';
};

// 截取选中区域的图像
const captureSelectedArea = async () => {
  try {
    const left = Math.min(selectionStart.value.x, selectionEnd.value.x);
    const top = Math.min(selectionStart.value.y, selectionEnd.value.y);
    const width = Math.abs(selectionEnd.value.x - selectionStart.value.x);
    const height = Math.abs(selectionEnd.value.y - selectionStart.value.y);
    
    message.loading('正在截取屏幕...', 1);
    
    // 使用html2canvas截取屏幕区域
    const canvas = await html2canvas(document.documentElement, {
      x: left,
      y: top,
      width: width,
      height: height,
      logging: false,
      useCORS: false,
      allowTaint: false,
      scale: 1
    });
    
    // 检查是否在起点中文网环境
    const isQidian = window.location.hostname.includes('qidian.com');
    
    // 如果在起点中文网环境，添加水印
    if (isQidian) {
      try {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // 保存当前状态
          ctx.save();
          
          // 设置水印样式
          ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
          ctx.font = '12px Arial';
          
          // 获取书籍和章节信息
          const bookName = novalName.value || '未知书籍';
          const chapterName = novalChapter.value || '未知章节';
          const timestamp = new Date().toLocaleString();
          
          // 添加水印文本
          const watermarkText = `${bookName} - ${chapterName} - ${timestamp}`;
          
          // 在右下角添加水印
          ctx.fillText(watermarkText, canvas.width - ctx.measureText(watermarkText).width - 10, canvas.height - 10);
          
          // 恢复状态
          ctx.restore();
          
          console.log('已添加起点中文网水印');
        }
      } catch (e) {
        console.error('添加水印失败:', e);
      }
    }
    
    const imageData = canvas.toDataURL('image/png', 0.9);
    
    // 完成截图，开始OCR识别
    isCapturing.value = false;
    isSelecting.value = false;
    showResultModal.value = true;
    isProcessing.value = true;
    recognizedText.value = '';
    
    // 计算选择区域
     const area = {
       x: screenshotArea.value.x,
       y: screenshotArea.value.y,
       width: screenshotArea.value.width,
       height: screenshotArea.value.height
     };
    
    emit('capture-complete', {
      imageData,
      area,
      isQidian,
      bookInfo: isQidian ? {
        bookName: novalName.value,
        chapterName: novalChapter.value,
        bookId: novalId.value,
        timestamp: new Date().toISOString()
      } : null
    });
    await performOCR(imageData);
    
  } catch (error) {
    console.error('截图失败:', error);
    message.error('截图失败，请重试');
    cancelCapture();
  }
};

// 执行OCR识别
const performOCR = async (imageData: string) => {
  try {
    message.loading('正在初始化OCR引擎...', 2);
    
    // 创建worker - 提高识别精度
     currentWorker = await createWorker(props.language, 1, {
       logger: m => {}, // 使用空函数作为logger避免控制台输出
       // 提高识别精度的配置
       tessedit_pageseg_mode: '6', // 假设单个统一的文本块
       tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十百千万亿零壹贰叁肆伍陆柒捌玖拾佰仟萬億', // 限制字符集提高精度
       preserve_interword_spaces: '0' // 不保留单词间空格
     });
    
    if (!currentWorker) {
      throw new Error('无法创建OCR引擎');
    }
    
    message.loading('正在识别文字...', 0);
    
    // 执行OCR识别
    const result = await currentWorker.recognize(imageData);
    
    // 提取识别文本并过滤不常见符号和空格
        const text = result.data.text
          .replace(/[~`!@#$%^&*()_+=\[\]{}|\\:;"'<>,.?/]/g, '') // 去除特殊符号
          .replace(/[\u00A0\u2000-\u200B\u2028\u2029\uFEFF]/g, '') // 去除不可见字符
          .replace(/[\u00B0\u00B1\u00B2\u00B3\u00B9\u00BC\u00BD\u00BE]/g, '') // 去除上标下标等
          .replace(/[\u2010-\u2015\u2018-\u201F\u2020-\u2027]/g, '') // 去除特殊标点
          .replace(/\s/g, ''); // 去除所有空白字符（空格、制表符、换行符等）
        recognizedText.value = text;
    
    // 清理worker
    await currentWorker.terminate();
    currentWorker = null;
    
    // 更新状态
    isProcessing.value = false;
    
    // 检查是否在起点中文网环境
    const isQidian = window.location.hostname.includes('qidian.com');
    
    // 发送OCR完成事件
    emit('ocr-complete', {
      text: text,
      confidence: result.data.confidence,
      isQidian: isQidian,
      bookInfo: isQidian ? {
        bookName: novalName.value,
        chapterName: novalChapter.value,
        bookId: novalId.value,
        timestamp: new Date().toISOString()
      } : null
    });
    
    // 显示结果消息
    if (text) {
      message.success(`识别完成！识别到 ${text.length} 个字符`);
    } else {
      message.warning('未识别到文字，请尝试选择更清晰的区域');
    }
    
  } catch (error) {
    console.error('OCR识别失败:', error);
    message.error('文字识别失败: ' + (error.message || '请重试'));
    
    isProcessing.value = false;
    recognizedText.value = '识别失败，请重试';
    
    // 清理worker
    if (currentWorker) {
      try {
        await currentWorker.terminate();
        currentWorker = null;
      } catch (e) {
        console.error('清理worker失败:', e);
      }
    }
  } finally {
    message.destroy();
  }
};

// 取消OCR识别
const cancelOCR = async () => {
  if (currentWorker) {
    try {
      message.loading('正在取消识别...', 0.5);
      await currentWorker.terminate();
      currentWorker = null;
      isProcessing.value = false;
      recognizedText.value = '已取消识别';
      message.info('已取消文字识别');
    } catch (error) {
      console.error('取消OCR识别失败:', error);
      message.error('取消识别失败');
    }
  }
};

// 复制到剪贴板
const copyToClipboard = () => {
  if (recognizedText.value) {
    copy(recognizedText.value);
    message.success('已复制到剪贴板');
  }
};

// 提交批注
const submitExegesis = async () => {
  if (!recognizedText.value.trim()) {
    message.warning('请先识别文字内容');
    return;
  }
  
  if (!exegesisText.value.trim()) {
    message.warning('请输入批注内容');
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 检查是否在起点中文网环境
    const isQidian = window.location.hostname.includes('qidian.com');
    let sourceInfo = '';
    
    if (isQidian) {
      // 添加来源信息
      sourceInfo = `来源：起点中文网 - ${window.location.href}`;
      console.log('检测到起点中文网环境，添加来源信息:', sourceInfo);
    }
    
    const exegesisData: ExegesisForm = {
      novalName: novalName.value || '未知小说',
      novalChapter: novalChapter.value || '未知章节',
      novalContent: recognizedText.value,
      exegesis: exegesisText.value + (sourceInfo ? `\n\n${sourceInfo}` : ''),
      novalId: novalId.value || 0,
      userid: 1 // 这里应该从用户状态获取，暂时写死
    };
    
    // 如果在起点中文网环境，添加额外信息
    if (isQidian) {
      try {
        // 尝试获取更多信息
        const author = document.querySelector('.book-info .writer')?.textContent.trim() ||
                      document.querySelector('meta[property="og:novel:author"]')?.getAttribute('content');
        
        const category = document.querySelector('meta[property="og:novel:category"]')?.getAttribute('content') ||
                       document.querySelector('.book-info .tag')?.textContent.trim();
        
        // 添加到exegesisData中
        if (author) {
          (exegesisData as any).author = author;
        }
        
        if (category) {
          (exegesisData as any).category = category;
        }
        
        // 添加当前时间
        (exegesisData as any).captureTime = new Date().toISOString();
        
        console.log('已添加起点中文网额外信息:', { author, category });
      } catch (e) {
        console.error('获取起点中文网额外信息失败:', e);
      }
    }
    
    await exegesisAdd(exegesisData);
    message.success('批注提交成功！');
    
    // 发送批注完成事件
    emit('exegesis-complete', {
      text: recognizedText.value,
      exegesis: exegesisText.value,
      data: exegesisData,
      isQidian: isQidian
    });
    
    // 清空批注内容
    exegesisText.value = '';
    
  } catch (error) {
    console.error('提交批注失败:', error);
    message.error('提交批注失败: ' + (error.message || '请重试'));
  } finally {
    isSubmitting.value = false;
  }
};

// 关闭结果弹窗
const closeResultModal = () => {
  showResultModal.value = false;
  recognizedText.value = '';
  exegesisText.value = '';
  // 重置OCR状态，允许下次截图
  emit('update:open', false);
};

// 组件卸载前清理
onBeforeUnmount(() => {
  cleanupEventListeners();
  if (currentWorker) {
    currentWorker.terminate().catch(console.error);
  }
});
</script>

<style scoped>
.ocr-container {
  position: relative;
}

.screenshot-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: crosshair;
}

.screenshot-message {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.screenshot-selection {
  position: absolute;
  border: 2px dashed #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  pointer-events: none;
}

.ocr-result-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.ocr-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 150px;
  gap: 16px;
  text-align: center;
  padding: 40px 0;
}

.ocr-loading p {
  margin: 16px 0 0 0;
  color: #666;
}

.ocr-cancel-action {
  margin-top: 16px;
}

.novel-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  gap: 8px;
}

.text-section,
.exegesis-section {
  margin-bottom: 16px;
}

.section-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.ocr-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}
</style>