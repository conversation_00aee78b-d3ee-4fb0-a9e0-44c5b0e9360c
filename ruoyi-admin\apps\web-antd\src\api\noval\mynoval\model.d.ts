import type { PageQuery, BaseEntity } from '#/api/common';

export interface MynovalVO {
  id: string | number;
  userid: string | number;
  cosid: string | number;
  name?: string;
  author?: string;
  flag?: string;
}

export interface MynovalForm extends BaseEntity {
  id?: string | number;
  userid?: string | number;
  cosid?: string | number;
  name?: string;
  author?: string;
  flag?: string;
}

export interface MynovalQuery extends PageQuery {
  id?: string | number;
  userid?: string | number;
  cosid?: string | number;
  flag?: string;

  params?: any;
}
