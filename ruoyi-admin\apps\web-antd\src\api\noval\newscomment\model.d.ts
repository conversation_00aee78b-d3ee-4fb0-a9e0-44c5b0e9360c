import type { PageQuery, BaseEntity } from '#/api/common';

export interface NewsCommentVo extends BaseEntity {
  /**
   * 新闻id
   */
  newsId?: number;
  /**
   * 评论id
   */
  commentId?: number;
}


export interface CommentVO {
  /**
   * 消息id
   */
  newsId: string | number;

  /**
   * 评论id
   */
  commerntId: string | number;

}

export interface CommentForm extends BaseEntity {
  /**
   * 消息id
   */
  newsId?: string | number;

  /**
   * 评论id
   */
  commerntId?: string | number;

}

export interface CommentQuery extends PageQuery {
  /**
   * 消息id
   */
  newsId?: string | number;

  /**
   * 评论id
   */
  commerntId?: string | number;

  /**
    * 日期范围参数
    */
  params?: any;
}
