export default eventHandler(async (event) => {
  return useResponseSuccess([
    {
      dictCode: 1,
      dictLabel: '草稿',
      dictValue: '0',
      dictType: 'sys_noval_state',
      dictSort: 1,
      status: '0',
      remark: '草稿状态',
      createTime: '2024-01-01 00:00:00',
    },
    {
      dictCode: 2,
      dictLabel: '已发布',
      dictValue: '1',
      dictType: 'sys_noval_state',
      dictSort: 2,
      status: '0',
      remark: '已发布状态',
      createTime: '2024-01-01 00:00:00',
    },
    {
      dictCode: 3,
      dictLabel: '已下架',
      dictValue: '2',
      dictType: 'sys_noval_state',
      dictSort: 3,
      status: '0',
      remark: '已下架状态',
      createTime: '2024-01-01 00:00:00',
    },
  ]);
});