import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

// 简化的路由菜单数据，不包含live2d-test
const ROUTER_MENUS = [
  {
    id: 1,
    name: 'Workspace',
    path: '/workspace',
    component: '/dashboard/workspace/index',
    hidden: false,
    meta: {
      icon: 'carbon:workspace',
      title: 'page.dashboard.workspace',
      noCache: false,
    },
    children: [],
  },
  {
    id: 2,
    name: 'Analytics',
    path: '/analytics',
    component: '/dashboard/analytics/index',
    hidden: false,
    meta: {
      icon: 'lucide:area-chart',
      title: 'page.dashboard.analytics',
      noCache: false,
    },
    children: [],
  },
];

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  // 返回简化的菜单数据，确保不包含live2d-test路由
  return useResponseSuccess(ROUTER_MENUS);
});
