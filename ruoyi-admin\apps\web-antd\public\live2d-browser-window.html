<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Live2D 浏览器窗口</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: transparent;
      overflow: hidden;
    }

    /* 浏览器窗口容器 */
    .browser-window {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 320px;
      height: 450px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      transition: all 0.3s ease;
      z-index: 9999;
      transform: translateY(calc(100% - 40px));
    }

    .browser-window.expanded {
      transform: translateY(0);
    }

    /* 浏览器窗口标题栏 */
    .window-header {
      display: flex;
      align-items: center;
      padding: 8px 10px;
      background-color: #f0f0f0;
      border-bottom: 1px solid #ddd;
      cursor: pointer;
    }

    .window-title {
      flex-grow: 1;
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 5px;
    }

    .window-controls {
      display: flex;
      gap: 8px;
    }

    .control-button {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #555;
    }

    .minimize-btn {
      background-color: #ffbd44;
    }

    .expand-btn {
      background-color: #00ca56;
    }

    .close-btn {
      background-color: #ff605c;
    }

    /* 浏览器窗口内容区 */
    .window-content {
      flex-grow: 1;
      position: relative;
      overflow: hidden;
    }

    .content-iframe {
      width: 100%;
      height: 100%;
      border: none;
    }

    /* Live2D 容器 */
    .live2d-container {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 120px;
      height: 150px;
      pointer-events: none;
      z-index: 10;
    }

    .live2d-iframe {
      width: 100%;
      height: 100%;
      border: none;
      pointer-events: auto;
    }

    /* 消息气泡 */
    .message-bubble {
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 20;
      display: none;
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
</head>
<body>
  <!-- 浏览器窗口容器 -->
  <div id="browserWindow" class="browser-window">
    <!-- 浏览器窗口标题栏 -->
    <div id="windowHeader" class="window-header">
      <div class="window-controls">
        <div class="control-button close-btn" title="关闭" onclick="closeWindow()">&times;</div>
        <div class="control-button minimize-btn" title="最小化" onclick="toggleWindow()">&minus;</div>
        <div class="control-button expand-btn" title="展开/收起" onclick="expandWindow()">&#9650;</div>
      </div>
      <div id="windowTitle" class="window-title">正在加载...</div>
    </div>

    <!-- 浏览器窗口内容区 -->
    <div class="window-content">
      <iframe id="contentIframe" class="content-iframe" src="about:blank" allowfullscreen></iframe>

      <!-- Live2D 容器 -->
      <div class="live2d-container">
        <iframe id="live2dIframe" class="live2d-iframe" src="about:blank" allowfullscreen></iframe>
      </div>

      <!-- 消息气泡 -->
      <div id="messageBubble" class="message-bubble"></div>
    </div>
  </div>

  <script>
    // 窗口状态
    let isExpanded = false;
    let isMinimized = true;
    let dragStartX = 0;
    let dragStartY = 0;
    let windowStartX = 0;
    let windowStartY = 0;
    let isDragging = false;

    // DOM 元素
    const browserWindow = document.getElementById('browserWindow');
    const windowHeader = document.getElementById('windowHeader');
    const windowTitle = document.getElementById('windowTitle');
    const contentIframe = document.getElementById('contentIframe');
    const live2dIframe = document.getElementById('live2dIframe');
    const messageBubble = document.getElementById('messageBubble');
    const expandBtn = document.querySelector('.expand-btn');

    // 初始化
    function init() {
      // 获取URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const contentUrl = urlParams.get('url') || '/live2d-external-demo.html';
      const modelId = urlParams.get('modelId') || '';

      // 设置内容iframe的src
      contentIframe.src = contentUrl;

      // 设置Live2D iframe的src
      const live2dSrc = `/live2d-embed.html${modelId ? `?modelId=${modelId}` : ''}`;
      live2dIframe.src = live2dSrc;

      // 设置窗口标题
      windowTitle.textContent = '外链浏览器';

      // 设置拖拽事件
      setupDragEvents();

      // 监听iframe加载完成事件
      contentIframe.onload = function() {
        try {
          // 尝试获取iframe内页面的标题
          const iframeTitle = contentIframe.contentDocument.title;
          if (iframeTitle) {
            windowTitle.textContent = iframeTitle;
          }

          // 向iframe内部注入Live2D配置
          injectLive2DConfig();
        } catch (e) {
          console.error('无法访问iframe内容:', e);
        }
      };

      // 监听来自Live2D iframe的消息
      window.addEventListener('message', handleLive2DMessages);
    }

    // 设置拖拽事件
    function setupDragEvents() {
      windowHeader.addEventListener('mousedown', function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        windowStartX = browserWindow.offsetLeft;
        windowStartY = browserWindow.offsetTop;

        // 防止文本选中
        e.preventDefault();
      });

      document.addEventListener('mousemove', function(e) {
        if (isDragging) {
          const deltaX = e.clientX - dragStartX;
          const deltaY = e.clientY - dragStartY;

          browserWindow.style.right = 'auto';
          browserWindow.style.bottom = 'auto';
          browserWindow.style.left = (windowStartX + deltaX) + 'px';
          browserWindow.style.top = (windowStartY + deltaY) + 'px';
        }
      });

      document.addEventListener('mouseup', function() {
        isDragging = false;
      });
    }

    // 处理来自Live2D iframe的消息
    function handleLive2DMessages(event) {
      // 确保消息来自Live2D iframe
      if (event.source !== live2dIframe.contentWindow) return;

      const data = event.data;
      if (!data || !data.type) return;

      switch (data.type) {
        case 'live2d-loaded':
          console.log('Live2D模型已加载:', data.modelName);
          // 可以在这里添加模型加载完成后的操作
          break;

        case 'live2d-message-shown':
          // Live2D显示了一条消息
          console.log('Live2D显示消息:', data.text);
          break;
      }
    }

    // 向内容iframe注入Live2D配置
    function injectLive2DConfig() {
      try {
        // 从localStorage获取Live2D配置
        const configStr = localStorage.getItem('live2dConfig');
        if (configStr) {
          const config = JSON.parse(configStr);

          // 向iframe内部注入配置
          contentIframe.contentWindow.postMessage({
            type: 'inject-live2d-config',
            config: config
          }, '*');

          console.log('已向内容iframe注入Live2D配置');
        }
      } catch (e) {
        console.error('注入Live2D配置失败:', e);
      }
    }

    // 显示消息气泡
    function showMessage(text, timeout = 3000) {
      messageBubble.textContent = text;
      messageBubble.style.display = 'block';

      // 设置超时自动隐藏
      setTimeout(() => {
        messageBubble.style.display = 'none';
      }, timeout);
    }

    // 切换窗口最小化/展开状态
    function toggleWindow() {
      isMinimized = !isMinimized;

      if (isMinimized) {
        browserWindow.style.transform = 'translateY(calc(100% - 40px))';
        expandBtn.innerHTML = '&#9650;'; // 上箭头
      } else {
        browserWindow.style.transform = 'translateY(0)';
        expandBtn.innerHTML = '&#9660;'; // 下箭头
      }
    }

    // 展开/收起窗口
    function expandWindow() {
      isExpanded = !isExpanded;

      if (isExpanded) {
        browserWindow.style.width = '80vw';
        browserWindow.style.height = '80vh';
        browserWindow.style.right = '10vw';
        browserWindow.style.bottom = '10vh';
        expandBtn.innerHTML = '&#9642;'; // 方块
      } else {
        browserWindow.style.width = '320px';
        browserWindow.style.height = '450px';
        browserWindow.style.right = '20px';
        browserWindow.style.bottom = '20px';
        expandBtn.innerHTML = '&#9650;'; // 上箭头
      }

      // 确保窗口是展开的
      if (isMinimized) {
        isMinimized = false;
        browserWindow.style.transform = 'translateY(0)';
      }
    }

    // 关闭窗口
    function closeWindow() {
      // 如果是在iframe中，通知父窗口关闭
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'close-live2d-browser-window'
        }, '*');
      } else {
        // 直接隐藏窗口
        browserWindow.style.display = 'none';
      }
    }

    // 初始化
    window.onload = init;
  </script>
</body>
</html>
