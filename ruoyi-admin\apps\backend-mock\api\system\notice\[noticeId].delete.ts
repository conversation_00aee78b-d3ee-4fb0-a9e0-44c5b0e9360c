import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { deleteNotice } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const noticeId = Number(getRouterParam(event, 'noticeId'));
  const success = deleteNotice(noticeId);

  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: '通知不存在',
    });
  }

  return useResponseSuccess(null);
});
