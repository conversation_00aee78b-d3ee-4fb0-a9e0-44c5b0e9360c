<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Live2D 模型外链展示示例</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0078d7;
    }
    .demo-section {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 20px;
      background-color: #f9f9f9;
    }
    .code-block {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 15px;
      overflow-x: auto;
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      margin: 15px 0;
    }
    .iframe-container {
      margin: 20px 0;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .fixed-iframe {
      position: fixed;
      right: 0;
      bottom: 0;
      width: 280px;
      height: 300px;
      z-index: 1000;
      pointer-events: none;
    }
    .fixed-iframe iframe {
      pointer-events: auto;
    }
    .responsive-iframe {
      width: 100%;
      height: 400px;
      border: none;
    }
    .button {
      display: inline-block;
      padding: 8px 16px;
      background-color: #0078d7;
      color: white;
      border-radius: 4px;
      text-decoration: none;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .button:hover {
      background-color: #005a9e;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .model-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    .model-card {
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
      transition: transform 0.3s ease;
    }
    .model-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .model-preview {
      width: 100%;
      height: 200px;
      object-fit: cover;
      background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .model-preview img {
      max-width: 100%;
      max-height: 100%;
    }
    .model-info {
      padding: 10px;
      background-color: white;
    }
    .model-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .model-id {
      color: #666;
      font-size: 0.9em;
    }
    @media (max-width: 768px) {
      .fixed-iframe {
        width: 200px;
        height: 250px;
      }
      .model-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      }
    }
  </style>
</head>
<body>
  <h1>Live2D 模型外链展示示例</h1>
  
  <div class="demo-section">
    <h2>1. 直接访问独立页面</h2>
    <p>您可以直接访问 Live2D 模型的独立页面：</p>
    <div class="code-block">
      <a href="/live2d-embed.html" target="_blank" class="button">打开独立页面</a>
    </div>
    
    <h3>1.1 通过URL参数指定模型</h3>
    <p>您可以通过URL参数 <code>modelId</code> 来指定要显示的模型：</p>
    <div class="code-block">
      <a href="/live2d-embed.html?modelId=haru" target="_blank" class="button">显示Haru模型</a>
      <a href="/live2d-embed.html?modelId=shizuku" target="_blank" class="button">显示Shizuku模型</a>
    </div>
    <p>URL格式: <code>/live2d-embed.html?modelId=模型ID</code></p>
  </div>

  <div class="demo-section">
    <h2>2. 通过 iframe 嵌入</h2>
    
    <h3>2.1 固定在右下角</h3>
    <p>您可以将 Live2D 模型固定在网页的右下角：</p>
    <div class="code-block">
&lt;iframe src="/live2d-embed.html" frameborder="0" width="280" height="300" style="position: fixed; right: 0; bottom: 0; z-index: 1000;"&gt;&lt;/iframe&gt;
    </div>
    <div class="fixed-iframe">
      <iframe src="/live2d-embed.html" frameborder="0" width="100%" height="100%"></iframe>
    </div>
    
    <h3>2.2 响应式嵌入</h3>
    <p>您也可以将 Live2D 模型嵌入到页面的特定位置：</p>
    <div class="code-block">
&lt;iframe src="/live2d-embed.html" frameborder="0" width="100%" height="400" style="border: none;"&gt;&lt;/iframe&gt;
    </div>
    <div class="iframe-container">
      <iframe src="/live2d-embed.html" class="responsive-iframe"></iframe>
    </div>
  </div>

  <div class="demo-section">
    <h2>3. 与父页面通信</h2>
    <p>Live2D 模型加载成功后会向父页面发送消息，您可以在父页面中监听这些消息：</p>
    <div class="code-block">
// 在父页面中监听 Live2D 模型加载成功的消息
window.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'live2d-loaded') {
    console.log('Live2D模型已加载:', event.data.modelName);
    console.log('模型ID:', event.data.modelId);
    // 在这里执行您的代码
  }
});
    </div>
    <p>您也可以从父页面向 Live2D iframe 发送消息来控制模型：</p>
    <div class="code-block">
// 获取 iframe 元素
const live2dIframe = document.getElementById('live2d-iframe');

// 切换到指定模型
function switchToModel(modelId) {
  live2dIframe.contentWindow.postMessage({
    type: 'switch-model',
    modelId: modelId
  }, '*');
}

// 显示消息
function showMessage(text, timeout) {
  live2dIframe.contentWindow.postMessage({
    type: 'show-message',
    text: text,
    timeout: timeout || 3000
  }, '*');
}

// 切换显示/隐藏
function toggleLive2D() {
  live2dIframe.contentWindow.postMessage({
    type: 'toggle-visibility'
  }, '*');
}

// 示例调用
switchToModel('haru');
showMessage('你好，我是Haru！', 5000);
    </div>
  </div>

  <div class="demo-section">
    <h2>4. 在您的网站中使用</h2>
    <p>要在您自己的网站中使用 Live2D 模型，请按照以下步骤操作：</p>
    <ol>
      <li>将 <code>live2d-embed.html</code> 文件复制到您的网站目录中</li>
      <li>确保 <code>/api/live2d-models.json</code> API 可访问，或者修改 <code>live2d-embed.html</code> 中的 API 路径</li>
      <li>使用上述 iframe 代码将 Live2D 模型嵌入到您的网页中</li>
    </ol>
    <p>您也可以根据需要修改 <code>live2d-embed.html</code> 文件中的配置参数。</p>
  </div>

  <div class="demo-section">
    <h2>5. 自定义模型</h2>
    <p>您可以通过修改 <code>/api/live2d-models.json</code> 文件来添加或修改 Live2D 模型：</p>
    <div class="code-block">
{
  "models": [
    {
      "id": "haru",
      "name": "Haru",
      "path": "/live2d/haru/haru.model.json",
      "preview": "/live2d/haru/preview.png"
    },
    {
      "id": "shizuku",
      "name": "Shizuku",
      "path": "/live2d/shizuku/shizuku.model.json",
      "preview": "/live2d/shizuku/preview.png"
    }
  ]
}
    </div>
    <p>确保模型文件和预览图片放在正确的路径下。</p>
    
    <h3>5.1 可用模型列表</h3>
    <p>以下是当前可用的模型列表：</p>
    <div class="model-grid" id="model-grid">
      <!-- 模型列表将通过JavaScript动态加载 -->
      <div class="model-card">
        <div class="model-preview">加载中...</div>
        <div class="model-info">
          <div class="model-name">加载中...</div>
          <div class="model-id">请稍候</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 加载模型列表
    async function loadModelList() {
      try {
        const response = await fetch('/api/live2d-models.json');
        if (!response.ok) throw new Error('无法加载模型列表');
        
        const data = await response.json();
        const modelGrid = document.getElementById('model-grid');
        
        // 清空加载中的占位符
        modelGrid.innerHTML = '';
        
        // 添加模型卡片
        data.models.forEach(model => {
          const card = document.createElement('div');
          card.className = 'model-card';
          
          const preview = document.createElement('div');
          preview.className = 'model-preview';
          
          if (model.preview) {
            const img = document.createElement('img');
            img.src = model.preview;
            img.alt = model.name;
            preview.appendChild(img);
          } else {
            preview.textContent = '无预览图';
          }
          
          const info = document.createElement('div');
          info.className = 'model-info';
          
          const name = document.createElement('div');
          name.className = 'model-name';
          name.textContent = model.name || '未命名';
          
          const id = document.createElement('div');
          id.className = 'model-id';
          id.textContent = `ID: ${model.id}`;
          
          const link = document.createElement('a');
          link.href = `/live2d-embed.html?modelId=${model.id}`;
          link.target = '_blank';
          link.className = 'button';
          link.style.display = 'block';
          link.style.margin = '10px 0 0 0';
          link.style.textAlign = 'center';
          link.textContent = '查看';
          
          info.appendChild(name);
          info.appendChild(id);
          info.appendChild(link);
          
          card.appendChild(preview);
          card.appendChild(info);
          
          modelGrid.appendChild(card);
        });
      } catch (error) {
        console.error('加载模型列表失败:', error);
        document.getElementById('model-grid').innerHTML = `
          <div style="grid-column: 1/-1; text-align: center; padding: 20px;">
            <p>加载模型列表失败，请确保 /api/live2d-models.json 可访问。</p>
            <p>错误信息: ${error.message}</p>
          </div>
        `;
      }
    }
    
    // 页面加载完成后加载模型列表
    document.addEventListener('DOMContentLoaded', loadModelList);
  </script>

</body>
</html>