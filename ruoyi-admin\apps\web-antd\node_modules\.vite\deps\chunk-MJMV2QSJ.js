import {
  getConfig,
  index_esm_default,
  require_xe_utils
} from "./chunk-BCXSQRR2.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/ui/src/utils.js
var import_xe_utils = __toESM(require_xe_utils());
function nextZIndex() {
  return index_esm_default.getNext();
}
function getLastZIndex() {
  return index_esm_default.getCurrent();
}
function getFuncText(content, args) {
  if (content) {
    const translate = getConfig().translate;
    return import_xe_utils.default.toValueString(translate ? translate("" + content, args) : content);
  }
  return "";
}
function eqEmptyValue(cellValue) {
  return cellValue === null || cellValue === void 0 || cellValue === "";
}
function handleBooleanDefaultValue(value) {
  return import_xe_utils.default.isBoolean(value) ? value : null;
}

export {
  nextZIndex,
  getLastZIndex,
  getFuncText,
  eqEmptyValue,
  handleBooleanDefaultValue
};
//# sourceMappingURL=chunk-MJMV2QSJ.js.map
