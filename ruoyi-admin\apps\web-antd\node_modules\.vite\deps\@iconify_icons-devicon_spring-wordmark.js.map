{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@iconify+icons-devicon@1.2.17/node_modules/@iconify/icons-devicon/spring-wordmark.js"], "sourcesContent": ["const data = {\n\t\"width\": 128,\n\t\"height\": 128,\n\t\"body\": \"<path fill=\\\"#5FB832\\\" d=\\\"M1.7 98.6c-.6-.3-1-1-1-1.8c0-1.2.9-2.1 2.1-2.1c.4 0 .8.1 1.1.3c2.2 1.5 4.6 2.2 6.7 2.2c2.3 0 3.6-1 3.6-2.5v-.1c0-1.8-2.5-2.4-5.2-3.2c-3.4-1-7.2-2.4-7.2-6.8v-.1c0-4.4 3.6-7 8.2-7c2.5 0 5 .7 7.3 1.9c.7.4 1.3 1.1 1.3 2c0 1.2-1 2.1-2.2 2.1c-.4 0-.7-.1-1.1-.3c-1.9-1-3.8-1.6-5.4-1.6c-2.1 0-3.2 1-3.2 2.3v.1c0 1.7 2.5 2.4 5.2 3.3c3.4 1 7.2 2.6 7.2 6.7v.1c0 4.9-3.8 7.3-8.6 7.3c-2.9 0-6.1-.9-8.8-2.8m38.1-9.1c0-4.5-3-7.4-6.6-7.4s-6.7 3-6.7 7.4v.1c0 4.4 3.1 7.4 6.7 7.4c3.6-.2 6.6-3 6.6-7.5m-18.4-9.3c0-1.5 1.1-2.7 2.6-2.7s2.7 1.2 2.7 2.7v1.6c1.7-2.4 4.1-4.3 7.8-4.3c5.4 0 10.7 4.3 10.7 11.9v.1c0 7.6-5.2 11.9-10.7 11.9c-3.8 0-6.2-1.9-7.8-4v8.1c0 1.5-1.2 2.6-2.7 2.6c-1.4 0-2.6-1.1-2.6-2.6V80.2m26-.1c0-1.5 1.1-2.7 2.6-2.7s2.7 1.2 2.7 2.7v1.3c.3-2 3.5-3.9 5.8-3.9c1.7 0 2.6 1.1 2.6 2.6c0 1.4-.9 2.3-2.1 2.5c-3.8.7-6.4 3.9-6.4 8.5v7.6c0 1.4-1.2 2.6-2.7 2.6c-1.4 0-2.6-1.1-2.6-2.6V80.1m16.2 0c0-1.5 1.2-2.7 2.7-2.7c1.5 0 2.7 1.2 2.7 2.7v18.6c0 1.5-1.2 2.6-2.7 2.6c-1.5 0-2.7-1.1-2.7-2.6V80.1m7.7.1c0-1.5 1.2-2.7 2.7-2.7s2.8 1.2 2.8 2.7v1.1c1.5-2.1 3.8-3.8 7.5-3.8c5.4 0 8.5 3.5 8.5 8.8v12.4c0 1.5-1.2 2.6-2.7 2.6s-2.8-1.1-2.8-2.6V87.9c0-3.6-1.9-5.6-5.1-5.6c-3.2 0-5.4 2.1-5.4 5.7v10.7c0 1.5-1.2 2.6-2.8 2.6c-1.5 0-2.7-1.1-2.7-2.6V80.2m35.3 16.7c-3.7 0-6.8-2.8-6.8-7.4v-.1c0-4.5 3.1-7.4 6.8-7.4c3.7 0 6.9 3 6.9 7.4v.1c0 4.4-3.2 7.4-6.9 7.4m9.5-19.3c-1.5 0-2.7 1.2-2.7 2.7v1.6c-1.8-2.4-4.2-4.3-8.1-4.3c-5.6 0-11 4.3-11 12v.1c0 7.6 5.4 12 11 12c3.9 0 6.4-1.9 8.1-4c-.3 4.2-2.9 6.3-7.5 6.3c-2.7 0-5.1-.7-7.3-1.8c-.3-.1-.6-.2-1-.2c-1.3 0-2.3 1-2.3 2.2c0 1 .6 1.7 1.5 2.1c2.9 1.4 5.8 2.1 9.2 2.1c4.3 0 7.6-1 9.8-3.1c2-1.9 3.1-4.8 3.1-8.7V80.2c-.1-1.5-1.3-2.6-2.8-2.6m-47.1-6.5c0 1.4-1.2 2.6-2.7 2.6c-1.5 0-2.7-1.2-2.7-2.6c0-1.4 1.2-2.6 2.7-2.6c1.5 0 2.7 1.2 2.7 2.6m14-17.3C77 61.6 64.3 59 56.1 59.3c0 0-1.4.1-2.9.3c0 0 .6-.2 1.2-.5c5.7-1.9 8.4-2.3 11.9-4.1c6.5-3.3 13-10.4 14.3-17.8c-2.5 7.1-10 13.2-16.9 15.7c-4.7 1.7-13.2 3.3-13.2 3.3l-.3-.2c-5.8-2.7-6-15 4.6-18.9c4.6-1.7 9-.8 14-1.9c5.3-1.2 11.5-5.1 14-10.2c2.8 8.3 6.2 21 .1 28.8zm.1-31.2c-.7 1.6-1.6 3.1-2.6 4.4c-4.4-4.4-10.5-7.1-17.3-7.1C49.8 19.9 39 30.5 39 43.4c0 6.8 3 12.9 7.7 17.2l.5.5c-.9-.7-1-2-.3-2.8c.7-.9 2-1 2.9-.3s1 2 .3 2.8c-.7.9-2 1-2.9.3l.4.3c4.2 3.4 9.6 5.5 15.5 5.5c12.7 0 23.1-9.7 24-21.8c.8-5.9-1-13.5-4.1-22.5m42.1 56.6h-.9v1.2h.9c.3 0 .6-.2.6-.6c0-.4-.3-.6-.6-.6zm.5 3l-.9-1.4h-.6v1.4h-.5v-3.4h1.4c.6 0 1.1.4 1.1 1c0 .8-.7 1-.9 1l.9 1.4h-.5zm-.8-4.3c-1.4 0-2.6 1.1-2.6 2.5s1.2 2.5 2.6 2.5c1.4 0 2.6-1.1 2.6-2.5c0-1.3-1.2-2.5-2.6-2.5zm0 5.6c-1.7 0-3.1-1.3-3.1-3s1.4-3 3.1-3c1.7 0 3.1 1.3 3.1 3c0 1.6-1.4 3-3.1 3\\\"/>\"\n};\nexports.__esModule = true;\nexports.default = data;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAM,OAAO;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,IACT;AACA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAAA;AAAA;", "names": []}