import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@iconify+icons-lucide@1.2.135/node_modules/@iconify/icons-lucide/table.js
var require_table = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-lucide@1.2.135/node_modules/@iconify/icons-lucide/table.js"(exports) {
    var data = {
      "width": 24,
      "height": 24,
      "body": '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M12 3v18"/><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M3 9h18M3 15h18"/></g>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_table();
//# sourceMappingURL=@iconify_icons-lucide_table.js.map
