import type { CommentVO, CommentForm, CommentQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询消息跳转论坛列表
* @param params
* @returns 消息跳转论坛列表
*/
export function commentList(params?: CommentQuery) {
  return requestClient.get<PageResult<CommentVO>>('/noval/comment/list', { params });
}

/**
 * 导出消息跳转论坛列表
 * @param params
 * @returns 消息跳转论坛列表
 */
export function commentExport(params?: CommentQuery) {
  return commonExport('/noval/comment/export', params ?? {});
}

/**
 * 查询消息跳转论坛详情
 * @param newsId id
 * @returns 消息跳转论坛详情
 */
export function commentInfo(newsId: ID) {
  return requestClient.get<CommentVO>(`/noval/comment/${newsId}`);
}

/**
 * 新增消息跳转论坛
 * @param data
 * @returns void
 */
export function commentAdd(data: CommentForm) {
  return requestClient.postWithMsg<void>('/noval/comment', data);
}

/**
 * 更新消息跳转论坛
 * @param data
 * @returns void
 */
export function commentUpdate(data: CommentForm) {
  return requestClient.putWithMsg<void>('/noval/comment', data);
}

/**
 * 删除消息跳转论坛
 * @param newsId id
 * @returns void
 */
export function commentRemove(newsId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/comment/${newsId}`);
}
