import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { MOCK_USERS } from '~/utils/mock-data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const userId = getRouterParam(event, 'userId');
  
  // 查找用户信息
  const user = MOCK_USERS.find(u => u.id === Number(userId));
  
  if (!user) {
    setResponseStatus(event, 404);
    return useResponseError('UserNotFoundException', `User with id ${userId} not found`);
  }

  // 返回用户信息（不包含密码）
  const userVo = {
    userId: user.id,
    userName: user.username,
    nickName: user.realName,
    email: `${user.username}@example.com`,
    phonenumber: '13800138000',
    sex: '0',
    avatar: '',
    status: '0',
    delFlag: '0',
    loginIp: '127.0.0.1',
    loginDate: new Date().toISOString(),
    createBy: 'admin',
    createTime: '2023-01-01 00:00:00',
    updateBy: 'admin',
    updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
    remark: '',
    dept: {
      deptId: 100,
      parentId: 0,
      deptName: '若依科技',
      orderNum: 0,
      leader: user.realName,
      phone: '15888888888',
      email: '<EMAIL>',
      status: '0',
      delFlag: '0'
    },
    roles: user.roles.map((role, index) => ({
      roleId: index + 1,
      roleName: role,
      roleKey: role,
      roleSort: index + 1,
      dataScope: '1',
      menuCheckStrictly: false,
      deptCheckStrictly: false,
      status: '0',
      delFlag: '0',
      flag: false,
      admin: role === 'super'
    }))
  };

  return useResponseSuccess(userVo);
});