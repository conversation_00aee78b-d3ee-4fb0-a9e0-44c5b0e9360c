import type { PageQuery, BaseEntity } from '#/api/common';

export interface Live2dcontrollerVO {
  /**
   * 随机id
   */
  id: string | number;

  /**
   * 用户id
   */
  userid: string | number;

  /**
   * live2d模型名称name
   */
  name: string;

}

export interface Live2dcontrollerForm extends BaseEntity {
  /**
   * 随机id
   */
  id?: string | number;

  /**
   * 用户id
   */
  userid?: string | number;

  /**
   * live2d模型名称name
   */
  name?: string;

}

export interface Live2dcontrollerQuery extends PageQuery {
  /**
   * 用户id
   */
  userid?: string | number;

  /**
   * live2d模型名称name
   */
  name?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
