"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _error = _interopRequireDefault(await jitiImport("./error"));function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };}

process.env.COMPATIBILITY_DATE = new Date().toISOString();var _default = exports.default =
defineNitroConfig({
  devErrorHandler: _error.default,
  errorHandler: '~/error',
  routeRules: {
    '/api/**': {
      cors: true,
      headers: {
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Headers':
        'Accept, Authorization, Content-Length, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With',
        'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Expose-Headers': '*'
      }
    }
  }
}); /* v9-8d39df09df5e22fd */
