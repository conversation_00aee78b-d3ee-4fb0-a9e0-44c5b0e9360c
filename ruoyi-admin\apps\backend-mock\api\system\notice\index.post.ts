import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { getNextNoticeId, addNotice } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);

  // 生成新的ID
  const newId = getNextNoticeId();

  // 创建新的通知
  const newNotice = {
    noticeId: newId,
    noticeTitle: body.noticeTitle,
    noticeType: body.noticeType,
    noticeContent: body.noticeContent,
    status: body.status || '0', // 默认为正常状态
    remark: body.remark || '',
    createBy: userinfo.username,
    createByName: userinfo.realName,
    createTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }),
  };

  // 添加到数据存储
  addNotice(newNotice);

  return useResponseSuccess(newNotice);
});
