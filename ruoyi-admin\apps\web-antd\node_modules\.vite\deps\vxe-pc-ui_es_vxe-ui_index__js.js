import {
  config,
  setup,
  ui_default,
  version
} from "./chunk-URMRKTZM.js";
import {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  coreVersion,
  createEvent,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  use,
  useFns,
  usePermission,
  useSize,
  validators
} from "./chunk-BCXSQRR2.js";
import "./chunk-PJ5U4TG7.js";
import "./chunk-GE6DY3YU.js";
import "./chunk-KT3WABTJ.js";
import "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  config,
  coreVersion,
  createEvent,
  vxe_ui_default as default,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  use,
  useFns,
  usePermission,
  useSize,
  validators,
  version
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-ui_index__js.js.map
