#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules/nitropack/dist/cli/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules/nitropack/dist/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules/nitropack/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules/nitropack/dist/cli/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules/nitropack/dist/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules/nitropack/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13_idb-keyval@6.2.2/node_modules:/mnt/d/daima/new/ruoyi-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nitropack/dist/cli/index.mjs" "$@"
else
  exec node  "$basedir/../nitropack/dist/cli/index.mjs" "$@"
fi
