<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Live2D 模型展示</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: transparent;
    }
    .live2d-container {
      position: fixed;
      bottom: 0;
      right: 20px;
      z-index: 9999;
      pointer-events: none;
    }
    .live2d-canvas-container {
      position: relative;
      pointer-events: auto;
    }
    .live2d-tips {
      position: absolute;
      top: -80px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 14px;
      white-space: nowrap;
      max-width: 200px;
      text-align: center;
      animation: fadeInOut 0.3s ease-in-out;
      pointer-events: none;
      display: none;
    }
    .live2d-toolbar {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      pointer-events: auto;
      z-index: 10;
    }
    .toolbar-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      color: #666;
    }
    .toolbar-button:hover {
      background-color: rgba(255, 255, 255, 0.95);
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
      color: #333;
    }
    .toolbar-button svg {
      width: 24px;
      height: 24px;
      transition: transform 0.3s ease;
    }
    .toolbar-button:hover svg {
      transform: scale(1.1);
    }
    @keyframes fadeInOut {
      0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
      100% { opacity: 1; transform: translateX(-50%) translateY(0); }
    }
  </style>
</head>
<body>
  <div class="live2d-container">
    <!-- Live2D Canvas -->
    <div id="live2dContainer" class="live2d-canvas-container"></div>

    <!-- 提示框 -->
    <div id="tipsContainer" class="live2d-tips"></div>

    <!-- 工具栏 -->
    <div id="live2d-toolbar" class="live2d-toolbar">
      <div class="toolbar-button" onclick="switchModel((currentModelIndex + 1) % modelList.length)" title="切换模型">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M7.5 3.5C10.5 3.5 12 5.5 12 8c0-2.5 1.5-4.5 4.5-4.5C19.5 3.5 21 5.5 21 8c0 6-9 11-9 11S3 14 3 8C3 5.5 4.5 3.5 7.5 3.5z" fill="currentColor"/></svg>
      </div>
      <div class="toolbar-button" onclick="takeScreenshot()" title="复制并批注">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M15.728 9.686l-1.414-1.414L5 17.586V19h1.414l9.314-9.314zm1.414-1.414l1.414-1.414-1.414-1.414-1.414 1.414 1.414 1.414zM7.242 21H3v-4.243L16.435 3.322a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414L7.243 21h-.001z" fill="currentColor"/></svg>
      </div>
      <div class="toolbar-button" onclick="toggleVisibility('toolbar-button')" title="显示/隐藏">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16c3.691 0 6.915-2.534 7.736-6C18.915 9.534 15.691 7 12 7c-3.691 0-6.915 2.534-7.736 6 .82 3.466 4.045 6 7.736 6zm0-10c2.21 0 4 1.791 4 4s-1.79 4-4 4-4-1.791-4-4 1.79-4 4-4z" fill="currentColor"/></svg>
      </div>
    </div>
  </div>

  <!-- 加载PIXI.js和Live2D库 -->
  <script src="https://cdn.jsdelivr.net/npm/pixi.js@5.3.3/dist/pixi.min.js" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/pixi-live2d-display@0.4.0/dist/index.min.js" crossorigin="anonymous"></script>

  <script>
    // 启用 Live2D 的 Cubism 4 支持
    window.PIXI = PIXI;

    // Live2D 配置
    let LIVE2D_CONFIG = {
      // 基础设置
      width: 300,
      height: 400,
      scale: 0.3,
      position: { x: 0, y: 0 },

      // 功能开关
      autoPlay: true,
      showToolbar: true,
      enableInteraction: true,
      enableMessages: true,

      // 默认模型配置（将被API数据覆盖）
      models: [
        {
          id: 'ariu',
          name: 'Ariu',
          path: '/live2d/ariu/ariu.model3.json',
          preview: '/live2d/ariu/preview.png'
        }
      ],

      // 默认模型
      defaultModel: 'ariu',

      // 消息配置（将被API数据覆盖）
      messages: {
        welcome: ['你好呀！我是你的专属看板娘~'],
        click: {
          head: ['不要摸我的头啦！'],
          body: ['呀！好痒~']
        },
        random: ['今天天气不错呢！']
      },

      // 动画配置（将被API数据覆盖）
      animations: {
        idle: 'idle',
        tapHead: 'tap_head',
        tapBody: 'tap_body',
        greeting: 'greeting'
      }
    };

    // 从API获取模型列表和配置
    async function fetchLive2DConfig() {
      try {
        const response = await fetch('/api/live2d-models.json');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        // 更新配置
        if (data.models && data.models.length > 0) {
          LIVE2D_CONFIG.models = data.models;
        }

        if (data.messages) {
          LIVE2D_CONFIG.messages = data.messages;
        }

        if (data.animations) {
          LIVE2D_CONFIG.animations = data.animations;
        }

        console.log('Live2D配置已从API加载');
        return true;
      } catch (error) {
        console.error('获取Live2D配置失败:', error);
        return false;
      }
    }

    // 获取随机消息
    function getRandomMessage(category, subcategory) {
      const messages = LIVE2D_CONFIG.messages;

      if (subcategory) {
        const subMessages = messages[category]?.[subcategory];
        if (Array.isArray(subMessages) && subMessages.length > 0) {
          return subMessages[Math.floor(Math.random() * subMessages.length)];
        }
      }

      const categoryMessages = messages[category];
      if (Array.isArray(categoryMessages) && categoryMessages.length > 0) {
        return categoryMessages[Math.floor(Math.random() * categoryMessages.length)];
      }

      return '...';
    }

    // DOM 元素
    const live2dContainer = document.getElementById('live2dContainer');
    const tipsContainer = document.getElementById('tipsContainer');
    const switchModelBtn = document.getElementById('switchModelBtn');
    const switchTextureBtn = document.getElementById('switchTextureBtn');
    const showMessageBtn = document.getElementById('showMessageBtn');
    const screenshotBtn = document.getElementById('screenshotBtn');
    const hideBtn = document.getElementById('hideBtn');

    // PIXI 应用和模型
    let app = null;
    let model = null;
    let currentModelIndex = 0;
    let currentTextureIndex = 0;
    let isHidden = false;

    // 可用的模型列表
    let modelList = LIVE2D_CONFIG.models.map(model => model.path);

    // 监听来自父页面的消息
    window.addEventListener('message', function(event) {
      try {
        const data = event.data;

        // 确保消息是对象类型
        if (typeof data !== 'object' || data === null) return;

        // 检查是否在起点中文网
        const isQidian = isInQidian();
        console.log('消息处理 - 是否在起点中文网环境:', isQidian);
        console.log('收到消息类型:', data.type);

        // 处理不同类型的消息
        switch (data.type) {
          case 'switch-model':
            // 切换到指定模型
            if (data.modelId && LIVE2D_CONFIG && LIVE2D_CONFIG.models) {
              const modelIndex = LIVE2D_CONFIG.models.findIndex(m => m.id === data.modelId);
              if (modelIndex >= 0) {
                currentModelIndex = modelIndex;
                loadModel(modelList[currentModelIndex])
                  .then(() => {
                    console.log(`通过消息切换到模型: ${LIVE2D_CONFIG.models[modelIndex].name}`);

                    // 向父窗口发送模型切换成功消息
                    if (window.parent && window.parent !== window) {
                      try {
                        window.parent.postMessage({
                          type: 'live2d-model-switched',
                          modelId: data.modelId,
                          modelName: LIVE2D_CONFIG.models[modelIndex].name,
                          success: true
                        }, '*');
                        console.log('已向父窗口发送模型切换成功消息');
                      } catch (e) {
                        console.error('向父窗口发送模型切换消息失败:', e);
                      }
                    }
                  })
                  .catch(error => {
                    console.error('模型切换失败:', error);
                    // 向父窗口发送模型切换失败消息
                    if (window.parent && window.parent !== window) {
                      try {
                        window.parent.postMessage({
                          type: 'live2d-model-switched',
                          modelId: data.modelId,
                          success: false,
                          error: error.message || '模型加载失败'
                        }, '*');
                      } catch (e) {
                        console.error('向父窗口发送模型切换失败消息失败:', e);
                      }
                    }
                  });
              } else {
                console.error(`未找到ID为${data.modelId}的模型`);
                // 向父窗口发送模型未找到消息
                if (window.parent && window.parent !== window) {
                  try {
                    window.parent.postMessage({
                      type: 'live2d-model-switched',
                      modelId: data.modelId,
                      success: false,
                      error: `未找到ID为${data.modelId}的模型`
                    }, '*');
                  } catch (e) {
                    console.error('向父窗口发送模型未找到消息失败:', e);
                  }
                }
              }
            }
            break;

          case 'show-message':
            // 显示消息
            if (data.text) {
              showMessage(data.text, data.timeout || 3000);
              console.log('显示来自父窗口的消息:', data.text);

              // 确认消息已显示
              if (window.parent && window.parent !== window && data.requireConfirmation) {
                try {
                  window.parent.postMessage({
                    type: 'live2d-message-shown',
                    text: data.text,
                    success: true,
                    timestamp: Date.now()
                  }, '*');
                  console.log('已向父窗口确认消息显示');
                } catch (e) {
                  console.error('向父窗口发送消息确认失败:', e);
                }
              }
            }
            break;

          case 'toggle-visibility':
            // 切换显示/隐藏
            toggleVisibility(data.source || 'parent-command');

            // 更新工具栏按钮状态
            const toggleButton = document.querySelector('.toolbar-button[title="显示/隐藏"]') ||
                                document.querySelector('.toolbar-button[title="显示看板娘"]') ||
                                document.querySelector('.toolbar-button[title="隐藏看板娘"]');
            if (toggleButton) {
              toggleButton.title = isHidden ? '显示看板娘' : '隐藏看板娘';
              console.log('已更新工具栏按钮状态');
            }

            // 向父窗口发送状态变更消息
            if (window.parent && window.parent !== window) {
              try {
                window.parent.postMessage({
                  type: 'live2d-visibility-changed',
                  isHidden: isHidden,
                  source: data.source || 'parent-command',
                  timestamp: Date.now()
                }, '*');
                console.log('已向父窗口发送显示状态变更消息');
              } catch (e) {
                console.error('向父窗口发送消息失败:', e);
              }
            }
            break;

          case 'take-screenshot':
            // 截图
            takeScreenshot()
              .then(imageUrl => {
                console.log('通过消息触发截图成功');
                // 向父窗口发送截图成功消息
                if (window.parent && window.parent !== window) {
                  try {
                    window.parent.postMessage({
                      type: 'live2d-screenshot-taken',
                      imageUrl: imageUrl,
                      success: true,
                      timestamp: Date.now()
                    }, '*');
                  } catch (e) {
                    console.error('向父窗口发送截图成功消息失败:', e);
                  }
                }
              })
              .catch(error => {
                console.error('截图失败:', error);
                // 向父窗口发送截图失败消息
                if (window.parent && window.parent !== window) {
                  try {
                    window.parent.postMessage({
                      type: 'live2d-screenshot-taken',
                      success: false,
                      error: error.message || '截图失败',
                      timestamp: Date.now()
                    }, '*');
                  } catch (e) {
                    console.error('向父窗口发送截图失败消息失败:', e);
                  }
                }
              });
            break;

          case 'get-reading-progress':
            // 获取阅读进度（起点中文网专用）
            if (isQidian && window.parent && window.parent !== window) {
              try {
                // 尝试获取最新的书籍信息
                const bookInfo = getQidianBookInfo();

                // 更新全局配置中的书籍信息
                LIVE2D_CONFIG.qidianBookInfo = bookInfo;

                // 向父窗口发送阅读进度信息
                window.parent.postMessage({
                  type: 'live2d-reading-progress',
                  progress: bookInfo.readingProgress || null,
                  bookInfo: bookInfo,
                  timestamp: Date.now(),
                  success: true
                }, '*');
                console.log('已向父窗口发送阅读进度信息:', bookInfo.readingProgress);

                // 显示阅读进度消息
                if (bookInfo.readingProgress) {
                  showMessage(`当前阅读进度: ${bookInfo.readingProgress}`, 3000);

                  // 如果有总字数，显示额外信息
                  if (bookInfo.totalWords) {
                    setTimeout(() => {
                      showMessage(`本书总字数: ${bookInfo.totalWords}`, 3000);
                    }, 3500);
                  }
                } else {
                  showMessage('无法获取阅读进度', 2000);
                }
              } catch (e) {
                console.error('获取阅读进度失败:', e);
                window.parent.postMessage({
                  type: 'live2d-reading-progress',
                  error: e.message || '获取阅读进度失败',
                  success: false,
                  timestamp: Date.now()
                }, '*');
                showMessage('获取阅读进度失败', 2000);
              }
            } else if (!isQidian) {
              console.log('非起点中文网环境，忽略获取阅读进度请求');
              if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                  type: 'live2d-reading-progress',
                  error: '非起点中文网环境',
                  success: false,
                  timestamp: Date.now()
                }, '*');
              }
            }
            break;

          case 'qidian-chapter-changed':
            // 章节变更通知（起点中文网专用）
            if (isQidian) {
              try {
                // 重新获取书籍信息
                const bookInfo = getQidianBookInfo();

                // 更新全局配置中的书籍信息
                LIVE2D_CONFIG.qidianBookInfo = bookInfo;
                LIVE2D_CONFIG.chapterName = bookInfo.chapterName;

                // 显示章节信息
                if (bookInfo.bookName && bookInfo.chapterName) {
                  showMessage(`正在阅读《${bookInfo.bookName}》${bookInfo.chapterName}`, 3000);
                  console.log('章节变更，显示新章节信息');

                  // 如果有阅读进度，稍后显示
                  if (bookInfo.readingProgress) {
                    setTimeout(() => {
                      showMessage(`阅读进度: ${bookInfo.readingProgress}`, 2500);
                    }, 3500);
                  }
                }

                // 向父窗口确认章节变更处理完成
                if (window.parent && window.parent !== window) {
                  window.parent.postMessage({
                    type: 'live2d-chapter-change-processed',
                    chapterName: bookInfo.chapterName,
                    bookInfo: bookInfo,
                    success: true,
                    timestamp: Date.now()
                  }, '*');
                }
              } catch (e) {
                console.error('处理章节变更通知失败:', e);
                if (window.parent && window.parent !== window) {
                  window.parent.postMessage({
                    type: 'live2d-chapter-change-processed',
                    error: e.message || '处理章节变更通知失败',
                    success: false,
                    timestamp: Date.now()
                  }, '*');
                }
              }
            }
            break;

          case 'update-config':
            // 更新配置
            if (data.config && typeof data.config === 'object') {
              try {
                // 保存旧配置以便比较变化
                const oldConfig = JSON.parse(JSON.stringify(LIVE2D_CONFIG));

                // 合并配置
                LIVE2D_CONFIG = { ...LIVE2D_CONFIG, ...data.config };
                console.log('配置已更新:', data.config);

                // 如果更新了模型列表，重新加载模型列表
                if (data.config.models) {
                  modelList = LIVE2D_CONFIG.models.map(model => model.path);
                  console.log('模型列表已更新');
                }

                // 如果更新了消息，显示提示
                if (data.config.messages && JSON.stringify(oldConfig.messages) !== JSON.stringify(LIVE2D_CONFIG.messages)) {
                  showMessage('消息配置已更新', 2000);
                }

                // 确认配置更新
                if (window.parent && window.parent !== window) {
                  window.parent.postMessage({
                    type: 'live2d-config-updated',
                    success: true,
                    timestamp: Date.now(),
                    changes: Object.keys(data.config)
                  }, '*');
                }
              } catch (e) {
                console.error('更新配置失败:', e);
                if (window.parent && window.parent !== window) {
                  window.parent.postMessage({
                    type: 'live2d-config-updated',
                    success: false,
                    error: e.message || '更新配置失败',
                    timestamp: Date.now()
                  }, '*');
                }
              }
            }
            break;

          case 'get-model-list':
            // 获取模型列表
            if (window.parent && window.parent !== window) {
              try {
                window.parent.postMessage({
                  type: 'live2d-model-list',
                  models: LIVE2D_CONFIG.models || [],
                  currentModelIndex: currentModelIndex,
                  success: true,
                  timestamp: Date.now()
                }, '*');
                console.log('已向父窗口发送模型列表');
              } catch (e) {
                console.error('向父窗口发送模型列表失败:', e);
                window.parent.postMessage({
                  type: 'live2d-model-list',
                  success: false,
                  error: e.message || '获取模型列表失败',
                  timestamp: Date.now()
                }, '*');
              }
            }
            break;

          case 'get-book-info':
            // 获取书籍信息（起点中文网专用）
            if (isQidian && window.parent && window.parent !== window) {
              try {
                const bookInfo = getQidianBookInfo();
                window.parent.postMessage({
                  type: 'live2d-book-info',
                  bookInfo: bookInfo,
                  success: true,
                  timestamp: Date.now()
                }, '*');
                console.log('已向父窗口发送书籍信息');
              } catch (e) {
                console.error('获取书籍信息失败:', e);
                window.parent.postMessage({
                  type: 'live2d-book-info',
                  success: false,
                  error: e.message || '获取书籍信息失败',
                  timestamp: Date.now()
                }, '*');
              }
            } else if (!isQidian) {
              console.log('非起点中文网环境，忽略获取书籍信息请求');
              if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                  type: 'live2d-book-info',
                  success: false,
                  error: '非起点中文网环境',
                  timestamp: Date.now()
                }, '*');
              }
            }
            break;

          case 'reload-model':
            // 重新加载当前模型
            if (currentModelIndex >= 0 && currentModelIndex < modelList.length) {
              loadModel(modelList[currentModelIndex])
                .then(() => {
                  console.log('模型重新加载成功');
                  showMessage('模型已重新加载', 2000);

                  // 向父窗口发送重新加载成功消息
                  if (window.parent && window.parent !== window) {
                    try {
                      window.parent.postMessage({
                        type: 'live2d-model-reloaded',
                        success: true,
                        timestamp: Date.now()
                      }, '*');
                    } catch (e) {
                      console.error('向父窗口发送模型重新加载成功消息失败:', e);
                    }
                  }
                })
                .catch(error => {
                  console.error('模型重新加载失败:', error);
                  showMessage('模型重新加载失败', 2000);

                  // 向父窗口发送重新加载失败消息
                  if (window.parent && window.parent !== window) {
                    try {
                      window.parent.postMessage({
                        type: 'live2d-model-reloaded',
                        success: false,
                        error: error.message || '模型重新加载失败',
                        timestamp: Date.now()
                      }, '*');
                    } catch (e) {
                      console.error('向父窗口发送模型重新加载失败消息失败:', e);
                    }
                  }
                });
            }
            break;

          case 'ping':
            // 心跳检测
            if (window.parent && window.parent !== window) {
              try {
                window.parent.postMessage({
                  type: 'live2d-pong',
                  timestamp: Date.now(),
                  isQidian: isQidian,
                  modelLoaded: !!model,
                  isHidden: isHidden
                }, '*');
              } catch (e) {
                console.error('向父窗口发送心跳响应失败:', e);
              }
            }
            break;

          default:
            // 忽略未知类型的消息
            console.log('收到未处理的消息类型:', data.type);
            break;
        }
      } catch (error) {
        console.error('处理消息时出错:', error);

        // 向父窗口发送错误消息
        if (window.parent && window.parent !== window) {
          try {
            window.parent.postMessage({
              type: 'live2d-error',
              error: error.message || '处理消息时出错',
              source: 'message-handler',
              messageType: event.data?.type || 'unknown',
              timestamp: Date.now()
            }, '*');
          } catch (e) {
            console.error('向父窗口发送错误消息失败:', e);
          }
        }
      }
    });

    // 初始化 PIXI 应用
    async function initPixiApp() {
      if (!live2dContainer) {
        console.error('找不到Live2D容器元素');
        return;
      }

      try {
        console.log('开始初始化PIXI应用');

        // 检查是否在起点中文网
        const isQidian = window.location.hostname.includes('qidian.com');
        console.log('是否在起点中文网环境:', isQidian);

        // 创建 PIXI 应用
        const pixiOptions = {
          width: LIVE2D_CONFIG.width || 300,
          height: LIVE2D_CONFIG.height || 400,
          backgroundColor: 0x000000,
          backgroundAlpha: 0,
          antialias: true,
          resolution: window.devicePixelRatio || 1,
          autoDensity: true,
          // 在起点中文网环境下添加额外选项
          forceCanvas: isQidian, // 在起点中文网使用Canvas渲染器可能更稳定
          powerPreference: 'low-power' // 低功耗模式，对移动设备更友好
        };

        console.log('PIXI应用配置:', pixiOptions);
        app = new PIXI.Application(pixiOptions);

        // 设置canvas属性
        if (app.view) {
          // 添加crossOrigin属性以支持跨域
          if (isQidian) {
            app.view.crossOrigin = 'anonymous';
          }

          // 设置canvas样式
          app.view.style.display = 'block';
          app.view.style.margin = '0 auto';

          // 将 canvas 添加到容器
          live2dContainer.appendChild(app.view);
          console.log('Canvas已添加到容器');
        } else {
          console.error('PIXI应用创建成功但canvas元素不存在');
        }

        // 更新模型列表
        modelList = LIVE2D_CONFIG.models.map(model => model.path);
        console.log('模型列表已更新:', modelList);

        // 如果在起点中文网，添加额外的错误处理
        if (isQidian) {
          // 设置全局错误处理
          window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error || event.message);
            if (event.error && event.error.toString().includes('Live2D')) {
              showMessage('Live2D模型加载出错，但我仍然会陪你阅读！', 5000);
            }
          }, false);

          // 设置未捕获的Promise错误处理
          window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise错误:', event.reason);
            if (event.reason && event.reason.toString().includes('Live2D')) {
              showMessage('Live2D模型加载出错，但我仍然会陪你阅读！', 5000);
            }
          });
        }

        // 加载当前选择的模型
        if (modelList.length > 0 && currentModelIndex >= 0 && currentModelIndex < modelList.length) {
          console.log(`准备加载模型: ${modelList[currentModelIndex]}`);
          await loadModel(modelList[currentModelIndex]);
        } else {
          console.error('没有可用的模型或模型索引无效');
          showMessage('没有可用的模型', 3000);
        }

      } catch (error) {
        console.error('初始化PIXI应用失败:', error);
        showMessage('初始化失败，请刷新页面重试', 5000);

        // 如果在起点中文网，显示特殊消息
        if (window.location.hostname.includes('qidian.com')) {
          setTimeout(() => {
            showMessage('虽然模型加载失败，但我仍然会陪你阅读！', 5000);
          }, 5500);
        }
      }
    }

    // 加载 Live2D 模型
    async function loadModel(modelPath) {
      if (!app) {
        console.error('PIXI应用未初始化，无法加载模型');
        return;
      }

      try {
        // 显示加载中消息
        showMessage('正在加载模型...', 10000);

        // 移除旧模型
        if (model) {
          app.stage.removeChild(model);
          model.destroy();
          console.log('已移除旧模型');
        }

        console.log(`正在加载模型: ${modelPath}`);

        // 检查是否在起点中文网
        const isQidian = window.location.hostname.includes('qidian.com');

        // 加载新模型
        try {
          // 添加跨域设置
          const modelOptions = {
            crossOrigin: 'anonymous'
          };

          model = await PIXI.live2d.Live2DModel.from(modelPath, modelOptions);
          console.log('模型加载成功');
        } catch (modelError) {
          console.error('模型加载失败，详细错误:', modelError);
          throw modelError;
        }

        if (model) {
          // 设置模型属性
          model.scale.set(LIVE2D_CONFIG.scale);
          model.position.set(LIVE2D_CONFIG.position.x, LIVE2D_CONFIG.position.y);

          // 添加到舞台
          app.stage.addChild(model);
          console.log('模型已添加到舞台');

          // 设置交互
          setupModelInteraction();

          // 自动播放动画
          if (LIVE2D_CONFIG.autoPlay && model.internalModel.motionManager) {
            model.motion('idle');
            console.log('已开始播放idle动画');
          }

          console.log('Live2D模型加载成功');

          // 准备欢迎消息
          let welcomeMessage = getRandomMessage('welcome');

          // 如果有书籍和章节信息，优先使用
          if (LIVE2D_CONFIG.bookName && LIVE2D_CONFIG.chapterName) {
            welcomeMessage = `欢迎阅读《${LIVE2D_CONFIG.bookName}》${LIVE2D_CONFIG.chapterName}`;
            console.log('使用书籍信息作为欢迎消息');
          } else if (isQidian) {
            // 在起点中文网尝试再次获取书籍信息
            try {
              const bookTitleElem = document.querySelector('.book-info-title') ||
                                   document.querySelector('.book-title') ||
                                   document.querySelector('.page-header h1');
              const chapterTitleElem = document.querySelector('.j_chapterName') ||
                                     document.querySelector('.chapter-name') ||
                                     document.querySelector('.j-catalogTitle');

              if (bookTitleElem && chapterTitleElem) {
                const bookTitle = bookTitleElem.textContent.trim();
                const chapterTitle = chapterTitleElem.textContent.trim();
                welcomeMessage = `欢迎阅读《${bookTitle}》${chapterTitle}`;
                console.log('从DOM获取到书籍信息作为欢迎消息');
              }
            } catch (e) {
              console.error('获取起点中文网书籍信息失败:', e);
            }
          }

          showMessage(welcomeMessage);

          // 更新页面标题，显示当前模型名称
          const currentModel = LIVE2D_CONFIG.models[currentModelIndex];
          if (currentModel && currentModel.name) {
            document.title = `Live2D - ${currentModel.name}`;
          }

          // 如果是在iframe中，通知父窗口模型已加载成功
          if (window.parent !== window) {
            try {
              const messageData = {
                type: 'live2d-loaded',
                modelId: currentModel?.id || '',
                modelName: currentModel?.name || ''
              };

              // 如果有书籍信息，也一并发送
              if (LIVE2D_CONFIG.bookName) {
                messageData.bookName = LIVE2D_CONFIG.bookName;
              }
              if (LIVE2D_CONFIG.chapterName) {
                messageData.chapterName = LIVE2D_CONFIG.chapterName;
              }

              console.log('向父窗口发送消息:', messageData);
              window.parent.postMessage(messageData, '*');
            } catch (e) {
              console.error('无法向父窗口发送消息:', e);
            }
          }
        }

      } catch (error) {
        console.error('模型加载失败:', error);
        showMessage('模型加载失败，请检查模型文件');

        // 尝试加载默认模型
        if (modelPath !== LIVE2D_CONFIG.models[0].path) {
          console.log('尝试加载默认模型...');
          currentModelIndex = 0;
          await loadModel(LIVE2D_CONFIG.models[0].path);
        } else {
          console.error('默认模型也加载失败，无法恢复');

          // 如果在起点中文网，显示特殊消息
          if (window.location.hostname.includes('qidian.com')) {
            showMessage('模型加载失败，但我仍然会陪你阅读！', 5000);
          }
        }
      }
    }

    // 设置模型交互
    function setupModelInteraction() {
      if (!model) {
        console.error('模型不存在，无法设置交互');
        return;
      }

      console.log('设置模型交互');

      // 检查是否在起点中文网
      const isQidian = window.location.hostname.includes('qidian.com');
      console.log('是否在起点中文网环境:', isQidian);

      try {
        model.interactive = true;
        model.buttonMode = true;
        console.log('模型交互属性已设置');

        // 点击事件
        model.on('pointerdown', (event) => {
          try {
            const point = event.data.global;
            console.log('模型被点击，坐标:', point.x, point.y);

            // 检测点击区域
            if (model?.hitTest('head', point.x, point.y)) {
              console.log('点击了头部区域');
              showMessage(getRandomMessage('click', 'head'));
              if (model?.motion) {
                model.motion(LIVE2D_CONFIG.animations.tapHead || 'tap_head');
              }
            } else if (model?.hitTest('body', point.x, point.y)) {
              console.log('点击了身体区域');
              showMessage(getRandomMessage('click', 'body'));
              if (model?.motion) {
                model.motion(LIVE2D_CONFIG.animations.tapBody || 'tap_body');
              }
            } else {
              console.log('点击了其他区域');
              showRandomMessage();
            }

            // 如果在起点中文网，尝试获取当前阅读进度
            if (isQidian) {
              try {
                const progressElem = document.querySelector('.chapter-control .progress') ||
                                    document.querySelector('.read-opt-footer .progress');
                if (progressElem) {
                  const progress = progressElem.textContent.trim();
                  if (progress) {
                    setTimeout(() => {
                      showMessage(`当前阅读进度: ${progress}`, 3000);
                    }, 3000);
                  }
                }
              } catch (e) {
                console.error('获取阅读进度失败:', e);
              }
            }
          } catch (error) {
            console.error('处理点击事件时出错:', error);
          }
        });

        // 鼠标悬停
        model.on('pointerover', () => {
          console.log('鼠标悬停在模型上');

          // 在起点中文网显示特殊消息
          if (isQidian) {
            const messages = [
              '你在看我吗？',
              '喜欢这本书吗？',
              '需要我为你朗读吗？',
              '这一章真精彩！',
              '别忘了收藏这本书哦！'
            ];
            const randomIndex = Math.floor(Math.random() * messages.length);
            showMessage(messages[randomIndex]);
          } else {
            showMessage('你在看我吗？');
          }
        });

        // 鼠标移出
        model.on('pointerout', () => {
          console.log('鼠标移出模型');
        });

        console.log('模型交互事件已注册');
      } catch (error) {
        console.error('设置模型交互时出错:', error);
      }
    }

    // 显示消息
    function showMessage(message, duration = 3000) {
      if (!tipsContainer) {
        console.error('无法显示消息：消息容器不存在');
        return;
      }

      console.log('显示消息:', message, '超时时间:', duration);

      try {
        // 检查是否在起点中文网
        const isQidian = window.location.hostname.includes('qidian.com');

        // 在起点中文网环境下，确保消息元素可见
        if (isQidian) {
          // 确保消息元素样式适合起点中文网
          tipsContainer.style.zIndex = '10000';
          tipsContainer.style.background = 'rgba(0, 0, 0, 0.7)';
          tipsContainer.style.color = '#fff';
          tipsContainer.style.borderRadius = '8px';
          tipsContainer.style.padding = '10px 15px';
          tipsContainer.style.fontSize = '14px';
          tipsContainer.style.fontWeight = 'bold';
          tipsContainer.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.5)';
          tipsContainer.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        }

        // 设置消息内容
        tipsContainer.textContent = message;
        tipsContainer.style.display = 'block';

        // 设置新的定时器
        setTimeout(() => {
          tipsContainer.style.display = 'none';
        }, duration);

        // 在起点中文网环境下，确保消息可见
        if (isQidian) {
          // 强制重绘以确保消息显示
          setTimeout(() => {
            if (tipsContainer.style.display !== 'block') {
              console.log('强制显示消息元素');
              tipsContainer.style.display = 'block';
            }
          }, 100);
        }
      } catch (error) {
        console.error('显示消息时出错:', error);
      }
    }

    // 隐藏Live2D模型
    function hideLive2D(source) {
      try {
        console.log(`隐藏Live2D模型, 来源: ${source || '用户操作'}`);

        // 检查是否在起点中文网
        const isQidian = isInQidian();
        console.log('是否在起点中文网环境:', isQidian);

        const container = live2dContainer.parentElement;
        if (container) {
          container.style.display = 'none';
          console.log('已隐藏Live2D容器');

          // 在起点中文网环境下，使用特定的隐藏消息
          if (isQidian) {
            showMessage('我先隐藏一下，你专心阅读吧~');

            // 保存隐藏状态到localStorage
            try {
              localStorage.setItem('live2d_hidden', 'true');
              console.log('已保存隐藏状态到localStorage');
            } catch (e) {
              console.error('保存隐藏状态失败:', e);
            }
          } else {
            showMessage('我先隐藏一下~');
          }
        } else {
          console.error('找不到Live2D容器的父元素');
        }

        isHidden = true;

        // 向父窗口发送状态变更消息
        if (window.parent && window.parent !== window) {
          try {
            window.parent.postMessage({
              type: 'live2d-visibility-changed',
              isHidden: true,
              source: source || 'user-action',
              timestamp: Date.now()
            }, '*');
            console.log('已向父窗口发送隐藏状态消息');
          } catch (e) {
            console.error('向父窗口发送消息失败:', e);
          }
        }
      } catch (error) {
        console.error('隐藏Live2D时出错:', error);
        isHidden = true; // 确保状态正确
      }
    }

    // 显示Live2D模型
    function showLive2D(source) {
      try {
        console.log(`显示Live2D模型, 来源: ${source || '用户操作'}`);

        // 检查是否在起点中文网
        const isQidian = isInQidian();
        console.log('是否在起点中文网环境:', isQidian);

        const container = live2dContainer.parentElement;
        if (container) {
          container.style.display = 'block';
          console.log('已显示Live2D容器');

          // 在起点中文网环境下显示特定消息
          if (isQidian) {
            try {
              // 尝试获取当前书籍和章节信息
              const bookInfo = getQidianBookInfo();

              if (bookInfo && bookInfo.bookTitle && bookInfo.chapterTitle) {
                showMessage(`我回来啦！继续陪你阅读《${bookInfo.bookTitle}》的${bookInfo.chapterTitle}~`);
              } else {
                const bookTitle = document.querySelector('.book-info h1')?.textContent ||
                                document.querySelector('.book-title')?.textContent;
                const chapterTitle = document.querySelector('.j_chapterName')?.textContent ||
                                   document.querySelector('.chapter-name')?.textContent;

                if (bookTitle && chapterTitle) {
                  showMessage(`我回来啦！继续陪你阅读《${bookTitle}》的${chapterTitle}~`);
                } else {
                  showMessage('我回来啦！继续陪你阅读~');
                }
              }

              // 保存显示状态到localStorage
              localStorage.setItem('live2d_hidden', 'false');
              console.log('已保存显示状态到localStorage');
            } catch (e) {
              console.error('处理起点中文网特定逻辑失败:', e);
              showMessage('我回来啦！');
            }
          } else {
            showMessage('我回来啦！');
          }
        } else {
          console.error('找不到Live2D容器的父元素');
        }

        isHidden = false;

        // 向父窗口发送状态变更消息
        if (window.parent && window.parent !== window) {
          try {
            window.parent.postMessage({
              type: 'live2d-visibility-changed',
              isHidden: false,
              source: source || 'user-action',
              timestamp: Date.now(),
              bookInfo: isQidian ? getQidianBookInfo() : null
            }, '*');
            console.log('已向父窗口发送显示状态消息');
          } catch (e) {
            console.error('向父窗口发送消息失败:', e);
          }
        }
      } catch (error) {
        console.error('显示Live2D时出错:', error);
        isHidden = false; // 确保状态正确
      }
    }

    // 切换显示/隐藏
    function toggleVisibility(source) {
      try {
        console.log(`切换Live2D模型显示/隐藏状态, 当前状态: ${isHidden ? '隐藏' : '显示'}, 来源: ${source || '用户操作'}`);

        // 保存旧状态用于判断是否有变化
        const oldHiddenState = isHidden;

        if (isHidden) {
          showLive2D(source);
        } else {
          hideLive2D(source);
        }

        // 确保状态已更新
        isHidden = !oldHiddenState;

        // 检查是否在起点中文网
        const isQidian = isInQidian();

        // 更新工具栏按钮状态
        const toggleButton = document.querySelector('.toolbar-button[title="显示/隐藏"]') ||
                           document.querySelector('.toolbar-button[title="显示看板娘"]') ||
                           document.querySelector('.toolbar-button[title="隐藏看板娘"]');
        if (toggleButton) {
          toggleButton.title = isHidden ? '显示看板娘' : '隐藏看板娘';
          console.log('已更新工具栏按钮状态');
        }

        // 向父窗口发送状态变更消息
        if (window.parent && window.parent !== window) {
          try {
            window.parent.postMessage({
              type: 'live2d-visibility-changed',
              isHidden: isHidden,
              source: source || 'user-action',
              timestamp: Date.now()
            }, '*');
            console.log('已向父窗口发送显示状态变更消息');
          } catch (e) {
            console.error('向父窗口发送消息失败:', e);
          }
        }

        return isHidden;
      } catch (error) {
        console.error('切换显示/隐藏状态时出错:', error);
        return isHidden;
      }
    }

    // 截图功能
    function takeScreenshot() {
      if (!app || !model) {
        showMessage('无法截图，模型未加载');
        return;
      }

      try {
        // 获取用户选中的文字
        const selectedText = window.getSelection()?.toString() || '';

        // 创建一个临时的canvas元素
        const canvas = document.createElement('canvas');
        canvas.width = app.view.width;
        canvas.height = app.view.height;

        // 获取canvas上下文并绘制当前画面
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 将PIXI应用的内容绘制到canvas上
        const renderer = app.renderer;
        const gl = renderer.gl;
        const pixels = new Uint8Array(gl.drawingBufferWidth * gl.drawingBufferHeight * 4);
        gl.readPixels(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight, gl.RGBA, gl.UNSIGNED_BYTE, pixels);

        // 创建一个新的ImageData对象
        const imageData = new ImageData(new Uint8ClampedArray(pixels), gl.drawingBufferWidth, gl.drawingBufferHeight);

        // 将ImageData绘制到canvas上（需要翻转Y轴）
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = gl.drawingBufferWidth;
        tempCanvas.height = gl.drawingBufferHeight;
        const tempCtx = tempCanvas.getContext('2d');
        tempCtx.putImageData(imageData, 0, 0);

        // 翻转并绘制到最终canvas
        ctx.save();
        ctx.scale(1, -1);
        ctx.drawImage(tempCanvas, 0, -canvas.height, canvas.width, canvas.height);
        ctx.restore();

        // 将canvas转换为图片URL
        const dataURL = canvas.toDataURL('image/png');

        // 创建下载链接
        const link = document.createElement('a');
        // 如果有选中文字，则将文件名设置为选中的文字（限制长度防止文件名过长）
        const filename = selectedText
          ? `${selectedText.substring(0, 30)}.png`
          : `live2d_${new Date().getTime()}.png`;
        link.download = filename;
        link.href = dataURL;
        link.click();

        // 根据是否有选中文字显示不同的消息
        if (selectedText) {
          showMessage(`已保存「${selectedText.length > 10 ? selectedText.substring(0, 10) + '...' : selectedText}」的截图！`);
        } else {
          showMessage('截图已保存');
        }
      } catch (error) {
        console.error('截图失败:', error);
        showMessage('截图失败，请重试');
      }
    }

    // 显示随机消息
    function showRandomMessage() {
      try {
        console.log('显示随机消息');

        // 检查是否在起点中文网
        const isQidian = window.location.hostname.includes('qidian.com');
        console.log('是否在起点中文网环境:', isQidian);

        if (isQidian) {
          // 在起点中文网环境下，显示与阅读相关的随机消息
          const qidianMessages = [
            '这本书真好看！',
            '要不要给作者打个赏？',
            '记得收藏这本书哦！',
            '这章节太精彩了！',
            '别忘了投票支持作者！',
            '要不要看看作者的其他作品？',
            '喜欢这本书就给它加个书签吧！',
            '这个情节真是出人意料！',
            '这个角色太有魅力了！',
            '不知道接下来会发生什么...',
            '作者的文笔真是太棒了！'
          ];

          // 尝试获取当前书籍和章节信息
          try {
            const bookTitle = document.querySelector('.book-info h1')?.textContent ||
                             document.querySelector('.book-title')?.textContent;
            const chapterTitle = document.querySelector('.j_chapterName')?.textContent ||
                                document.querySelector('.chapter-name')?.textContent;

            if (bookTitle && chapterTitle) {
              // 有书籍和章节信息时，有几率显示相关消息
              if (Math.random() < 0.3) {
                showMessage(`《${bookTitle}》的${chapterTitle}真精彩！`);
                return;
              }
            }
          } catch (e) {
            console.error('获取书籍信息失败:', e);
          }

          // 随机选择一条起点中文网相关消息
          const randomIndex = Math.floor(Math.random() * qidianMessages.length);
          showMessage(qidianMessages[randomIndex]);
        } else {
          // 非起点中文网环境，使用默认的随机消息
          const randomTip = getRandomMessage('random');
          showMessage(randomTip);
        }
      } catch (error) {
        console.error('显示随机消息时出错:', error);
        // 出错时仍尝试显示默认随机消息
        const randomTip = getRandomMessage('random');
        showMessage(randomTip);
      }
    }

    // 切换模型
    async function switchModel(index) {
      try {
        console.log('切换模型，索引:', index);

        if (!modelList || modelList.length === 0) {
          showMessage('没有可用的模型');
          return;
        }

        // 确保索引在有效范围内
        index = Math.max(0, Math.min(index, modelList.length - 1));

        if (index === currentModelIndex && model) {
          showMessage('已经是当前模型了');
          return;
        }

        // 检查是否在起点中文网
        const isQidian = window.location.hostname.includes('qidian.com');
        console.log('是否在起点中文网环境:', isQidian);

        currentModelIndex = index;
        showMessage(`正在切换到${LIVE2D_CONFIG.models[index].name}...`, 3000);

        // 在起点中文网环境下，添加额外的错误处理
        if (isQidian) {
          try {
            // 加载新模型
            await loadModel(modelList[currentModelIndex]);
          } catch (qidianError) {
            console.error('在起点中文网环境下加载模型失败:', qidianError);
            showMessage('模型加载失败，但我仍然会陪你阅读！', 5000);
          }
        } else {
          // 非起点中文网环境，正常加载模型
          await loadModel(modelList[currentModelIndex]);
        }
      } catch (error) {
        console.error('切换模型时出错:', error);
        showMessage('切换模型失败，请稍后再试', 3000);
      }
    }

    // 切换材质
    function switchTexture() {
      if (!model || !model.internalModel.textures) {
        showMessage('当前模型不支持换装~');
        return;
      }

      const textures = model.internalModel.textures;
      if (textures.length <= 1) {
        showMessage('暂时没有其他衣服~');
        return;
      }

      currentTextureIndex = (currentTextureIndex + 1) % textures.length;
      // 这里需要根据具体的 Live2D 版本实现材质切换
      showMessage('换了新衣服！');
    }

    // 复制文字到剪贴板并显示批注弹窗
    function takeScreenshot() {
      try {
        console.log('尝试复制文字并打开批注弹窗');

        // 获取用户选中的文字
        const selectedText = window.getSelection()?.toString() || '';
        console.log('用户选中的文字:', selectedText);

        // 直接处理文字复制和跳转
        if (!selectedText.trim()) {
          showMessage('请先选择要复制的文字');
          return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
          try {

            // 复制文字到剪贴板

            try {
              // 现代浏览器API
              navigator.clipboard.writeText(selectedText).then(() => {
                console.log('文字已复制到剪贴板（使用现代API）');
              }).catch(err => {
                console.error('使用现代API复制失败:', err);
                // 回退到传统方法
                const textArea = document.createElement('textarea');
                textArea.value = selectedText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                console.log('文字已复制到剪贴板（使用传统方法）');
              });
            } catch (e) {
              console.error('复制文字失败:', e);
            }
            
            // 不再将数据存储到 sessionStorage，避免跳转到批注页面时自动打开批注表单
            // const exegesisData = {
            //   novalContent: selectedText,
            //   novalName: '未知小说',
            //   novalChapter: '未知章节',
            //   novalId: 0
            // };
            // sessionStorage.setItem('exegesis_data', JSON.stringify(exegesisData));
            
            // 显示消息
            showMessage(`已复制「${selectedText.length > 10 ? selectedText.substring(0, 10) + '...' : selectedText}」并打开批注弹窗`);
            
            // 延迟一下再显示弹窗，让用户看到消息
            setTimeout(() => {
              // 创建批注弹窗
              createExegesisModal(selectedText);
            }, 1000);

            // 向父窗口发送复制成功消息
            if (window.parent && window.parent !== window) {
              window.parent.postMessage({
                type: 'live2d-text-copied',
                text: selectedText,
                success: true,
                timestamp: Date.now()
              }, '*');
            }

            resolve(selectedText);
          } catch (error) {
            console.error('复制文字过程中出错:', error);
            showMessage('复制文字失败，请稍后再试~');
            reject(error);
          }
        });
      } catch (error) {
        console.error('复制文字失败:', error);
        showMessage('复制文字失败了，请稍后再试~');
        return Promise.reject(error);
      }
    }

    // 创建批注弹窗
    function createExegesisModal(selectedText) {
      // 创建弹窗容器
      const modalContainer = document.createElement('div');
      modalContainer.className = 'exegesis-modal-container';
      modalContainer.style.position = 'fixed';
      modalContainer.style.top = '0';
      modalContainer.style.left = '0';
      modalContainer.style.width = '100%';
      modalContainer.style.height = '100%';
      modalContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      modalContainer.style.display = 'flex';
      modalContainer.style.justifyContent = 'center';
      modalContainer.style.alignItems = 'center';
      modalContainer.style.zIndex = '10000';
      
      // 创建弹窗内容
      const modalContent = document.createElement('div');
      modalContent.className = 'exegesis-modal-content';
      modalContent.style.backgroundColor = '#fff';
      modalContent.style.borderRadius = '8px';
      modalContent.style.padding = '20px';
      modalContent.style.width = '80%';
      modalContent.style.maxWidth = '600px';
      modalContent.style.maxHeight = '80vh';
      modalContent.style.overflowY = 'auto';
      modalContent.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
      
      // 创建标题
      const modalTitle = document.createElement('h3');
      modalTitle.textContent = '添加批注';
      modalTitle.style.margin = '0 0 15px 0';
      modalTitle.style.borderBottom = '1px solid #eee';
      modalTitle.style.paddingBottom = '10px';
      modalTitle.style.fontSize = '18px';
      modalTitle.style.fontWeight = 'bold';
      
      // 创建选中文字显示区域
      const selectedTextDiv = document.createElement('div');
      selectedTextDiv.className = 'selected-text';
      selectedTextDiv.style.padding = '10px';
      selectedTextDiv.style.backgroundColor = '#f5f5f5';
      selectedTextDiv.style.borderRadius = '4px';
      selectedTextDiv.style.marginBottom = '15px';
      selectedTextDiv.style.fontSize = '14px';
      selectedTextDiv.style.lineHeight = '1.5';
      selectedTextDiv.textContent = selectedText;
      
      // 创建批注输入框
      const exegesisTextarea = document.createElement('textarea');
      exegesisTextarea.className = 'exegesis-textarea';
      exegesisTextarea.placeholder = '请输入您的批注...';
      exegesisTextarea.style.width = '100%';
      exegesisTextarea.style.minHeight = '120px';
      exegesisTextarea.style.padding = '10px';
      exegesisTextarea.style.boxSizing = 'border-box';
      exegesisTextarea.style.border = '1px solid #ddd';
      exegesisTextarea.style.borderRadius = '4px';
      exegesisTextarea.style.resize = 'vertical';
      exegesisTextarea.style.marginBottom = '15px';
      exegesisTextarea.style.fontSize = '14px';
      
      // 创建按钮容器
      const buttonContainer = document.createElement('div');
      buttonContainer.style.display = 'flex';
      buttonContainer.style.justifyContent = 'flex-end';
      buttonContainer.style.gap = '10px';
      
      // 创建取消按钮
      const cancelButton = document.createElement('button');
      cancelButton.textContent = '取消';
      cancelButton.style.padding = '8px 16px';
      cancelButton.style.backgroundColor = '#f5f5f5';
      cancelButton.style.border = '1px solid #ddd';
      cancelButton.style.borderRadius = '4px';
      cancelButton.style.cursor = 'pointer';
      cancelButton.style.fontSize = '14px';
      cancelButton.onclick = () => {
        document.body.removeChild(modalContainer);
      };
      
      // 创建保存按钮
      const saveButton = document.createElement('button');
      saveButton.textContent = '保存';
      saveButton.style.padding = '8px 16px';
      saveButton.style.backgroundColor = '#4CAF50';
      saveButton.style.color = 'white';
      saveButton.style.border = 'none';
      saveButton.style.borderRadius = '4px';
      saveButton.style.cursor = 'pointer';
      saveButton.style.fontSize = '14px';
      saveButton.onclick = () => {
        const exegesisText = exegesisTextarea.value.trim();
        if (!exegesisText) {
          alert('请输入批注内容');
          return;
        }
        
        // 保存批注数据
        saveExegesis(selectedText, exegesisText);
        
        // 关闭弹窗
        document.body.removeChild(modalContainer);
        
        // 显示保存成功消息
        showMessage('批注已保存');
      };
      
      // 组装弹窗
      buttonContainer.appendChild(cancelButton);
      buttonContainer.appendChild(saveButton);
      
      modalContent.appendChild(modalTitle);
      modalContent.appendChild(selectedTextDiv);
      modalContent.appendChild(exegesisTextarea);
      modalContent.appendChild(buttonContainer);
      
      modalContainer.appendChild(modalContent);
      
      // 添加点击外部关闭弹窗的功能
      modalContainer.addEventListener('click', (e) => {
        if (e.target === modalContainer) {
          document.body.removeChild(modalContainer);
        }
      });
      
      // 添加到页面
      document.body.appendChild(modalContainer);
      
      // 聚焦到输入框
      exegesisTextarea.focus();
    }
    
    // 保存批注数据
    function saveExegesis(selectedText, exegesisText) {
      // 获取已有的批注数据
      let exegesisList = [];
      try {
        const savedData = localStorage.getItem('exegesis_list');
        if (savedData) {
          exegesisList = JSON.parse(savedData);
        }
      } catch (e) {
        console.error('读取批注数据失败:', e);
      }
      
      // 添加新批注
      exegesisList.push({
        id: Date.now(),
        content: selectedText,
        exegesis: exegesisText,
        novalName: '未知小说',
        novalChapter: '未知章节',
        novalId: 0,
        createTime: new Date().toISOString()
      });
      
      // 保存到localStorage
      try {
        localStorage.setItem('exegesis_list', JSON.stringify(exegesisList));
        console.log('批注保存成功');
      } catch (e) {
        console.error('保存批注数据失败:', e);
        alert('保存批注失败，可能是存储空间不足');
      }
      
      // 向父窗口发送批注保存成功消息
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          type: 'live2d-exegesis-saved',
          success: true,
          timestamp: Date.now()
        }, '*');
      }
    }

    // 隐藏Live2D (旧版本兼容函数)
    function hideLive2D(source = 'legacy-code') {
      const container = live2dContainer.parentElement;
      if (container) {
        container.style.display = 'none';
        showMessage('我先隐藏一下~');

        // 保存隐藏状态到localStorage
        try {
          localStorage.setItem('live2dDisplayState', 'hidden');
        } catch (e) {
          console.error('保存Live2D显示状态失败:', e);
        }

        // 向父窗口发送可见性变更消息
        try {
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'live2d-visibility-changed',
              isHidden: true,
              source: source,
              timestamp: new Date().getTime()
            }, '*');
          }
        } catch (e) {
          console.error('发送Live2D可见性变更消息失败:', e);
        }
      }
    }

    // 显示Live2D
    function showLive2D() {
      const container = live2dContainer.parentElement;
      if (container) {
        container.style.display = 'block';
        showMessage('我回来啦！');
      }
    }

    // 事件监听
    switchModelBtn.addEventListener('click', switchModel);
    switchTextureBtn.addEventListener('click', switchTexture);
    showMessageBtn.addEventListener('click', showRandomMessage);
    screenshotBtn.addEventListener('click', takeScreenshot);
    hideBtn.addEventListener('click', () => hideLive2D('hide-button'));

    // 判断是否在起点中文网
    function isInQidian() {
      try {
        console.log('检查是否在起点中文网环境');

        // 检查域名
        const hostname = window.location.hostname;
        const isQidianDomain = hostname.includes('qidian.com') ||
                             hostname.includes('qdmm.com') ||
                             hostname.includes('readnovel.com') ||
                             hostname.includes('xs8.cn');

        // 检查URL路径
        const pathname = window.location.pathname;
        const isReadingPage = pathname.includes('/chapter/') ||
                            pathname.includes('/read/') ||
                            pathname.includes('/book/') ||
                            pathname.includes('/novel/') ||
                            pathname.includes('/info/');

        // 检查URL参数
        const searchParams = new URLSearchParams(window.location.search);
        const hasQidianParams = searchParams.has('qd_src') ||
                              searchParams.has('qd_bid') ||
                              searchParams.has('qd_seid');

        // 检查页面元素
        const qidianSelectors = [
          '.qd_reader_header', '.qd_reader_container', '.book-info', '.read-main',
          '.qd-header', '.qd-footer', '.qd-reader', '.qd-chapter', '.qd-book',
          '.chapter-control', '.read-opt-footer', '.book-detail-info',
          '.qd_bookinfo', '.qd_readinfo', '.qd_readerBox'
        ];

        let hasQidianElements = false;
        for (const selector of qidianSelectors) {
          if (document.querySelector(selector)) {
            console.log(`检测到起点中文网元素: ${selector}`);
            hasQidianElements = true;
            break;
          }
        }

        // 检查是否有起点特有的全局变量
        const qidianGlobals = ['QD_READER', 'QidianPage', 'g_data', 'QD', 'QidianNovel', 'QDReader'];
        let hasQidianGlobals = false;
        for (const globalVar of qidianGlobals) {
          if (typeof window[globalVar] !== 'undefined') {
            console.log(`检测到起点中文网全局变量: ${globalVar}`);
            hasQidianGlobals = true;
            break;
          }
        }

        // 检查页面标题
        const pageTitle = document.title;
        const hasTitleKeywords = pageTitle.includes('起点') ||
                               pageTitle.includes('阅读') ||
                               pageTitle.includes('小说') ||
                               pageTitle.includes('书籍');

        // 检查meta标签
        const hasSiteMetaTag = !!document.querySelector('meta[name="site"][content*="qidian"]') ||
                             !!document.querySelector('meta[name="application-name"][content*="起点"]');

        // 综合判断
        const result = isQidianDomain ||
                     (isReadingPage && (hasQidianElements || hasQidianGlobals)) ||
                     (hasQidianParams && (hasQidianElements || hasTitleKeywords)) ||
                     hasSiteMetaTag;

        console.log('起点中文网检测结果:', {
          域名检测: isQidianDomain,
          路径检测: isReadingPage,
          URL参数检测: hasQidianParams,
          元素检测: hasQidianElements,
          全局变量检测: hasQidianGlobals,
          标题关键词检测: hasTitleKeywords,
          Meta标签检测: hasSiteMetaTag,
          当前URL: window.location.href,
          最终结果: result
        });

        // 如果检测到是起点中文网，添加额外的日志
        if (result) {
          console.log('确认在起点中文网环境中，将进行特殊优化');

          // 尝试获取书籍信息
          try {
            const bookInfo = getQidianBookInfo();
            if (bookInfo.bookName) {
              console.log(`当前阅读书籍: ${bookInfo.bookName}`);
            }
            if (bookInfo.chapterName) {
              console.log(`当前阅读章节: ${bookInfo.chapterName}`);
            }
          } catch (e) {
            console.error('获取书籍信息失败:', e);
          }
        }

        return result;
      } catch (error) {
        console.error('检测起点中文网环境时出错:', error);
        // 出错时保守返回false
        return false;
      }
    }

    // 获取起点中文网的书籍和章节信息
    function getQidianBookInfo() {
      try {
        console.log('尝试获取起点中文网书籍信息');

        // 初始化返回对象，包含更多字段
        const bookInfo = {
          bookName: '',          // 书籍名称
          chapterName: '',       // 章节名称
          readingProgress: '',   // 阅读进度
          author: '',           // 作者
          bookId: '',           // 书籍ID
          chapterId: '',        // 章节ID
          category: '',         // 书籍分类
          readTime: '',         // 阅读时长
          wordCount: '',        // 总字数
          updateTime: '',       // 更新时间
          vipStatus: '',        // VIP状态
          bookCover: '',        // 书籍封面
          readCount: ''         // 阅读量
        };

        console.log('开始获取书籍基本信息');

        // 尝试多种选择器获取书籍标题
        const bookTitleSelectors = [
          '.book-info-title', // 新版阅读页
          '.book-title', // 移动版
          '.page-header h1', // 某些页面
          '.book-info h1', // 经典网页版
          '.book-detail-info .book-name', // 书籍详情页
          '#bookImg', // 通过图片alt属性
          '.readPageHeader h1', // 新版阅读页头部
          '.qd_reader_title', // 起点阅读器
          '.book-name', // 通用
          '.book-info-title', // 通用
          '[data-eid="qd_G19"]', // 数据属性
          '.page-header .book', // 某些移动版
          '.book-cover-wrap h1', // 封面页
          'h3.lang', // 某些阅读页
          '#bookName' // ID选择器
        ];

        for (const selector of bookTitleSelectors) {
          try {
            const element = document.querySelector(selector);
            if (element) {
              // 如果是图片元素，获取alt属性
              if (element.tagName === 'IMG' && element.alt) {
                bookInfo.bookName = element.alt;
              } else {
                bookInfo.bookName = element.textContent.trim();
              }
              if (bookInfo.bookName) {
                console.log(`通过选择器 ${selector} 获取到书名:`, bookInfo.bookName);
                break;
              }
            }
          } catch (e) {
            console.error(`使用选择器 ${selector} 获取书名失败:`, e);
          }
        }

        // 尝试从URL获取书籍ID
        try {
          const url = window.location.href;
          const bookIdPatterns = [
            /\/book\/(\d+)/i,
            /bookId=(\d+)/i,
            /bid=(\d+)/i,
            /b=(\d+)/i
          ];

          for (const pattern of bookIdPatterns) {
            const match = url.match(pattern);
            if (match && match[1]) {
              bookInfo.bookId = match[1];
              console.log(`从URL(${pattern})获取到书籍ID:`, bookInfo.bookId);

              // 如果没有书名，设置一个默认值
              if (!bookInfo.bookName) {
                bookInfo.bookName = '当前书籍';
                console.log('未获取到书名，使用默认值:', bookInfo.bookName);
              }
              break;
            }
          }
        } catch (e) {
          console.error('从URL获取书籍ID失败:', e);
        }

        // 尝试多种选择器获取章节标题
        const chapterTitleSelectors = [
          '.j_chapterName', // 经典网页版
          '.chapter-name', // 移动版
          '.j-catalogTitle', // 目录标题
          '.j_chapterInfo', // 新版阅读页
          '.readPageHeader .info', // 新版阅读页头部
          '.qd_reader_chapter_title', // 起点阅读器
          '.read-section-name', // 其他可能的选择器
          '#chapterName', // ID选择器
          '.chapter-title', // 通用
          '[data-eid="qd_G20"]', // 数据属性
          '.page-header .chapter', // 某些移动版
          '.read-chapter-name', // 某些阅读页
          'h3.j_chapterName', // 某些阅读页
          '.chapter_header h2' // 某些阅读页
        ];

        for (const selector of chapterTitleSelectors) {
          try {
            const element = document.querySelector(selector);
            if (element) {
              bookInfo.chapterName = element.textContent.trim();
              if (bookInfo.chapterName) {
                console.log(`通过选择器 ${selector} 获取到章节名:`, bookInfo.chapterName);
                break;
              }
            }
          } catch (e) {
            console.error(`使用选择器 ${selector} 获取章节名失败:`, e);
          }
        }

        // 尝试从URL获取章节ID
        try {
          const url = window.location.href;
          const chapterIdPatterns = [
            /\/chapter\/(\d+)/i,
            /chapterId=(\d+)/i,
            /cid=(\d+)/i,
            /c=(\d+)/i
          ];

          for (const pattern of chapterIdPatterns) {
            const match = url.match(pattern);
            if (match && match[1]) {
              bookInfo.chapterId = match[1];
              console.log(`从URL(${pattern})获取到章节ID:`, bookInfo.chapterId);

              // 如果没有章节名，设置一个默认值
              if (!bookInfo.chapterName) {
                bookInfo.chapterName = '当前章节';
                console.log('未获取到章节名，使用默认值:', bookInfo.chapterName);
              }
              break;
            }
          }
        } catch (e) {
          console.error('从URL获取章节ID失败:', e);
        }

        console.log('开始获取阅读进度信息');

        // 尝试获取阅读进度
        try {
          const progressSelectors = [
            '.read-opt-progress .progress', // 阅读页底部
            '.j_readingProgress', // 进度指示器
            '.qd_reader_progress', // 起点阅读器
            '.read-progress', // 某些阅读页
            '.chapter-control .progress', // 阅读页导航
            '.progress-wrap', // 通用进度条
            '#readingProgress', // ID选择器
            '.reading-progress', // 通用
            '.j_progressBar', // 进度条
            '[data-eid="qd_G21"]', // 数据属性
            '.page-progress' // 某些移动版
          ];

          for (const selector of progressSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
              bookInfo.readingProgress = element.textContent.trim();
              console.log(`通过选择器 ${selector} 获取到阅读进度:`, bookInfo.readingProgress);
              break;
            }
          }
        } catch (e) {
          console.error('获取阅读进度失败:', e);
        }

        console.log('开始获取作者信息');

        // 尝试获取作者信息
        try {
          const authorSelectors = [
            '.book-info .writer', // 书籍详情页
            '.book-detail-info .author', // 新版书籍详情页
            '.book-author', // 通用
            '.chapter-control .author', // 阅读页导航
            '.book-cover-wrap .author', // 封面页
            '.author-info a', // 某些页面
            '.book-info-author', // 通用
            '#authorName', // ID选择器
            '.author', // 通用
            '[data-eid="qd_G22"]', // 数据属性
            '.page-header .writer' // 某些移动版
          ];

          for (const selector of authorSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
              // 使用正则表达式移除"作者："等前缀
              bookInfo.author = element.textContent.trim()
                .replace(/作者[：:]/g, '')
                .replace(/著/g, '')
                .replace(/编著/g, '')
                .replace(/著作/g, '')
                .trim();
              console.log(`通过选择器 ${selector} 获取到作者信息:`, bookInfo.author);
              break;
            }
          }
        } catch (e) {
          console.error('获取作者信息失败:', e);
        }

        console.log('开始获取书籍分类信息');

        // 尝试获取书籍分类
        try {
          const categorySelectors = [
            '.book-info .tag a', // 书籍详情页
            '.book-detail-info .tag', // 新版书籍详情页
            '.book-category', // 通用
            '.tag-box .tag:first-child', // 某些页面
            '#bookCategory', // ID选择器
            '[data-eid="qd_G23"]', // 数据属性
            '.book-info .tag span:first-child', // 书籍详情页
            '.book-detail-info .tag a:first-child' // 新版书籍详情页
          ];

          for (const selector of categorySelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
              bookInfo.category = element.textContent.trim();
              console.log(`通过选择器 ${selector} 获取到书籍分类:`, bookInfo.category);
              break;
            }
          }
        } catch (e) {
          console.error('获取书籍分类失败:', e);
        }

        console.log('开始获取阅读时长信息');

        // 尝试获取阅读时长
        try {
          const readTimeSelectors = [
            '.read-time', // 阅读页
            '.j_readTime', // 某些阅读页
            '.reading-time', // 通用
            '.j_readingTime', // 某些阅读页
            '#readingTime', // ID选择器
            '[data-eid="qd_G24"]' // 数据属性
          ];

          for (const selector of readTimeSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
              bookInfo.readTime = element.textContent.trim();
              console.log(`通过选择器 ${selector} 获取到阅读时长:`, bookInfo.readTime);
              break;
            }
          }
        } catch (e) {
          console.error('获取阅读时长失败:', e);
        }

        console.log('开始获取总字数信息');

        // 尝试获取总字数
        try {
          const wordCountSelectors = [
            '.book-info .total', // 书籍详情页
            '.book-detail-info .word-count', // 新版书籍详情页
            '.word-count', // 通用
            '.total-word', // 某些页面
            '#wordCount', // ID选择器
            '[data-eid="qd_G25"]' // 数据属性
          ];

          for (const selector of wordCountSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
              bookInfo.wordCount = element.textContent.trim();
              console.log(`通过选择器 ${selector} 获取到总字数:`, bookInfo.wordCount);
              break;
            }
          }
        } catch (e) {
          console.error('获取总字数失败:', e);
        }

        console.log('开始获取书籍封面信息');

        // 尝试获取书籍封面
        try {
          const coverSelectors = [
            '.book-img img', // 书籍详情页
            '.book-cover img', // 通用
            '#bookImg', // ID选择器
            '.book-information .book-img img', // 某些页面
            '.book-detail-cover img' // 新版书籍详情页
          ];

          for (const selector of coverSelectors) {
            const element = document.querySelector(selector);
            if (element && element.src) {
              bookInfo.bookCover = element.src;
              console.log(`通过选择器 ${selector} 获取到书籍封面:`, bookInfo.bookCover);
              break;
            }
          }
        } catch (e) {
          console.error('获取书籍封面失败:', e);
        }

        console.log('开始从全局变量获取信息');

        // 尝试从全局变量获取信息
        try {
          const globalVars = ['g_data', 'QD_READER', 'QidianPage', 'QD', 'QidianNovel', 'QDReader'];
          for (const varName of globalVars) {
            if (typeof window[varName] !== 'undefined') {
              console.log(`检查全局变量 ${varName} 中的书籍信息`);

              // 检查不同的数据结构
              const globalVar = window[varName];

              // 尝试获取书籍标题
              if (!bookInfo.bookName && globalVar) {
                if (globalVar.bookInfo && globalVar.bookInfo.bookName) {
                  bookInfo.bookName = globalVar.bookInfo.bookName;
                  console.log(`从全局变量 ${varName}.bookInfo.bookName 获取到书籍标题:`, bookInfo.bookName);
                } else if (globalVar.book && globalVar.book.title) {
                  bookInfo.bookName = globalVar.book.title;
                  console.log(`从全局变量 ${varName}.book.title 获取到书籍标题:`, bookInfo.bookName);
                } else if (globalVar.title) {
                  bookInfo.bookName = globalVar.title;
                  console.log(`从全局变量 ${varName}.title 获取到书籍标题:`, bookInfo.bookName);
                }
              }

              // 尝试获取章节标题
              if (!bookInfo.chapterName && globalVar) {
                if (globalVar.chapter && globalVar.chapter.chapterName) {
                  bookInfo.chapterName = globalVar.chapter.chapterName;
                  console.log(`从全局变量 ${varName}.chapter.chapterName 获取到章节标题:`, bookInfo.chapterName);
                } else if (globalVar.chapter && globalVar.chapter.title) {
                  bookInfo.chapterName = globalVar.chapter.title;
                  console.log(`从全局变量 ${varName}.chapter.title 获取到章节标题:`, bookInfo.chapterName);
                }
              }

              // 尝试获取书籍ID
              if (!bookInfo.bookId && globalVar) {
                if (globalVar.bookInfo && globalVar.bookInfo.bookId) {
                  bookInfo.bookId = globalVar.bookInfo.bookId;
                  console.log(`从全局变量 ${varName}.bookInfo.bookId 获取到书籍ID:`, bookInfo.bookId);
                } else if (globalVar.book && globalVar.book.id) {
                  bookInfo.bookId = globalVar.book.id;
                  console.log(`从全局变量 ${varName}.book.id 获取到书籍ID:`, bookInfo.bookId);
                } else if (globalVar.bookId) {
                  bookInfo.bookId = globalVar.bookId;
                  console.log(`从全局变量 ${varName}.bookId 获取到书籍ID:`, bookInfo.bookId);
                }
              }

              // 尝试获取章节ID
              if (!bookInfo.chapterId && globalVar) {
                if (globalVar.chapter && globalVar.chapter.chapterId) {
                  bookInfo.chapterId = globalVar.chapter.chapterId;
                  console.log(`从全局变量 ${varName}.chapter.chapterId 获取到章节ID:`, bookInfo.chapterId);
                } else if (globalVar.chapter && globalVar.chapter.id) {
                  bookInfo.chapterId = globalVar.chapter.id;
                  console.log(`从全局变量 ${varName}.chapter.id 获取到章节ID:`, bookInfo.chapterId);
                } else if (globalVar.chapterId) {
                  bookInfo.chapterId = globalVar.chapterId;
                  console.log(`从全局变量 ${varName}.chapterId 获取到章节ID:`, bookInfo.chapterId);
                }
              }
            }
          }
        } catch (e) {
          console.error('从全局变量获取信息失败:', e);
        }

        console.log('开始从meta标签获取信息');

        // 尝试从meta标签获取信息
        try {
          // 获取书籍ID
          if (!bookInfo.bookId) {
            const metaBookId = document.querySelector('meta[property="og:novel:book_id"]') ||
                            document.querySelector('meta[name="bookId"]');
            if (metaBookId && metaBookId.getAttribute('content')) {
              bookInfo.bookId = metaBookId.getAttribute('content');
              console.log('从meta标签获取到书籍ID:', bookInfo.bookId);
            }
          }

          // 获取章节ID
          if (!bookInfo.chapterId) {
            const metaChapterId = document.querySelector('meta[property="og:novel:chapter_id"]') ||
                               document.querySelector('meta[name="chapterId"]');
            if (metaChapterId && metaChapterId.getAttribute('content')) {
              bookInfo.chapterId = metaChapterId.getAttribute('content');
              console.log('从meta标签获取到章节ID:', bookInfo.chapterId);
            }
          }

          // 获取书籍分类
          if (!bookInfo.category) {
            const metaCategory = document.querySelector('meta[property="og:novel:category"]') ||
                              document.querySelector('meta[name="category"]');
            if (metaCategory && metaCategory.getAttribute('content')) {
              bookInfo.category = metaCategory.getAttribute('content');
              console.log('从meta标签获取到书籍分类:', bookInfo.category);
            }
          }

          // 获取作者
          if (!bookInfo.author) {
            const metaAuthor = document.querySelector('meta[property="og:novel:author"]') ||
                            document.querySelector('meta[name="author"]');
            if (metaAuthor && metaAuthor.getAttribute('content')) {
              bookInfo.author = metaAuthor.getAttribute('content');
              console.log('从meta标签获取到作者:', bookInfo.author);
            }
          }

          // 获取书籍名称
          if (!bookInfo.bookName) {
            const metaBookName = document.querySelector('meta[property="og:novel:book_name"]') ||
                              document.querySelector('meta[property="og:title"]') ||
                              document.querySelector('meta[name="title"]');
            if (metaBookName && metaBookName.getAttribute('content')) {
              bookInfo.bookName = metaBookName.getAttribute('content');
              console.log('从meta标签获取到书籍名称:', bookInfo.bookName);
            }
          }
        } catch (e) {
          console.error('从meta标签获取信息失败:', e);
        }

        console.log('最终获取到的起点中文网书籍信息:', bookInfo);
        return bookInfo;
      } catch (e) {
        console.error('获取起点中文网书籍信息失败:', e);
        return {};
      }
    }

        console.log('最终获取到的起点中文网书籍信息:', bookInfo);
        return bookInfo;
      } catch (e) {
        console.error('获取起点中文网书籍信息失败:', e);
        return {};
      }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('Live2D 页面加载完成，版本: 1.3.0');

      try {
        // 检查是否在起点中文网
        const inQidian = isInQidian();
        console.log('是否在起点中文网:', inQidian);
        console.log('当前页面URL:', window.location.href);
        console.log('当前页面域名:', window.location.hostname);

        // 如果在起点中文网，进行特殊处理
        if (inQidian) {
          console.log('检测到起点中文网环境，进行特殊优化');

          // 设置特定的样式
          const styleElement = document.createElement('style');
          styleElement.textContent = `
            #live2d-container {
              z-index: 999999 !important;
              pointer-events: auto !important;
              transition: opacity 0.3s ease-in-out !important;
            }
            #live2d-message {
              z-index: 9999999 !important;
              font-family: 'Microsoft YaHei', '微软雅黑', sans-serif !important;
              background-color: rgba(255, 255, 255, 0.9) !important;
              color: #333 !important;
              border: 1px solid #ccc !important;
              box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2) !important;
              border-radius: 8px !important;
              padding: 8px 15px !important;
              font-size: 14px !important;
              max-width: 300px !important;
              word-break: break-word !important;
              pointer-events: none !important;
              transition: opacity 0.3s ease-in-out !important;
            }
            #live2d-tools {
              z-index: 999999 !important;
              transition: opacity 0.3s ease-in-out !important;
            }
            .live2d-qidian-button {
              background-color: rgba(255, 255, 255, 0.8) !important;
              border: 1px solid #ddd !important;
              border-radius: 4px !important;
              color: #333 !important;
              padding: 5px 10px !important;
              margin: 5px !important;
              cursor: pointer !important;
              font-size: 12px !important;
              transition: all 0.2s ease !important;
            }
            .live2d-qidian-button:hover {
              background-color: rgba(240, 240, 240, 0.9) !important;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
            }
          `;
          document.head.appendChild(styleElement);
          console.log('已添加起点中文网专用样式');

          // 尝试获取书籍信息
          try {
            const bookInfo = getQidianBookInfo();
            console.log('获取到书籍信息:', bookInfo);

            // 保存书籍信息到全局配置
            LIVE2D_CONFIG.qidianBookInfo = bookInfo;

            // 更新配置
            if (bookInfo.bookName) {
              LIVE2D_CONFIG.bookName = bookInfo.bookName;
              let welcomeMsg = `欢迎阅读《${bookInfo.bookName}》！`;

              // 根据书籍分类选择合适的模型
              if (bookInfo.category) {
                console.log('根据书籍分类选择模型:', bookInfo.category);
                // 根据分类设置模型偏好
                if (/玄幻|奇幻|仙侠|修真|异世界/.test(bookInfo.category)) {
                  LIVE2D_CONFIG.modelPreference = ['shizuku', 'miku', 'epsilon'];
                  welcomeMsg += ` 仙侠世界的奇妙冒险开始啦！`;
                } else if (/科幻|未来|机甲/.test(bookInfo.category)) {
                  LIVE2D_CONFIG.modelPreference = ['epsilon', 'haru', 'koharu'];
                  welcomeMsg += ` 科技与未来的奇妙旅程！`;
                } else if (/校园|青春|恋爱|言情/.test(bookInfo.category)) {
                  LIVE2D_CONFIG.modelPreference = ['haruto', 'koharu', 'hijiki'];
                  welcomeMsg += ` 青春的故事总是令人心动～`;
                } else if (/悬疑|推理|恐怖/.test(bookInfo.category)) {
                  LIVE2D_CONFIG.modelPreference = ['tororo', 'wanko', 'izumi'];
                  welcomeMsg += ` 一起解开谜团吧！`;
                } else if (/历史|军事/.test(bookInfo.category)) {
                  LIVE2D_CONFIG.modelPreference = ['chitose', 'tororo', 'hibiki'];
                  welcomeMsg += ` 历史的长河中总有精彩故事！`;
                } else if (/都市|职场|商战/.test(bookInfo.category)) {
                  LIVE2D_CONFIG.modelPreference = ['hijiki', 'tororo', 'haruto'];
                  welcomeMsg += ` 都市生活的精彩纷呈！`;
                }
              }

              LIVE2D_CONFIG.messages.welcome = welcomeMsg;
            }

            if (bookInfo.chapterName) {
              LIVE2D_CONFIG.chapterName = bookInfo.chapterName;
              LIVE2D_CONFIG.messages.welcome += ` 当前章节: ${bookInfo.chapterName}`;
            }

            if (bookInfo.readingProgress) {
              LIVE2D_CONFIG.readingProgress = bookInfo.readingProgress;
              LIVE2D_CONFIG.messages.welcome += ` 阅读进度: ${bookInfo.readingProgress}`;
            }

            // 添加与阅读相关的消息
            LIVE2D_CONFIG.messages.idle = LIVE2D_CONFIG.messages.idle || [];

            // 根据书籍信息添加个性化消息
            const customMessages = [];

            // 基础阅读消息
            customMessages.push(
              '专心阅读，不要分心哦~',
              '这本书真好看，我也想知道后面的剧情！',
              '需要我帮你记住阅读进度吗？',
              '阅读使人明智，我会陪你一起变得更聪明！',
              '如果觉得累了，可以休息一下眼睛哦~',
              '这个情节真是太精彩了！',
              '作者的文笔真不错呢！'
            );

            // 如果有作者信息，添加相关消息
            if (bookInfo.author) {
              customMessages.push(
                `${bookInfo.author}的作品总是这么精彩！`,
                `我很喜欢${bookInfo.author}的写作风格！`,
                `${bookInfo.author}真是个有才华的作家！`
              );
            }

            // 如果有书名，添加相关消息
            if (bookInfo.bookName) {
              customMessages.push(
                `《${bookInfo.bookName}》这本书的剧情真是引人入胜！`,
                `我也在追《${bookInfo.bookName}》，超级好看！`,
                `《${bookInfo.bookName}》的世界观设定很精彩呢！`
              );
            }

            // 如果有章节名，添加相关消息
            if (bookInfo.chapterName) {
              customMessages.push(
                `「${bookInfo.chapterName}」这一章节真是扣人心弦！`,
                `我很期待「${bookInfo.chapterName}」后面的发展！`
              );
            }

            // 如果有阅读进度，添加相关消息
            if (bookInfo.readingProgress) {
              customMessages.push(
                `你已经读到${bookInfo.readingProgress}了，继续加油！`,
                `${bookInfo.readingProgress}的剧情真是精彩！`
              );
            }

            // 如果有总字数，添加相关消息
            if (bookInfo.totalWords) {
              customMessages.push(
                `这本书总共有${bookInfo.totalWords}，内容很丰富呢！`,
                `${bookInfo.totalWords}的长篇巨作，值得细细品味！`
              );
            }

            // 将自定义消息添加到配置中
            LIVE2D_CONFIG.messages.idle.push(...customMessages);
            console.log('已更新Live2D配置，添加了阅读相关消息');

            // 向父窗口发送书籍信息
            try {
              window.parent.postMessage({
                type: 'live2d-book-info',
                bookInfo: LIVE2D_CONFIG.qidianBookInfo
              }, '*');
              console.log('已向父窗口发送书籍信息');
            } catch (e) {
              console.error('向父窗口发送书籍信息失败:', e);
            }
          } catch (error) {
            console.error('获取书籍信息失败:', error);
          }

          // 监听起点中文网特有的事件
          try {
            // 上次章节信息，用于检测变化
            let lastChapterId = LIVE2D_CONFIG.qidianBookInfo?.chapterId || '';
            let lastChapterName = LIVE2D_CONFIG.qidianBookInfo?.chapterName || '';

            // 监听章节切换 - 通过URL变化
            window.addEventListener('hashchange', () => {
              console.log('检测到URL哈希变化，可能是章节切换');
              setTimeout(checkChapterChange, 1000);
            });

            // 监听章节切换 - 通过历史状态变化
            window.addEventListener('popstate', () => {
              console.log('检测到历史状态变化，可能是章节切换');
              setTimeout(checkChapterChange, 1000);
            });

            // 定期检查章节变化（某些情况下URL变化但不触发上述事件）
            const chapterCheckInterval = setInterval(checkChapterChange, 10000);

            // 章节变化检查函数
            function checkChapterChange() {
              try {
                const currentInfo = getQidianBookInfo();
                const currentChapterId = currentInfo.chapterId || '';
                const currentChapterName = currentInfo.chapterName || '';

                // 检查章节是否变化
                if ((currentChapterId && currentChapterId !== lastChapterId) ||
                    (currentChapterName && currentChapterName !== lastChapterName)) {
                  console.log('检测到章节变化:', {
                    from: { id: lastChapterId, name: lastChapterName },
                    to: { id: currentChapterId, name: currentChapterName }
                  });

                  // 更新上次章节信息
                  lastChapterId = currentChapterId;
                  lastChapterName = currentChapterName;

                  // 更新全局配置
                  LIVE2D_CONFIG.qidianBookInfo = currentInfo;
                  LIVE2D_CONFIG.chapterName = currentChapterName;

                  // 显示章节变化消息
                  if (currentChapterName) {
                    showMessage(`正在阅读: ${currentChapterName}`, 3000);
                  }

                  // 向父窗口发送章节变化消息
                  try {
                    window.parent.postMessage({
                      type: 'live2d-chapter-changed',
                      chapterId: currentChapterId,
                      chapterName: currentChapterName,
                      bookInfo: currentInfo
                    }, '*');
                    console.log('已向父窗口发送章节变化消息');
                  } catch (e) {
                    console.error('向父窗口发送章节变化消息失败:', e);
                  }
                }
              } catch (e) {
                console.error('检查章节变化失败:', e);
              }
            }

            // 监听滚动事件，但限制频率
            let scrollTimeout;
            let lastScrollTime = 0;
            let lastScrollPosition = window.scrollY;

            window.addEventListener('scroll', () => {
              const now = Date.now();
              const currentPosition = window.scrollY;

              // 计算滚动速度和方向
              const timeDiff = now - lastScrollTime;
              const scrollDiff = currentPosition - lastScrollPosition;

              // 更新上次滚动时间和位置
              lastScrollTime = now;
              lastScrollPosition = currentPosition;

              // 清除之前的超时
              clearTimeout(scrollTimeout);

              // 设置新的超时 - 滚动停止后触发
              scrollTimeout = setTimeout(() => {
                // 只在有较大滚动后停止时触发，且限制频率
                if (Math.abs(scrollDiff) > 100 && Math.random() < 0.2) {
                  const direction = scrollDiff > 0 ? '下' : '上';
                  const messages = [
                    `看来你${direction}滑了一大段，找到有趣的内容了吗？`,
                    '慢慢看，不要着急~',
                    '这段情节很精彩呢！',
                    '认真阅读中...',
                    '我也在跟着你一起看呢！',
                    '这一段写得真好！',
                    '喜欢这个情节吗？'
                  ];
                  showMessage(messages[Math.floor(Math.random() * messages.length)], 2000);

                  // 尝试获取并显示当前阅读进度
                  try {
                    const currentInfo = getQidianBookInfo();
                    if (currentInfo.readingProgress && currentInfo.readingProgress !== LIVE2D_CONFIG.readingProgress) {
                      LIVE2D_CONFIG.readingProgress = currentInfo.readingProgress;
                      setTimeout(() => {
                        showMessage(`当前阅读进度: ${currentInfo.readingProgress}`, 2000);
                      }, 2500);
                    }
                  } catch (e) {
                    console.error('获取阅读进度失败:', e);
                  }
                }
              }, 1000);
            });

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', () => {
              if (document.visibilityState === 'visible') {
                // 页面变为可见时
                console.log('页面变为可见');
                const messages = [
                  '欢迎回来继续阅读！',
                  '我一直在等你回来呢~',
                  '继续我们的阅读吧！',
                  '休息得如何？准备继续阅读了吗？'
                ];
                showMessage(messages[Math.floor(Math.random() * messages.length)], 3000);

                // 检查章节是否变化
                setTimeout(checkChapterChange, 1000);
              } else {
                // 页面变为不可见时
                console.log('页面变为不可见');
                // 可以在这里保存一些状态或发送消息
              }
            });

            console.log('已添加起点中文网特有事件监听');
          } catch (e) {
            console.error('添加起点中文网事件监听失败:', e);
          }
        }

        // 先从API获取配置
        const configLoaded = await fetchLive2DConfig();
        console.log('配置加载状态:', configLoaded ? '成功' : '失败');

        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const modelId = urlParams.get('modelId');
        console.log('URL参数中的模型ID:', modelId);

        // 如果URL中指定了模型ID，则使用该模型
        if (modelId) {
          const modelConfig = LIVE2D_CONFIG.models.find(m => m.id === modelId);
          if (modelConfig) {
            currentModelIndex = LIVE2D_CONFIG.models.findIndex(m => m.id === modelId);
            console.log(`从URL参数加载模型: ${modelConfig.name}`);
          } else {
            console.log(`未找到ID为${modelId}的模型配置`);
          }
        }

        // 初始化PIXI应用
        initPixiApp();
        console.log('PIXI应用初始化成功');
      } catch (error) {
        console.error('初始化Live2D失败:', error);

        // 显示友好的错误消息
        const errorMessage = isInQidian() ?
          '小助手加载失败，但不影响您继续阅读哦~' :
          '加载失败，请刷新页面重试';

        showMessage(errorMessage, 5000);

        // 创建一个简单的错误提示元素
        const errorElement = document.createElement('div');
        errorElement.style.cssText = 'position:fixed;bottom:10px;right:10px;background:rgba(255,255,255,0.9);padding:10px;border-radius:5px;font-size:12px;z-index:999999;';
        errorElement.innerHTML = `<p>Live2D加载失败</p><button id="retry-live2d" style="padding:5px;margin-top:5px;">重试</button>`;
        document.body.appendChild(errorElement);

        // 添加重试按钮事件
        document.getElementById('retry-live2d')?.addEventListener('click', () => {
          location.reload();
        });
      }
    });
  </script>
</body>
</html>
