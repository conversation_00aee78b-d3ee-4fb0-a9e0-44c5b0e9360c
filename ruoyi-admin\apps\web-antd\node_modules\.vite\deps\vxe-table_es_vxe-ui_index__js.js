import {
  VXETable,
  _t,
  clipboard,
  commands,
  config,
  formats,
  getConfig,
  getI18n,
  getIcon,
  getTheme,
  globalEvents,
  globalResize,
  hooks,
  interceptor,
  log,
  menus,
  modal,
  print,
  readFile,
  renderer,
  saveFile,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  t,
  ui_default,
  use,
  validators,
  version
} from "./chunk-EKP7TM2V.js";
import {
  VxeUI
} from "./chunk-BCXSQRR2.js";
import "./chunk-PJ5U4TG7.js";
import "./chunk-GE6DY3YU.js";
import "./chunk-KT3WABTJ.js";
import "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  VXETable,
  VxeUI,
  _t,
  clipboard,
  commands,
  config,
  vxe_ui_default as default,
  formats,
  getConfig,
  getI18n,
  getIcon,
  getTheme,
  globalEvents,
  globalResize,
  hooks,
  interceptor,
  log,
  menus,
  modal,
  print,
  readFile,
  renderer,
  saveFile,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  t,
  use,
  validators,
  version
};
//# sourceMappingURL=vxe-table_es_vxe-ui_index__js.js.map
