import type { PageQuery, BaseEntity } from '#/api/common';

export interface NewsVO {
  /**
   * id（后端随机生成）
   */
  id: string | number;

  /**
   * 消息发起人id
   */
  otherId: string | number;

  /**
   * 消息接收者id
   */
  MyId: string | number;

  /**
   * 消息内容
   */
  news: string;

  /**
   * 消息类型0代表管理员信息，1代表论坛信息，2代表私聊信息，3代表点赞消息，4代表关注消息
   */
  type: string | number;

  /**
   * isread=0未未读，isread=1为已读。
   */
  isread: number;

}

export interface NewsForm extends BaseEntity {
  /**
   * id（后端随机生成）
   */
  id?: string | number;

  /**
   * 消息发起人id
   */
  otherId?: string | number;

  /**
   * 消息接收者id
   */
  MyId?: string | number;

  /**
   * 消息内容
   */
  news?: string;

  /**
   * 消息类型0代表管理员信息，1代表论坛信息，2代表私聊信息，3代表点赞消息，4代表关注消息
   */
  type?: string | number;

  /**
   * isread=0未未读，isread=1为已读。
   */
  isread?: number;

}

export interface NewsQuery extends PageQuery {
  /**
   * id（后端随机生成）
   */
  id?: string | number;

  /**
   * 消息发起人id
   */
  otherId?: string | number;

  /**
   * 消息接收者id
   */
  MyId?: string | number;

  /**
   * 消息内容
   */
  news?: string;

  /**
   * 消息类型0代表管理员信息，1代表论坛信息，2代表私聊信息，3代表点赞消息，4代表关注消息
   */
  type?: string | number;

  /**
   * isread=0未未读，isread=1为已读。
   */
  isread?: number;

  /**
    * 日期范围参数
    */
  params?: any;
}
