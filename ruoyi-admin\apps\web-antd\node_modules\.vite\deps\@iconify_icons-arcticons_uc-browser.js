import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@iconify+icons-arcticons@1.2.77/node_modules/@iconify/icons-arcticons/uc-browser.js
var require_uc_browser = __commonJS({
  "../../node_modules/.pnpm/@iconify+icons-arcticons@1.2.77/node_modules/@iconify/icons-arcticons/uc-browser.js"(exports) {
    var data = {
      "width": 48,
      "height": 48,
      "body": '<circle cx="19.584" cy="35.252" r="7.248" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M29.384 38.58s5.985-1.098 5.985 3.92H16.524c-6.184 0-11.197-5.013-11.197-11.197c0-2.9 1.1-5.548 2.914-7.533c3.51-3.84 5.1-5.194 5.1-8.375s-3.953-5.124-8.014-2.65C8.806 6.678 11.574 5.5 17.228 5.5s8.305 4.712 8.305 8.835c0 8.187-13.197 8.893-13.197 20.917"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M29.384 38.58a9.131 9.131 0 0 0-15.58-9.521"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M18.141 25.52c5.272-3.705 12.575-2.233 17.228 2.185c5.36-.943 7.304 2.356 7.304 2.356c-2.28-.178-5.034.656-6.854 1.343a3.04 3.04 0 0 1-3.336-.79c-3.593-3.961-9.133-7.585-14.342-5.093Z"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M22.98 23.573s2.789-2.863 8.973-6.176c-.236-3.003-.088-4.13 1.414-5.065c2.65.67 3.18 3.829 3.18 3.829c4.889 1.649 6.832 8.304 5.007 9.247s-7.741.256-11.353-.98"/><circle cx="19.584" cy="35.252" r="2.626" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>'
    };
    exports.__esModule = true;
    exports.default = data;
  }
});
export default require_uc_browser();
//# sourceMappingURL=@iconify_icons-arcticons_uc-browser.js.map
