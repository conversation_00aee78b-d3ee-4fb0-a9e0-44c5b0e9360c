import {
  dynamicApp
} from "./chunk-URMRKTZM.js";
import {
  VxeUI,
  createEvent,
  getConfig,
  require_xe_utils,
  useSize
} from "./chunk-BCXSQRR2.js";
import "./chunk-PJ5U4TG7.js";
import {
  defineComponent,
  h
} from "./chunk-GE6DY3YU.js";
import "./chunk-KT3WABTJ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/icon/src/icon.js
var import_xe_utils = __toESM(require_xe_utils());
var icon_default = defineComponent({
  name: "VxeIcon",
  props: {
    name: String,
    className: String,
    roll: Boolean,
    status: String,
    size: {
      type: String,
      default: () => getConfig().icon.size || getConfig().size
    }
  },
  emits: [
    "click"
  ],
  setup(props, context) {
    const { emit } = context;
    const xID = import_xe_utils.default.uniqueId();
    const { computeSize } = useSize(props);
    const $xeIcon = {
      xID,
      props,
      context
    };
    const clickEvent = (evnt) => {
      emit("click", createEvent(evnt, {}));
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $icon: $xeIcon }, params));
    };
    const iconMethods = {
      dispatchEvent
    };
    const iconPrivateMethods = {};
    Object.assign($xeIcon, iconMethods, iconPrivateMethods);
    const renderVN = () => {
      const { name, roll, status, className } = props;
      const vSize = computeSize.value;
      return h("i", {
        class: ["vxe-icon", `vxe-icon-${name}`, `${className || ""}`, {
          [`size--${vSize}`]: vSize,
          [`theme--${status}`]: status,
          roll
        }],
        onClick: clickEvent
      });
    };
    $xeIcon.renderVN = renderVN;
    return $xeIcon;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/icon/index.js
var VxeIcon = Object.assign({}, icon_default, {
  install(app) {
    app.component(icon_default.name, icon_default);
  }
});
dynamicApp.use(VxeIcon);
VxeUI.component(icon_default);
var Icon = VxeIcon;
var icon_default2 = VxeIcon;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/vxe-icon/index.js
var vxe_icon_default = icon_default2;
export {
  Icon,
  VxeIcon,
  vxe_icon_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-icon_index__js.js.map
