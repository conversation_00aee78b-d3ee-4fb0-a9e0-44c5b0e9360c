import type { PageQuery, BaseEntity } from '#/api/common';

export interface DirectoryVO {
  /**
   * 书本id
   */
  id: string | number;

  /**
   *  第x章
   */
  chapter: string;

  /**
   * 章节链接
   */
  link: string;

  /**
   *  章节名称
   */
  chaptername: string;

}

export interface DirectoryForm extends BaseEntity {
  /**
   *  书本id
   */
  id?: string | number;

  /**
   *  第x章
   */
  chapter?: string;

  /**
   *  章节链接
   */
  link?: string;

  /**
   *  章节名
   */
  chaptername?: string;

}

export interface DirectoryQuery extends PageQuery {
   /**
   * 书本id
   */
  id?: string | number;

  /**
   *  第x章
   */
  chapter?: string;

  /**
   *  章节链接
   */
  link?: string;

  /**
   *  章节名称
   */
  chaptername?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
