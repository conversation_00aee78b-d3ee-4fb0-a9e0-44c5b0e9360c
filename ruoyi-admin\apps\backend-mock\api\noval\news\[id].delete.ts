import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { deleteNewsMultiple } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const idsParam = getRouterParam(event, 'id');

  // 支持单个ID和多个ID（逗号分隔）
  const ids = String(idsParam).split(',').map(id => Number(id.trim()));

  // 删除指定的消息
  const deletedCount = deleteNewsMultiple(ids);

  return useResponseSuccess(null, `删除成功，共删除${deletedCount}条消息`);
});
