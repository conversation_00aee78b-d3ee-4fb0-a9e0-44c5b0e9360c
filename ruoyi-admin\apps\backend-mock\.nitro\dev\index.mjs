import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import { tmpdir } from 'node:os';
import { Server } from 'node:http';
import { resolve, dirname, join } from 'node:path';
import nodeCrypto from 'node:crypto';
import { parentPort, threadId } from 'node:worker_threads';
import { defineEventHandler, handleCacheHeaders, splitCookiesString, createEvent, fetchWithEvent, isEvent, eventHandler, setHeaders, sendRedirect, proxyRequest, getRequestURL, getRequestHeader, getResponseHeader, getRequestHeaders, setResponseHeaders, setResponseStatus, send, createApp, createRouter as createRouter$1, toNode<PERSON><PERSON><PERSON>, lazyEventHandler, createError, getRouterParam, readBody, getQuery as getQuery$1, getHeader, deleteCookie, setCookie, getCookie, readMultipartFormData } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/h3@1.15.1/node_modules/h3/dist/index.mjs';
import destr from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/destr@2.0.3/node_modules/destr/dist/index.mjs';
import { createHooks } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/hookable@5.5.3/node_modules/hookable/dist/index.mjs';
import { createFetch, Headers as Headers$1 } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch/dist/node.mjs';
import { fetchNodeRequestHandler, callNodeRequestHandler } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/node-mock-http@1.0.0/node_modules/node-mock-http/dist/index.mjs';
import { parseURL, withoutBase, joinURL, getQuery, withQuery } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/ufo@1.5.4/node_modules/ufo/dist/index.mjs';
import { createStorage, prefixStorage } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/unstorage@1.15.0_db0@0.3.1_idb-keyval@6.2.2_ioredis@5.6.0/node_modules/unstorage/dist/index.mjs';
import unstorage_47drivers_47fs from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/unstorage@1.15.0_db0@0.3.1_idb-keyval@6.2.2_ioredis@5.6.0/node_modules/unstorage/drivers/fs.mjs';
import { digest } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/ohash@2.0.11/node_modules/ohash/dist/index.mjs';
import { klona } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/klona@2.0.6/node_modules/klona/dist/index.mjs';
import defu, { defuFn } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/defu@6.1.4/node_modules/defu/dist/defu.mjs';
import { snakeCase } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/scule@1.3.0/node_modules/scule/dist/index.mjs';
import { toRouteMatcher, createRouter } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/radix3@1.1.2/node_modules/radix3/dist/index.mjs';
import { readFile } from 'node:fs/promises';
import consola from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/consola@3.4.2/node_modules/consola/dist/index.mjs';
import { ErrorParser } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/youch-core@0.3.2/node_modules/youch-core/build/index.js';
import { Youch } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/youch@4.1.0-beta.6/node_modules/youch/build/index.js';
import { SourceMapConsumer } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/source-map@0.7.4/node_modules/source-map/source-map.js';
import axios from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/axios@1.8.4/node_modules/axios/index.js';
import { faker } from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/@faker-js+faker@9.6.0/node_modules/@faker-js/faker/dist/index.js';
import jwt from 'file://D:/daima/new/ruoyi-admin/node_modules/.pnpm/jsonwebtoken@9.0.2/node_modules/jsonwebtoken/index.js';

const serverAssets = [{"baseName":"server","dir":"D:/daima/new/ruoyi-admin/apps/backend-mock/assets"}];

const assets = createStorage();

for (const asset of serverAssets) {
  assets.mount(asset.baseName, unstorage_47drivers_47fs({ base: asset.dir, ignore: (asset?.ignore || []) }));
}

const storage = createStorage({});

storage.mount('/assets', assets);

storage.mount('root', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"D:/daima/new/ruoyi-admin/apps/backend-mock"}));
storage.mount('src', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"D:/daima/new/ruoyi-admin/apps/backend-mock"}));
storage.mount('build', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"D:/daima/new/ruoyi-admin/apps/backend-mock/.nitro"}));
storage.mount('cache', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"D:/daima/new/ruoyi-admin/apps/backend-mock/.nitro/cache"}));
storage.mount('data', unstorage_47drivers_47fs({"driver":"fs","base":"D:/daima/new/ruoyi-admin/apps/backend-mock/.data/kv"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage, base) : storage;
}

const Hasher = /* @__PURE__ */ (() => {
  class Hasher2 {
    buff = "";
    #context = /* @__PURE__ */ new Map();
    write(str) {
      this.buff += str;
    }
    dispatch(value) {
      const type = value === null ? "null" : typeof value;
      return this[type](value);
    }
    object(object) {
      if (object && typeof object.toJSON === "function") {
        return this.object(object.toJSON());
      }
      const objString = Object.prototype.toString.call(object);
      let objType = "";
      const objectLength = objString.length;
      objType = objectLength < 10 ? "unknown:[" + objString + "]" : objString.slice(8, objectLength - 1);
      objType = objType.toLowerCase();
      let objectNumber = null;
      if ((objectNumber = this.#context.get(object)) === void 0) {
        this.#context.set(object, this.#context.size);
      } else {
        return this.dispatch("[CIRCULAR:" + objectNumber + "]");
      }
      if (typeof Buffer !== "undefined" && Buffer.isBuffer && Buffer.isBuffer(object)) {
        this.write("buffer:");
        return this.write(object.toString("utf8"));
      }
      if (objType !== "object" && objType !== "function" && objType !== "asyncfunction") {
        if (this[objType]) {
          this[objType](object);
        } else {
          this.unknown(object, objType);
        }
      } else {
        const keys = Object.keys(object).sort();
        const extraKeys = [];
        this.write("object:" + (keys.length + extraKeys.length) + ":");
        const dispatchForKey = (key) => {
          this.dispatch(key);
          this.write(":");
          this.dispatch(object[key]);
          this.write(",");
        };
        for (const key of keys) {
          dispatchForKey(key);
        }
        for (const key of extraKeys) {
          dispatchForKey(key);
        }
      }
    }
    array(arr, unordered) {
      unordered = unordered === void 0 ? false : unordered;
      this.write("array:" + arr.length + ":");
      if (!unordered || arr.length <= 1) {
        for (const entry of arr) {
          this.dispatch(entry);
        }
        return;
      }
      const contextAdditions = /* @__PURE__ */ new Map();
      const entries = arr.map((entry) => {
        const hasher = new Hasher2();
        hasher.dispatch(entry);
        for (const [key, value] of hasher.#context) {
          contextAdditions.set(key, value);
        }
        return hasher.toString();
      });
      this.#context = contextAdditions;
      entries.sort();
      return this.array(entries, false);
    }
    date(date) {
      return this.write("date:" + date.toJSON());
    }
    symbol(sym) {
      return this.write("symbol:" + sym.toString());
    }
    unknown(value, type) {
      this.write(type);
      if (!value) {
        return;
      }
      this.write(":");
      if (value && typeof value.entries === "function") {
        return this.array(
          [...value.entries()],
          true
          /* ordered */
        );
      }
    }
    error(err) {
      return this.write("error:" + err.toString());
    }
    boolean(bool) {
      return this.write("bool:" + bool);
    }
    string(string) {
      this.write("string:" + string.length + ":");
      this.write(string);
    }
    function(fn) {
      this.write("fn:");
      if (isNativeFunction(fn)) {
        this.dispatch("[native]");
      } else {
        this.dispatch(fn.toString());
      }
    }
    number(number) {
      return this.write("number:" + number);
    }
    null() {
      return this.write("Null");
    }
    undefined() {
      return this.write("Undefined");
    }
    regexp(regex) {
      return this.write("regex:" + regex.toString());
    }
    arraybuffer(arr) {
      this.write("arraybuffer:");
      return this.dispatch(new Uint8Array(arr));
    }
    url(url) {
      return this.write("url:" + url.toString());
    }
    map(map) {
      this.write("map:");
      const arr = [...map];
      return this.array(arr, false);
    }
    set(set) {
      this.write("set:");
      const arr = [...set];
      return this.array(arr, false);
    }
    bigint(number) {
      return this.write("bigint:" + number.toString());
    }
  }
  for (const type of [
    "uint8array",
    "uint8clampedarray",
    "unt8array",
    "uint16array",
    "unt16array",
    "uint32array",
    "unt32array",
    "float32array",
    "float64array"
  ]) {
    Hasher2.prototype[type] = function(arr) {
      this.write(type + ":");
      return this.array([...arr], false);
    };
  }
  function isNativeFunction(f) {
    if (typeof f !== "function") {
      return false;
    }
    return Function.prototype.toString.call(f).slice(
      -15
      /* "[native code] }".length */
    ) === "[native code] }";
  }
  return Hasher2;
})();
function serialize(object) {
  const hasher = new Hasher();
  hasher.dispatch(object);
  return hasher.buff;
}
function hash(value) {
  return digest(typeof value === "string" ? value : serialize(value)).replace(/[-_]/g, "").slice(0, 10);
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.waitUntil = incomingEvent.waitUntil;
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

const inlineAppConfig = {};



const appConfig = defuFn(inlineAppConfig);

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /\{\{([^{}]*)\}\}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/"
  },
  "nitro": {
    "routeRules": {
      "/api/**": {
        "cors": true,
        "headers": {
          "access-control-allow-origin": "*",
          "access-control-allow-methods": "*",
          "access-control-allow-headers": "*",
          "access-control-max-age": "0",
          "Access-Control-Allow-Credentials": "true",
          "Access-Control-Allow-Headers": "Accept, Authorization, Content-Length, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With",
          "Access-Control-Allow-Methods": "GET,HEAD,PUT,PATCH,POST,DELETE",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Expose-Headers": "*"
        }
      }
    }
  }
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  {
    return _sharedRuntimeConfig;
  }
}
_deepFreeze(klona(appConfig));
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

function _captureError(error, type) {
  console.error(`[${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

const errorHandler$2 = function(error, event) {
  event.node.res.end(`[Error Handler] ${error.stack}`);
};

function defineNitroErrorHandler(handler) {
  return handler;
}

const errorHandler$1 = defineNitroErrorHandler(
  async function defaultNitroErrorHandler(error, event) {
    const res = await defaultHandler(error, event);
    if (!event.node?.res.headersSent) {
      setResponseHeaders(event, res.headers);
    }
    setResponseStatus(event, res.status, res.statusText);
    return send(
      event,
      typeof res.body === "string" ? res.body : JSON.stringify(res.body, null, 2)
    );
  }
);
async function defaultHandler(error, event, opts) {
  const isSensitive = error.unhandled || error.fatal;
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage || "Server Error";
  const url = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
  if (statusCode === 404) {
    const baseURL = "/";
    if (/^\/[^/]/.test(baseURL) && !url.pathname.startsWith(baseURL)) {
      const redirectTo = `${baseURL}${url.pathname.slice(1)}${url.search}`;
      return {
        status: 302,
        statusText: "Found",
        headers: { location: redirectTo },
        body: `Redirecting...`
      };
    }
  }
  await loadStackTrace(error).catch(consola.error);
  const youch = new Youch();
  if (isSensitive && !opts?.silent) {
    const tags = [error.unhandled && "[unhandled]", error.fatal && "[fatal]"].filter(Boolean).join(" ");
    const ansiError = await (await youch.toANSI(error)).replaceAll(process.cwd(), ".");
    consola.error(
      `[request error] ${tags} [${event.method}] ${url}

`,
      ansiError
    );
  }
  const useJSON = opts?.json || !getRequestHeader(event, "accept")?.includes("text/html");
  const headers = {
    "content-type": useJSON ? "application/json" : "text/html",
    // Prevent browser from guessing the MIME types of resources.
    "x-content-type-options": "nosniff",
    // Prevent error page from being embedded in an iframe
    "x-frame-options": "DENY",
    // Prevent browsers from sending the Referer header
    "referrer-policy": "no-referrer",
    // Disable the execution of any js
    "content-security-policy": "script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"
  };
  if (statusCode === 404 || !getResponseHeader(event, "cache-control")) {
    headers["cache-control"] = "no-cache";
  }
  const body = useJSON ? {
    error: true,
    url,
    statusCode,
    statusMessage,
    message: error.message,
    data: error.data,
    stack: error.stack?.split("\n").map((line) => line.trim())
  } : await youch.toHTML(error, {
    request: {
      url: url.href,
      method: event.method,
      headers: getRequestHeaders(event)
    }
  });
  return {
    status: statusCode,
    statusText: statusMessage,
    headers,
    body
  };
}
async function loadStackTrace(error) {
  if (!(error instanceof Error)) {
    return;
  }
  const parsed = await new ErrorParser().defineSourceLoader(sourceLoader).parse(error);
  const stack = error.message + "\n" + parsed.frames.map((frame) => fmtFrame(frame)).join("\n");
  Object.defineProperty(error, "stack", { value: stack });
  if (error.cause) {
    await loadStackTrace(error.cause).catch(consola.error);
  }
}
async function sourceLoader(frame) {
  if (!frame.fileName || frame.fileType !== "fs" || frame.type === "native") {
    return;
  }
  if (frame.type === "app") {
    const rawSourceMap = await readFile(`${frame.fileName}.map`, "utf8").catch(() => {
    });
    if (rawSourceMap) {
      const consumer = await new SourceMapConsumer(rawSourceMap);
      const originalPosition = consumer.originalPositionFor({ line: frame.lineNumber, column: frame.columnNumber });
      if (originalPosition.source && originalPosition.line) {
        frame.fileName = resolve(dirname(frame.fileName), originalPosition.source);
        frame.lineNumber = originalPosition.line;
        frame.columnNumber = originalPosition.column || 0;
      }
    }
  }
  const contents = await readFile(frame.fileName, "utf8").catch(() => {
  });
  return contents ? { contents } : void 0;
}
function fmtFrame(frame) {
  if (frame.type === "native") {
    return frame.raw;
  }
  const src = `${frame.fileName || ""}:${frame.lineNumber}:${frame.columnNumber})`;
  return frame.functionName ? `at ${frame.functionName} (${src}` : `at ${src}`;
}

const errorHandlers = [errorHandler$2, errorHandler$1];

async function errorHandler(error, event) {
  for (const handler of errorHandlers) {
    try {
      await handler(error, event, { defaultHandler });
      if (event.handled) {
        return; // Response handled
      }
    } catch(error) {
      // Handler itself thrown, log and continue
      console.error(error);
    }
  }
  // H3 will handle fallback
}

const plugins = [
  
];

function useResponseSuccess(data) {
  return {
    code: 0,
    data,
    error: null,
    message: "ok"
  };
}
function usePageResponseSuccess(page, pageSize, list, { message = "ok" } = {}) {
  const pageData = pagination(
    Number.parseInt(`${page}`),
    Number.parseInt(`${pageSize}`),
    list
  );
  return {
    ...useResponseSuccess({
      items: pageData,
      total: list.length
    }),
    message
  };
}
function useResponseError(message, error = null) {
  return {
    code: -1,
    data: null,
    error,
    message
  };
}
function forbiddenResponse(event, message = "Forbidden Exception") {
  setResponseStatus(event, 403);
  return useResponseError(message, message);
}
function unAuthorizedResponse(event) {
  setResponseStatus(event, 401);
  return useResponseError("Unauthorized Exception", "Unauthorized Exception");
}
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function pagination(pageNo, pageSize, array) {
  const offset = (pageNo - 1) * Number(pageSize);
  return offset + Number(pageSize) >= array.length ? array.slice(offset) : array.slice(offset, offset + Number(pageSize));
}

const _ah2sUh = defineEventHandler(async (event) => {
  var _a;
  event.node.res.setHeader(
    "Access-Control-Allow-Origin",
    (_a = event.headers.get("Origin")) != null ? _a : "*"
  );
  if (event.method === "OPTIONS") {
    event.node.res.statusCode = 204;
    event.node.res.statusMessage = "No Content.";
    return "OK";
  } else if (["DELETE", "PATCH", "POST", "PUT"].includes(event.method) && event.path.startsWith("/api/system/")) {
    await sleep(Math.floor(Math.random() * 2e3));
    return forbiddenResponse(event, "\u6F14\u793A\u73AF\u5883\uFF0C\u7981\u6B62\u4FEE\u6539");
  }
});

const _lazy_GjZyiF = () => Promise.resolve().then(function () { return codes$1; });
const _lazy_pjnkS7 = () => Promise.resolve().then(function () { return login_post$1; });
const _lazy_FKsLAr = () => Promise.resolve().then(function () { return logout_post$1; });
const _lazy_om6WDY = () => Promise.resolve().then(function () { return refresh_post$1; });
const _lazy_hXl1rS = () => Promise.resolve().then(function () { return all$1; });
const _lazy_8Zepe5 = () => Promise.resolve().then(function () { return data$2; });
const _lazy_otueCa = () => Promise.resolve().then(function () { return index_post$5; });
const _lazy_2a3ST0 = () => Promise.resolve().then(function () { return _id__delete$3; });
const _lazy_1V2rTw = () => Promise.resolve().then(function () { return _id__get$1; });
const _lazy_dJYoXd = () => Promise.resolve().then(function () { return data$1; });
const _lazy_YoXzRC = () => Promise.resolve().then(function () { return index_post$3; });
const _lazy_9FXV8p = () => Promise.resolve().then(function () { return index_put$3; });
const _lazy_ysRSi5 = () => Promise.resolve().then(function () { return list$b; });
const _lazy_2W14vh = () => Promise.resolve().then(function () { return list2_get$1; });
const _lazy_JJlPri = () => Promise.resolve().then(function () { return proxy$1; });
const _lazy_egSm_i = () => Promise.resolve().then(function () { return upload_post$1; });
const _lazy_bTk3yu = () => Promise.resolve().then(function () { return status$1; });
const _lazy_RIZNSu = () => Promise.resolve().then(function () { return _post$1; });
const _lazy_g7P7Xn = () => Promise.resolve().then(function () { return _id__delete$1; });
const _lazy_aoAf6X = () => Promise.resolve().then(function () { return _id__put$1; });
const _lazy_ilyUIn = () => Promise.resolve().then(function () { return list$9; });
const _lazy_TF_CUS = () => Promise.resolve().then(function () { return new_type$1; });
const _lazy_Did5dD = () => Promise.resolve().then(function () { return sys_notice_type$1; });
const _lazy_szFB1d = () => Promise.resolve().then(function () { return sys_noval_state$1; });
const _lazy_PZePG1 = () => Promise.resolve().then(function () { return getRouters$1; });
const _lazy_OjhWIr = () => Promise.resolve().then(function () { return list$7; });
const _lazy_e1SCi2 = () => Promise.resolve().then(function () { return nameExists$1; });
const _lazy_gC44ba = () => Promise.resolve().then(function () { return pathExists$1; });
const _lazy_XmPQe6 = () => Promise.resolve().then(function () { return _noticeId__delete$1; });
const _lazy_tvIZld = () => Promise.resolve().then(function () { return _noticeId__get$1; });
const _lazy_PFlCSv = () => Promise.resolve().then(function () { return data; });
const _lazy_EdmP0u = () => Promise.resolve().then(function () { return index_post$1; });
const _lazy_d2hvRA = () => Promise.resolve().then(function () { return index_put$1; });
const _lazy_Tcvcpu = () => Promise.resolve().then(function () { return list$5; });
const _lazy_mkAMtP = () => Promise.resolve().then(function () { return list$3; });
const _lazy_oC_1BF = () => Promise.resolve().then(function () { return _userId__get$1; });
const _lazy_dcoo_c = () => Promise.resolve().then(function () { return list$1; });
const _lazy_3FlI2a = () => Promise.resolve().then(function () { return test_get$1; });
const _lazy_6nqoPp = () => Promise.resolve().then(function () { return test_post$1; });
const _lazy_FbAnEN = () => Promise.resolve().then(function () { return info$1; });
const _lazy_ZiH919 = () => Promise.resolve().then(function () { return _____$3; });
const _lazy_PBF6Jb = () => Promise.resolve().then(function () { return _____$1; });

const handlers = [
  { route: '', handler: _ah2sUh, lazy: false, middleware: true, method: undefined },
  { route: '/api/auth/codes', handler: _lazy_GjZyiF, lazy: true, middleware: false, method: undefined },
  { route: '/api/auth/login', handler: _lazy_pjnkS7, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/logout', handler: _lazy_FKsLAr, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/refresh', handler: _lazy_om6WDY, lazy: true, middleware: false, method: "post" },
  { route: '/api/menu/all', handler: _lazy_hXl1rS, lazy: true, middleware: false, method: undefined },
  { route: '/api/noval/manage/data', handler: _lazy_8Zepe5, lazy: true, middleware: false, method: undefined },
  { route: '/api/noval/manage', handler: _lazy_otueCa, lazy: true, middleware: false, method: "post" },
  { route: '/api/noval/news/:id', handler: _lazy_2a3ST0, lazy: true, middleware: false, method: "delete" },
  { route: '/api/noval/news/:id', handler: _lazy_1V2rTw, lazy: true, middleware: false, method: "get" },
  { route: '/api/noval/news/data', handler: _lazy_dJYoXd, lazy: true, middleware: false, method: undefined },
  { route: '/api/noval/news', handler: _lazy_YoXzRC, lazy: true, middleware: false, method: "post" },
  { route: '/api/noval/news', handler: _lazy_9FXV8p, lazy: true, middleware: false, method: "put" },
  { route: '/api/noval/news/list', handler: _lazy_ysRSi5, lazy: true, middleware: false, method: undefined },
  { route: '/api/noval/news/list2', handler: _lazy_2W14vh, lazy: true, middleware: false, method: "get" },
  { route: '/api/proxy', handler: _lazy_JJlPri, lazy: true, middleware: false, method: undefined },
  { route: '/api/resource/oss/upload', handler: _lazy_egSm_i, lazy: true, middleware: false, method: "post" },
  { route: '/api/status', handler: _lazy_bTk3yu, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/dept/', handler: _lazy_RIZNSu, lazy: true, middleware: false, method: "post" },
  { route: '/api/system/dept/:id', handler: _lazy_g7P7Xn, lazy: true, middleware: false, method: "delete" },
  { route: '/api/system/dept/:id', handler: _lazy_aoAf6X, lazy: true, middleware: false, method: "put" },
  { route: '/api/system/dept/list', handler: _lazy_ilyUIn, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/dict/data/type/new_type', handler: _lazy_TF_CUS, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/dict/data/type/sys_notice_type', handler: _lazy_Did5dD, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/dict/data/type/sys_noval_state', handler: _lazy_szFB1d, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/menu/getRouters', handler: _lazy_PZePG1, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/menu/list', handler: _lazy_OjhWIr, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/menu/name-exists', handler: _lazy_e1SCi2, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/menu/path-exists', handler: _lazy_gC44ba, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/notice/:noticeId', handler: _lazy_XmPQe6, lazy: true, middleware: false, method: "delete" },
  { route: '/api/system/notice/:noticeId', handler: _lazy_tvIZld, lazy: true, middleware: false, method: "get" },
  { route: '/api/system/notice/data', handler: _lazy_PFlCSv, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/notice', handler: _lazy_EdmP0u, lazy: true, middleware: false, method: "post" },
  { route: '/api/system/notice', handler: _lazy_d2hvRA, lazy: true, middleware: false, method: "put" },
  { route: '/api/system/notice/list', handler: _lazy_Tcvcpu, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/role/list', handler: _lazy_mkAMtP, lazy: true, middleware: false, method: undefined },
  { route: '/api/system/user/nochang/:userId', handler: _lazy_oC_1BF, lazy: true, middleware: false, method: "get" },
  { route: '/api/table/list', handler: _lazy_dcoo_c, lazy: true, middleware: false, method: undefined },
  { route: '/api/test', handler: _lazy_3FlI2a, lazy: true, middleware: false, method: "get" },
  { route: '/api/test', handler: _lazy_6nqoPp, lazy: true, middleware: false, method: "post" },
  { route: '/api/user/info', handler: _lazy_FbAnEN, lazy: true, middleware: false, method: undefined },
  { route: '/**', handler: _lazy_ZiH919, lazy: true, middleware: false, method: undefined },
  { route: '/noval/NovalForYh/**', handler: _lazy_PBF6Jb, lazy: true, middleware: false, method: undefined }
];

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(true),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const fetchContext = event.node.req?.__unenv__;
      if (fetchContext?._platform) {
        event.context = {
          ...fetchContext._platform,
          ...event.context
        };
      }
      if (!event.context.waitUntil && fetchContext?.waitUntil) {
        event.context.waitUntil = fetchContext.waitUntil;
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (event.context.waitUntil) {
          event.context.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
      await nitroApp$1.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter$1({
    preemptive: true
  });
  const nodeHandler = toNodeListener(h3App);
  const localCall = (aRequest) => callNodeRequestHandler(nodeHandler, aRequest);
  const localFetch = (input, init) => {
    if (!input.toString().startsWith("/")) {
      return globalThis.fetch(input, init);
    }
    return fetchNodeRequestHandler(
      nodeHandler,
      input,
      init
    ).then((response) => normalizeFetchResponse(response));
  };
  const $fetch = createFetch({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp$1 = createNitroApp();
function useNitroApp() {
  return nitroApp$1;
}
runNitroPlugins(nitroApp$1);

const scheduledTasks = false;

const tasks = {
  
};

const __runningTasks__ = {};
async function runTask(name, {
  payload = {},
  context = {}
} = {}) {
  if (__runningTasks__[name]) {
    return __runningTasks__[name];
  }
  if (!(name in tasks)) {
    throw createError({
      message: `Task \`${name}\` is not available!`,
      statusCode: 404
    });
  }
  if (!tasks[name].resolve) {
    throw createError({
      message: `Task \`${name}\` is not implemented!`,
      statusCode: 501
    });
  }
  const handler = await tasks[name].resolve();
  const taskEvent = { name, payload, context };
  __runningTasks__[name] = handler.run(taskEvent);
  try {
    const res = await __runningTasks__[name];
    return res;
  } finally {
    delete __runningTasks__[name];
  }
}

if (!globalThis.crypto) {
  globalThis.crypto = nodeCrypto;
}
const { NITRO_NO_UNIX_SOCKET, NITRO_DEV_WORKER_ID } = process.env;
trapUnhandledNodeErrors();
parentPort?.on("message", (msg) => {
  if (msg && msg.event === "shutdown") {
    shutdown();
  }
});
const nitroApp = useNitroApp();
const server = new Server(toNodeListener(nitroApp.h3App));
let listener;
listen().catch(() => listen(
  true
  /* use random port */
)).catch((error) => {
  console.error("Dev worker failed to listen:", error);
  return shutdown();
});
nitroApp.router.get(
  "/_nitro/tasks",
  defineEventHandler(async (event) => {
    const _tasks = await Promise.all(
      Object.entries(tasks).map(async ([name, task]) => {
        const _task = await task.resolve?.();
        return [name, { description: _task?.meta?.description }];
      })
    );
    return {
      tasks: Object.fromEntries(_tasks),
      scheduledTasks
    };
  })
);
nitroApp.router.use(
  "/_nitro/tasks/:name",
  defineEventHandler(async (event) => {
    const name = getRouterParam(event, "name");
    const payload = {
      ...getQuery$1(event),
      ...await readBody(event).then((r) => r?.payload).catch(() => ({}))
    };
    return await runTask(name, { payload });
  })
);
function listen(useRandomPort = Boolean(
  NITRO_NO_UNIX_SOCKET || process.versions.webcontainer || "Bun" in globalThis && process.platform === "win32"
)) {
  return new Promise((resolve, reject) => {
    try {
      listener = server.listen(useRandomPort ? 0 : getSocketAddress(), () => {
        const address = server.address();
        parentPort?.postMessage({
          event: "listen",
          address: typeof address === "string" ? { socketPath: address } : { host: "localhost", port: address?.port }
        });
        resolve();
      });
    } catch (error) {
      reject(error);
    }
  });
}
function getSocketAddress() {
  const socketName = `nitro-worker-${process.pid}-${threadId}-${NITRO_DEV_WORKER_ID}-${Math.round(Math.random() * 1e4)}.sock`;
  if (process.platform === "win32") {
    return join(String.raw`\\.\pipe`, socketName);
  }
  if (process.platform === "linux") {
    const nodeMajor = Number.parseInt(process.versions.node.split(".")[0], 10);
    if (nodeMajor >= 20) {
      return `\0${socketName}`;
    }
  }
  return join(tmpdir(), socketName);
}
async function shutdown() {
  server.closeAllConnections?.();
  await Promise.all([
    new Promise((resolve) => listener?.close(resolve)),
    nitroApp.hooks.callHook("close").catch(console.error)
  ]);
  parentPort?.postMessage({ event: "exit" });
}

const MOCK_USERS = [
  {
    id: 0,
    password: "123456",
    realName: "Vben",
    roles: ["super"],
    username: "vben"
  },
  {
    id: 1,
    password: "123456",
    realName: "Admin",
    roles: ["admin"],
    username: "admin",
    homePath: "/workspace"
  },
  {
    id: 2,
    password: "123456",
    realName: "Jack",
    roles: ["user"],
    username: "jack",
    homePath: "/analytics"
  }
];
const MOCK_CODES = [
  // super
  {
    codes: ["AC_100100", "AC_100110", "AC_100120", "AC_100010"],
    username: "vben"
  },
  {
    // admin
    codes: ["AC_100010", "AC_100020", "AC_100030"],
    username: "admin"
  },
  {
    // user
    codes: ["AC_1000001", "AC_1000002"],
    username: "jack"
  }
];
const dashboardMenus = [
  {
    meta: {
      order: -1,
      title: "page.dashboard.title"
    },
    name: "Dashboard",
    path: "/dashboard",
    redirect: "/analytics",
    children: [
      {
        name: "Analytics",
        path: "/analytics",
        component: "/dashboard/analytics/index",
        meta: {
          affixTab: true,
          title: "page.dashboard.analytics"
        }
      },
      {
        name: "Workspace",
        path: "/workspace",
        component: "/dashboard/workspace/index",
        meta: {
          title: "page.dashboard.workspace"
        }
      }
    ]
  }
];
const createDemosMenus = (role) => {
  const roleWithMenus = {
    admin: {
      component: "/demos/access/admin-visible",
      meta: {
        icon: "mdi:button-cursor",
        title: "demos.access.adminVisible"
      },
      name: "AccessAdminVisibleDemo",
      path: "/demos/access/admin-visible"
    },
    super: {
      component: "/demos/access/super-visible",
      meta: {
        icon: "mdi:button-cursor",
        title: "demos.access.superVisible"
      },
      name: "AccessSuperVisibleDemo",
      path: "/demos/access/super-visible"
    },
    user: {
      component: "/demos/access/user-visible",
      meta: {
        icon: "mdi:button-cursor",
        title: "demos.access.userVisible"
      },
      name: "AccessUserVisibleDemo",
      path: "/demos/access/user-visible"
    }
  };
  return [
    {
      meta: {
        icon: "ic:baseline-view-in-ar",
        keepAlive: true,
        order: 1e3,
        title: "demos.title"
      },
      name: "Demos",
      path: "/demos",
      redirect: "/demos/access",
      children: [
        {
          name: "AccessDemos",
          path: "/demosaccess",
          meta: {
            icon: "mdi:cloud-key-outline",
            title: "demos.access.backendPermissions"
          },
          redirect: "/demos/access/page-control",
          children: [
            {
              name: "AccessPageControlDemo",
              path: "/demos/access/page-control",
              component: "/demos/access/index",
              meta: {
                icon: "mdi:page-previous-outline",
                title: "demos.access.pageAccess"
              }
            },
            {
              name: "AccessButtonControlDemo",
              path: "/demos/access/button-control",
              component: "/demos/access/button-control",
              meta: {
                icon: "mdi:button-cursor",
                title: "demos.access.buttonControl"
              }
            },
            {
              name: "AccessMenuVisible403Demo",
              path: "/demos/access/menu-visible-403",
              component: "/demos/access/menu-visible-403",
              meta: {
                authority: ["no-body"],
                icon: "mdi:button-cursor",
                menuVisibleWithForbidden: true,
                title: "demos.access.menuVisible403"
              }
            },
            roleWithMenus[role]
          ]
        }
      ]
    }
  ];
};
const MOCK_MENUS = [
  {
    menus: [...dashboardMenus, ...createDemosMenus("super")],
    username: "vben"
  },
  {
    menus: [...dashboardMenus, ...createDemosMenus("admin")],
    username: "admin"
  },
  {
    menus: [...dashboardMenus, ...createDemosMenus("user")],
    username: "jack"
  }
];
const MOCK_MENU_LIST = [
  {
    id: 1,
    name: "Workspace",
    status: 1,
    type: "menu",
    icon: "mdi:dashboard",
    path: "/workspace",
    component: "/dashboard/workspace/index",
    meta: {
      icon: "carbon:workspace",
      title: "page.dashboard.workspace",
      affixTab: true,
      order: 0
    }
  },
  {
    id: 2,
    meta: {
      icon: "carbon:settings",
      order: 9997,
      title: "system.title",
      badge: "new",
      badgeType: "normal",
      badgeVariants: "primary"
    },
    status: 1,
    type: "catalog",
    name: "System",
    path: "/system",
    children: [
      {
        id: 201,
        pid: 2,
        path: "/system/menu",
        name: "SystemMenu",
        authCode: "System:Menu:List",
        status: 1,
        type: "menu",
        meta: {
          icon: "carbon:menu",
          title: "system.menu.title"
        },
        component: "/system/menu/list",
        children: [
          {
            id: 20101,
            pid: 201,
            name: "SystemMenuCreate",
            status: 1,
            type: "button",
            authCode: "System:Menu:Create",
            meta: { title: "common.create" }
          },
          {
            id: 20102,
            pid: 201,
            name: "SystemMenuEdit",
            status: 1,
            type: "button",
            authCode: "System:Menu:Edit",
            meta: { title: "common.edit" }
          },
          {
            id: 20103,
            pid: 201,
            name: "SystemMenuDelete",
            status: 1,
            type: "button",
            authCode: "System:Menu:Delete",
            meta: { title: "common.delete" }
          }
        ]
      },
      {
        id: 202,
        pid: 2,
        path: "/system/dept",
        name: "SystemDept",
        status: 1,
        type: "menu",
        authCode: "System:Dept:List",
        meta: {
          icon: "carbon:container-services",
          title: "system.dept.title"
        },
        component: "/system/dept/list",
        children: [
          {
            id: 20401,
            pid: 201,
            name: "SystemDeptCreate",
            status: 1,
            type: "button",
            authCode: "System:Dept:Create",
            meta: { title: "common.create" }
          },
          {
            id: 20402,
            pid: 201,
            name: "SystemDeptEdit",
            status: 1,
            type: "button",
            authCode: "System:Dept:Edit",
            meta: { title: "common.edit" }
          },
          {
            id: 20403,
            pid: 201,
            name: "SystemDeptDelete",
            status: 1,
            type: "button",
            authCode: "System:Dept:Delete",
            meta: { title: "common.delete" }
          }
        ]
      }
    ]
  },
  {
    id: 9,
    meta: {
      badgeType: "dot",
      order: 9998,
      title: "demos.vben.title",
      icon: "carbon:data-center"
    },
    name: "Project",
    path: "/vben-admin",
    type: "catalog",
    status: 1,
    children: [
      {
        id: 901,
        pid: 9,
        name: "VbenDocument",
        path: "/vben-admin/document",
        component: "IFrameView",
        type: "embedded",
        status: 1,
        meta: {
          icon: "carbon:book",
          iframeSrc: "https://doc.vben.pro",
          title: "demos.vben.document"
        }
      },
      {
        id: 902,
        pid: 9,
        name: "VbenGithub",
        path: "/vben-admin/github",
        component: "IFrameView",
        type: "link",
        status: 1,
        meta: {
          icon: "carbon:logo-github",
          link: "https://github.com/vbenjs/vue-vben-admin",
          title: "Github"
        }
      },
      {
        id: 903,
        pid: 9,
        name: "VbenAntdv",
        path: "/vben-admin/antdv",
        component: "IFrameView",
        type: "link",
        status: 0,
        meta: {
          icon: "carbon:hexagon-vertical-solid",
          badgeType: "dot",
          link: "https://ant.vben.pro",
          title: "demos.vben.antdv"
        }
      }
    ]
  },
  {
    id: 10,
    component: "_core/about/index",
    type: "menu",
    status: 1,
    meta: {
      icon: "lucide:copyright",
      order: 9999,
      title: "demos.vben.about"
    },
    name: "About",
    path: "/about"
  }
];
function getMenuIds(menus) {
  const ids = [];
  menus.forEach((item) => {
    ids.push(item.id);
    if (item.children && item.children.length > 0) {
      ids.push(...getMenuIds(item.children));
    }
  });
  return ids;
}

const ACCESS_TOKEN_SECRET = "access_token_secret";
const REFRESH_TOKEN_SECRET = "refresh_token_secret";
function generateAccessToken(user) {
  return jwt.sign(user, ACCESS_TOKEN_SECRET, { expiresIn: "7d" });
}
function generateRefreshToken(user) {
  return jwt.sign(user, REFRESH_TOKEN_SECRET, {
    expiresIn: "30d"
  });
}
function verifyAccessToken(event) {
  const authHeader = getHeader(event, "Authorization");
  if (!(authHeader == null ? void 0 : authHeader.startsWith("Bearer"))) {
    return null;
  }
  const token = authHeader.split(" ")[1];
  try {
    const decoded = jwt.verify(token, ACCESS_TOKEN_SECRET);
    const username = decoded.username;
    const user = MOCK_USERS.find((item) => item.username === username);
    const { password: _pwd, ...userinfo } = user;
    return userinfo;
  } catch {
    return null;
  }
}
function verifyRefreshToken(token) {
  try {
    const decoded = jwt.verify(token, REFRESH_TOKEN_SECRET);
    const username = decoded.username;
    const user = MOCK_USERS.find((item) => item.username === username);
    const { password: _pwd, ...userinfo } = user;
    return userinfo;
  } catch {
    return null;
  }
}

const codes = eventHandler((event) => {
  var _a, _b;
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const codes = (_b = (_a = MOCK_CODES.find((item) => item.username === userinfo.username)) == null ? void 0 : _a.codes) != null ? _b : [];
  return useResponseSuccess(codes);
});

const codes$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: codes
});

function clearRefreshTokenCookie(event) {
  deleteCookie(event, "jwt", {
    httpOnly: true,
    sameSite: "none",
    secure: true
  });
}
function setRefreshTokenCookie(event, refreshToken) {
  setCookie(event, "jwt", refreshToken, {
    httpOnly: true,
    maxAge: 24 * 60 * 60,
    // unit: seconds
    sameSite: "none",
    secure: true
  });
}
function getRefreshTokenFromCookie(event) {
  const refreshToken = getCookie(event, "jwt");
  return refreshToken;
}

const login_post = defineEventHandler(async (event) => {
  const { password, username } = await readBody(event);
  if (!password || !username) {
    setResponseStatus(event, 400);
    return useResponseError(
      "BadRequestException",
      "Username and password are required"
    );
  }
  const findUser = MOCK_USERS.find(
    (item) => item.username === username && item.password === password
  );
  if (!findUser) {
    clearRefreshTokenCookie(event);
    return forbiddenResponse(event, "Username or password is incorrect.");
  }
  const accessToken = generateAccessToken(findUser);
  const refreshToken = generateRefreshToken(findUser);
  setRefreshTokenCookie(event, refreshToken);
  return useResponseSuccess({
    ...findUser,
    accessToken
  });
});

const login_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: login_post
});

const logout_post = defineEventHandler(async (event) => {
  const refreshToken = getRefreshTokenFromCookie(event);
  if (!refreshToken) {
    return useResponseSuccess("");
  }
  clearRefreshTokenCookie(event);
  return useResponseSuccess("");
});

const logout_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: logout_post
});

const refresh_post = defineEventHandler(async (event) => {
  const refreshToken = getRefreshTokenFromCookie(event);
  if (!refreshToken) {
    return forbiddenResponse(event);
  }
  clearRefreshTokenCookie(event);
  const userinfo = verifyRefreshToken(refreshToken);
  if (!userinfo) {
    return forbiddenResponse(event);
  }
  const findUser = MOCK_USERS.find(
    (item) => item.username === userinfo.username
  );
  if (!findUser) {
    return forbiddenResponse(event);
  }
  const accessToken = generateAccessToken(findUser);
  setRefreshTokenCookie(event, refreshToken);
  return accessToken;
});

const refresh_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: refresh_post
});

const all = eventHandler(async (event) => {
  var _a, _b;
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const menus = (_b = (_a = MOCK_MENUS.find((item) => item.username === userinfo.username)) == null ? void 0 : _a.menus) != null ? _b : [];
  return useResponseSuccess(menus);
});

const all$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: all
});

let manageData = [];
let nextId$1 = 1;
function getNextManageId() {
  return nextId$1++;
}
function addManage(manage) {
  manageData.push(manage);
}
function getManageData() {
  return manageData;
}

const data$2 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  addManage: addManage,
  getManageData: getManageData,
  getNextManageId: getNextManageId
});

const index_post$4 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const body = await readBody(event);
  const newId = getNextManageId();
  const newManage = {
    id: newId,
    name: body.name || "",
    flag: body.flag || "",
    author: body.author || "",
    createTime: (/* @__PURE__ */ new Date()).toISOString()
  };
  addManage(newManage);
  return { code: 200, data: newManage, msg: "ok" };
});

const index_post$5 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_post$4
});

let newsData = [
  {
    id: 1,
    // id（后端随机生成）
    otherId: 1,
    // 消息发起人id (管理员)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u6B22\u8FCE\u4F7F\u7528\u6211\u4EEC\u7684\u5C0F\u8BF4\u8BBA\u575B\uFF01\u8BF7\u9075\u5B88\u793E\u533A\u89C4\u5219\uFF0C\u6587\u660E\u53D1\u8A00\u3002",
    type: 0,
    // 管理员信息
    isread: 0,
    // 未读
    createTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    // id（后端随机生成）
    otherId: 2,
    // 消息发起人id (用户2)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u60A8\u7684\u5E16\u5B50\u300A\u5173\u4E8E\u7384\u5E7B\u5C0F\u8BF4\u7684\u8BA8\u8BBA\u300B\u6536\u5230\u4E86\u65B0\u7684\u56DE\u590D",
    type: 1,
    // 论坛信息
    isread: 0,
    // 未读
    createTime: "2024-01-15 14:20:00"
  },
  {
    id: 3,
    // id（后端随机生成）
    otherId: 3,
    // 消息发起人id (用户3)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u4F60\u597D\uFF0C\u6211\u60F3\u548C\u4F60\u8BA8\u8BBA\u4E00\u4E0B\u6700\u8FD1\u770B\u7684\u5C0F\u8BF4",
    type: 2,
    // 私聊信息
    isread: 0,
    // 未读
    createTime: "2024-01-15 16:45:00"
  },
  {
    id: 4,
    // id（后端随机生成）
    otherId: 1,
    // 消息发起人id (管理员)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u7CFB\u7EDF\u5C06\u4E8E\u4ECA\u665A22:00-24:00\u8FDB\u884C\u7EF4\u62A4\uFF0C\u671F\u95F4\u53EF\u80FD\u5F71\u54CD\u6B63\u5E38\u4F7F\u7528\u3002",
    type: 0,
    // 管理员信息
    isread: 1,
    // 已读
    createTime: "2024-01-16 09:00:00"
  },
  {
    id: 5,
    // id（后端随机生成）
    otherId: 4,
    // 消息发起人id (用户4)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u6709\u4EBA\u70B9\u8D5E\u4E86\u60A8\u7684\u8BC4\u8BBA",
    type: 3,
    // 点赞消息
    isread: 0,
    // 未读
    createTime: "2024-01-16 11:30:00"
  },
  {
    id: 6,
    // id（后端随机生成）
    otherId: 5,
    // 消息发起人id (用户5)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u611F\u8C22\u60A8\u7684\u63A8\u8350\uFF0C\u90A3\u672C\u5C0F\u8BF4\u771F\u7684\u5F88\u4E0D\u9519\uFF01",
    type: 2,
    // 私聊信息
    isread: 0,
    // 未读
    createTime: "2024-01-16 15:20:00"
  },
  {
    id: 7,
    // id（后端随机生成）
    otherId: 2,
    // 消息发起人id (用户2)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u6700\u8FD1\u6709\u4EC0\u4E48\u597D\u770B\u7684\u5C0F\u8BF4\u63A8\u8350\u5417\uFF1F",
    type: 2,
    // 私聊信息
    isread: 0,
    // 未读
    createTime: "2024-01-17 09:15:00"
  },
  {
    id: 8,
    // id（后端随机生成）
    otherId: 6,
    // 消息发起人id (用户6)
    MyId: 1,
    // 消息接收者id (用户1)
    news: "\u4F60\u597D\uFF0C\u6211\u770B\u5230\u4F60\u7684\u4E66\u8BC4\u5199\u5F97\u5F88\u597D\uFF0C\u60F3\u548C\u4F60\u4EA4\u6D41\u4E00\u4E0B",
    type: 2,
    // 私聊信息
    isread: 0,
    // 未读
    createTime: "2024-01-17 14:30:00"
  }
];
function getNextNewsId() {
  return Math.max(...newsData.map((item) => Number(item.id))) + 1;
}
function addNews(news) {
  newsData.push(news);
}
function updateNews(id, updates) {
  const index = newsData.findIndex((item) => Number(item.id) === id);
  if (index === -1) {
    return false;
  }
  newsData[index] = { ...newsData[index], ...updates };
  return true;
}
function deleteNews(id) {
  const index = newsData.findIndex((item) => Number(item.id) === id);
  if (index === -1) {
    return false;
  }
  newsData.splice(index, 1);
  return true;
}
function deleteNewsMultiple(ids) {
  let deletedCount = 0;
  ids.forEach((id) => {
    if (deleteNews(id)) {
      deletedCount++;
    }
  });
  return deletedCount;
}
function findNews(id) {
  return newsData.find((item) => Number(item.id) === id);
}
function findNewsByReceiver(MyId) {
  return newsData.filter((item) => Number(item.MyId) === Number(MyId));
}
function findNewsBySender(otherId) {
  return newsData.filter((item) => Number(item.otherId) === Number(otherId));
}
function findNewsByType(type) {
  return newsData.filter((item) => Number(item.type) === Number(type));
}

const data$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  addNews: addNews,
  deleteNews: deleteNews,
  deleteNewsMultiple: deleteNewsMultiple,
  findNews: findNews,
  findNewsByReceiver: findNewsByReceiver,
  findNewsBySender: findNewsBySender,
  findNewsByType: findNewsByType,
  getNextNewsId: getNextNewsId,
  newsData: newsData,
  updateNews: updateNews
});

const _id__delete$2 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const idsParam = getRouterParam(event, "id");
  const ids = String(idsParam).split(",").map((id) => Number(id.trim()));
  deleteNewsMultiple(ids);
  return useResponseSuccess(null);
});

const _id__delete$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__delete$2
});

const _id__get = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const newsId = Number(getRouterParam(event, "id"));
  const news = findNews(newsId);
  if (!news) {
    throw createError({
      statusCode: 404,
      statusMessage: "\u6D88\u606F\u4E0D\u5B58\u5728"
    });
  }
  return useResponseSuccess(news);
});

const _id__get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__get
});

const index_post$2 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const body = await readBody(event);
  const newId = getNextNewsId();
  const newNews = {
    id: newId,
    // id（后端随机生成）
    otherId: body.otherId || userinfo.userId,
    // 消息发起人id，默认为当前用户
    MyId: body.MyId,
    // 消息接收者id
    news: body.news,
    // 消息内容
    type: body.type || 2,
    // 消息类型，默认为私聊信息
    isread: body.isread || 0,
    // 是否已读，默认为未读
    createTime: (/* @__PURE__ */ new Date()).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    }).replace(/\//g, "-")
  };
  addNews(newNews);
  return useResponseSuccess(null);
});

const index_post$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_post$2
});

const index_put$2 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const body = await readBody(event);
  const success = updateNews(body.id, {
    otherId: body.otherId,
    MyId: body.MyId,
    news: body.news,
    type: body.type,
    isread: body.isread
  });
  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: "\u6D88\u606F\u4E0D\u5B58\u5728"
    });
  }
  return useResponseSuccess(null);
});

const index_put$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_put$2
});

const list$a = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const {
    pageNum = 1,
    pageSize = 20,
    id,
    otherId,
    MyId,
    news,
    type,
    isread
  } = getQuery$1(event);
  let listData = structuredClone(newsData);
  if (id) {
    listData = listData.filter(
      (item) => String(item.id).includes(String(id))
    );
  }
  if (otherId) {
    listData = listData.filter(
      (item) => String(item.otherId).includes(String(otherId))
    );
  }
  if (MyId) {
    listData = listData.filter(
      (item) => String(item.MyId).includes(String(MyId))
    );
  }
  if (news) {
    listData = listData.filter(
      (item) => item.news.toLowerCase().includes(String(news).toLowerCase())
    );
  }
  if (type !== void 0 && type !== "") {
    listData = listData.filter((item) => String(item.type) === String(type));
  }
  if (isread !== void 0 && isread !== "") {
    listData = listData.filter((item) => String(item.isread) === String(isread));
  }
  listData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
  const total = listData.length;
  const startIndex = (Number(pageNum) - 1) * Number(pageSize);
  const endIndex = startIndex + Number(pageSize);
  const paginatedData = listData.slice(startIndex, endIndex);
  return useResponseSuccess({
    rows: paginatedData,
    total
  });
});

const list$b = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list$a
});

const list2_get = eventHandler(async (event) => {
  console.log("=== list2.get.ts \u63A5\u6536\u5230\u8BF7\u6C42 ===");
  console.log("\u8BF7\u6C42URL:", event.node.req.url);
  console.log("\u8BF7\u6C42\u65B9\u6CD5:", event.node.req.method);
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    console.log("\u7528\u6237\u8BA4\u8BC1\u5931\u8D25");
    return unAuthorizedResponse(event);
  }
  console.log("\u7528\u6237\u8BA4\u8BC1\u6210\u529F:", userinfo);
  const query = getQuery$1(event);
  const MyId = query.MyId;
  const otherId = query.otherId;
  const type = query.type;
  console.log("\u540E\u7AEF\u63A5\u6536\u5230\u7684\u8BF7\u6C42\u53C2\u6570:", { MyId, otherId, type });
  console.log("\u539F\u59CB\u67E5\u8BE2\u53C2\u6570:", query);
  console.log("\u53C2\u6570\u7C7B\u578B\u68C0\u67E5:", {
    MyId: typeof MyId,
    otherId: typeof otherId,
    type: typeof type
  });
  if (!MyId || !otherId || type === void 0) {
    console.error("\u7F3A\u5C11\u5FC5\u8981\u7684\u53C2\u6570:", { MyId, otherId, type });
    throw createError({
      statusCode: 400,
      statusMessage: "\u7F3A\u5C11\u5FC5\u8981\u7684\u53C2\u6570: MyId, otherId, type"
    });
  }
  let filteredData = structuredClone(newsData);
  filteredData = filteredData.filter(
    (item) => String(item.type) === String(type) && (String(item.MyId) === String(MyId) && String(item.otherId) === String(otherId) || String(item.MyId) === String(otherId) && String(item.otherId) === String(MyId))
  );
  filteredData.sort((a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime());
  filteredData = filteredData.map((item) => ({
    ...item,
    id: String(item.id),
    otherId: String(item.otherId),
    MyId: String(item.MyId),
    type: String(item.type)
  }));
  console.log("\u540E\u7AEF\u8FD4\u56DE\u7684\u6570\u636E:", filteredData);
  return {
    code: 200,
    data: filteredData,
    msg: "ok"
  };
});

const list2_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list2_get
});

const proxy = defineEventHandler(async (event) => {
  try {
    const query = getQuery$1(event);
    const url = query.url;
    if (!url) {
      return {
        code: 400,
        message: "\u7F3A\u5C11URL\u53C2\u6570"
      };
    }
    try {
      new URL(url);
    } catch (e) {
      return {
        code: 400,
        message: "URL\u683C\u5F0F\u65E0\u6548"
      };
    }
    const headers = {
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      "Cache-Control": "max-age=0",
      "Connection": "keep-alive",
      "Upgrade-Insecure-Requests": "1",
      "Referer": new URL(url).origin
    };
    const isQidian = url.includes("qidian.com");
    const isNewQidianChapter = url.includes("qidian.com/chapter/");
    const response = await axios.get(url, {
      headers,
      timeout: 15e3,
      // 15秒超时
      // 某些网站可能需要跟随重定向
      maxRedirects: 5
    });
    const html = response.data;
    let content = "";
    let title = "";
    if (typeof html === "string") {
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (titleMatch && titleMatch[1]) {
        title = titleMatch[1].trim();
      }
      if (isQidian) {
        if (isNewQidianChapter) {
          try {
            let chapterContent = "";
            let chapterTitle = "";
            const titleSelectors = [
              /<h[1-3][^>]*class="[^"]*chapter-title[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<h[1-3][^>]*class="[^"]*j_chapterName[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<h[1-3][^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<div[^>]*class="[^"]*chapter-name[^"]*"[^>]*>([^<]+)<\/div>/i,
              /<div[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/div>/i,
              /<span[^>]*class="[^"]*content-wrap[^"]*"[^>]*>([^<]+)<\/span>/i,
              /<span[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/span>/i,
              /<span[^>]*class="[^"]*chapter[^"]*"[^>]*>([^<]+)<\/span>/i
            ];
            if (!chapterTitle) {
              const urlMatch = url.match(/\/chapter\/\d+\/(\d+)\/?/);
              if (urlMatch && urlMatch[1]) {
                const chapterIdPattern = new RegExp(`data-chapter-id="${urlMatch[1]}"[^>]*>([^<]+)<`, "i");
                const chapterIdMatch = html.match(chapterIdPattern);
                if (chapterIdMatch && chapterIdMatch[1]) {
                  chapterTitle = chapterIdMatch[1].trim();
                }
              }
            }
            for (const selector of titleSelectors) {
              const match = html.match(selector);
              if (match && match[1]) {
                chapterTitle = match[1].trim();
                if (chapterTitle) break;
              }
            }
            if (chapterTitle) {
              title = chapterTitle;
            }
            const contentSelectors = [
              { regex: /<div[^>]*class="[^"]*content-text[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*read-content[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*chapter-content[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" },
              { regex: /<div[^>]*id="content"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*main-text-wrap[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*content-wrap[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*j_readContent[^"]*"[^>]*>(([\s\S]*?)<\/div>)/i, type: "div" }
            ];
            for (const selector of contentSelectors) {
              const match = html.match(selector.regex);
              if (match && match[1]) {
                chapterContent = match[1];
                const paragraphs = chapterContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                if (paragraphs && paragraphs.length > 0) {
                  const processedParagraphs = paragraphs.map((p) => {
                    const text = p.replace(/<[^>]+>/g, "").trim();
                    if (text && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")) {
                      return `<p>${text}</p>`;
                    }
                    return "";
                  }).filter((p) => p);
                  if (processedParagraphs.length > 0) {
                    content = processedParagraphs.join("\n");
                    break;
                  }
                }
              }
            }
            if (!content) {
              try {
                const metaDescriptionMatch = html.match(/<meta[^>]*name="description"[^>]*content="([^"]+)"[^>]*>/i) || html.match(/<meta[^>]*property="og:description"[^>]*content="([^"]+)"[^>]*>/i);
                if (metaDescriptionMatch && metaDescriptionMatch[1]) {
                  const description = metaDescriptionMatch[1].trim();
                  if (description.length > 50 && !description.includes("\u8D77\u70B9\u4E2D\u6587\u7F51") && !description.includes("\u5C0F\u8BF4\u9605\u8BFB") && !description.includes("\u7CBE\u5F69\u5C0F\u8BF4")) {
                    content = `<p>${description}</p>`;
                    if (description.includes("\u7AE0\u8282\u5185\u5BB9\u7531") || description.includes("\u5185\u5BB9\u66F4\u65B0") || description.includes("\u6700\u65B0\u7AE0\u8282")) {
                      content += "\n<p>\u6CE8\u610F\uFF1A\u5F53\u524D\u9875\u9762\u53EF\u80FD\u9700\u8981\u767B\u5F55\u6216\u8005\u901A\u8FC7\u5176\u4ED6\u65B9\u5F0F\u8BBF\u95EE\u624D\u80FD\u67E5\u770B\u5B8C\u6574\u5185\u5BB9\u3002</p>";
                    }
                  }
                }
              } catch (e) {
                console.error("\u4ECEmeta\u6807\u7B7E\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
              }
            }
            if (!content) {
              try {
                const jsonLdMatch = html.match(/<script[^>]*type="application\/ld\+json"[^>]*>([\s\S]*?)<\/script>/i);
                if (jsonLdMatch && jsonLdMatch[1]) {
                  try {
                    const jsonLdData = JSON.parse(jsonLdMatch[1]);
                    let articleBody = "";
                    if (jsonLdData.articleBody) {
                      articleBody = jsonLdData.articleBody;
                    } else if (jsonLdData.mainEntity && jsonLdData.mainEntity.articleBody) {
                      articleBody = jsonLdData.mainEntity.articleBody;
                    } else if (jsonLdData.description) {
                      articleBody = jsonLdData.description;
                    }
                    if (articleBody && articleBody.length > 100) {
                      const paragraphs = articleBody.split(/\n+|<br\s*\/?>|\r\n/).filter((p) => p.trim().length > 5).map((p) => `<p>${p.trim()}</p>`);
                      if (paragraphs.length > 0) {
                        content = paragraphs.join("\n");
                      }
                    }
                  } catch (jsonError) {
                    console.error("\u89E3\u6790JSON-LD\u6570\u636E\u5931\u8D25:", jsonError);
                  }
                }
              } catch (e) {
                console.error("\u4ECEJSON-LD\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
              }
            }
            if (!content) {
              try {
                const dataElements = html.match(/<[^>]*data-eid="[^"]*"[^>]*>([\s\S]*?)<\/[^>]*>/gi);
                if (dataElements && dataElements.length > 0) {
                  let longestContent = "";
                  for (const element of dataElements) {
                    const contentMatch = element.match(/>([\s\S]*?)<\//i);
                    if (contentMatch && contentMatch[1]) {
                      const elementContent = contentMatch[1].trim();
                      if (elementContent.length > longestContent.length && elementContent.length > 100 && !elementContent.includes("\u5E7F\u544A") && !elementContent.includes("\u8D77\u70B9")) {
                        longestContent = elementContent;
                      }
                    }
                  }
                  if (longestContent) {
                    const paragraphs = longestContent.split(/\n+|<br\s*\/?>|\r\n/).filter((p) => p.trim().length > 5).map((p) => `<p>${p.trim()}</p>`);
                    if (paragraphs.length > 0) {
                      content = paragraphs.join("\n");
                    }
                  }
                }
              } catch (e) {
                console.error("\u4ECEdata\u5C5E\u6027\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
              }
            }
            if (!content) {
              try {
                const scriptDataMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({[\s\S]*?});/i) || html.match(/g_data\s*=\s*({[\s\S]*?});/i) || html.match(/chapter\s*:\s*({[\s\S]*?}),/i) || html.match(/chapterInfo\s*:\s*({[\s\S]*?}),/i);
                if (scriptDataMatch && scriptDataMatch[1]) {
                  const scriptDataText = scriptDataMatch[1];
                  const contentMatch = scriptDataText.match(/content\s*:\s*['"]([\s\S]*?)['"]/) || scriptDataText.match(/chapterContent\s*:\s*['"]([\s\S]*?)['"]/) || scriptDataText.match(/body\s*:\s*['"]([\s\S]*?)['"]/);
                  if (contentMatch && contentMatch[1]) {
                    let extractedContent = contentMatch[1];
                    try {
                      const jsonData = JSON.parse(`{"content":"${extractedContent}"}`);
                      if (jsonData && jsonData.content) {
                        extractedContent = jsonData.content;
                      }
                    } catch (jsonError) {
                      console.error("JSON\u89E3\u6790\u5931\u8D25\uFF0C\u4F7F\u7528\u539F\u59CB\u5185\u5BB9:", jsonError);
                    }
                    extractedContent = extractedContent.replace(/\\n/g, "\n").replace(/\\r/g, "").replace(/\\t/g, "").replace(/\\\\/g, "\\").replace(/\\'/g, "'").replace(/\\"/g, '"');
                    const paragraphs = extractedContent.split(/\n+/).filter((p) => p.trim().length > 5).map((p) => `<p>${p.trim()}</p>`);
                    if (paragraphs.length > 0) {
                      content = paragraphs.join("\n");
                    }
                  }
                }
              } catch (e) {
                console.error("\u4ECEJavaScript\u6570\u636E\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
              }
            }
            if (!content) {
              const contentContainerMatch = html.match(/<div[^>]*class="[^"]*main-text-wrap[^"]*"[^>]*>([\s\S]*?)<\/div>/i) || html.match(/<div[^>]*class="[^"]*content-wrap[^"]*"[^>]*>([\s\S]*?)<\/div>/i) || html.match(/<div[^>]*class="[^"]*j_readContent[^"]*"[^>]*>([\s\S]*?)<\/div>/i) || html.match(/<article[^>]*class="[^"]*article-content[^"]*"[^>]*>([\s\S]*?)<\/article>/i);
              if (contentContainerMatch && contentContainerMatch[1]) {
                const containerContent = contentContainerMatch[1];
                const paragraphMatches = containerContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                if (paragraphMatches && paragraphMatches.length > 0) {
                  const filteredParagraphs = paragraphMatches.map((p) => {
                    const text = p.replace(/<[^>]+>/g, "").trim();
                    if (text && text.length > 5 && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")) {
                      return `<p>${text}</p>`;
                    }
                    return "";
                  }).filter((p) => p);
                  if (filteredParagraphs.length > 0) {
                    content = filteredParagraphs.join("\n");
                  }
                }
              }
            }
            if (!content) {
              let cleanHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "").replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "").replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, "").replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, "").replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, "").replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, "");
              const allParagraphs = cleanHtml.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
              if (allParagraphs && allParagraphs.length > 0) {
                const filteredParagraphs = allParagraphs.map((p) => {
                  const text = p.replace(/<[^>]+>/g, "").trim();
                  if (text && text.length > 5 && // 过滤掉太短的段落
                  !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")) {
                    return `<p>${text}</p>`;
                  }
                  return "";
                }).filter((p) => p);
                if (filteredParagraphs.length > 0) {
                  content = filteredParagraphs.join("\n");
                }
              }
              if (!content) {
                const textNodes = cleanHtml.match(/>[^<]{10,}</g);
                if (textNodes && textNodes.length > 0) {
                  const paragraphs = textNodes.map((node) => node.slice(1, -1).trim()).filter(
                    (text) => text && text.length > 10 && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")
                  ).map((text) => `<p>${text}</p>`);
                  if (paragraphs.length > 0) {
                    content = paragraphs.join("\n");
                  }
                }
              }
            }
          } catch (e) {
            console.error("\u63D0\u53D6\u65B0\u7248\u8D77\u70B9\u7AE0\u8282\u5185\u5BB9\u5931\u8D25:", e);
          }
          if (!content && isNewQidianChapter) {
            try {
              const iframeMatches = html.match(/<iframe[^>]*src="([^"]+)"[^>]*>/gi);
              if (iframeMatches && iframeMatches.length > 0) {
                for (const iframeTag of iframeMatches) {
                  const srcMatch = iframeTag.match(/src="([^"]+)"/i);
                  if (srcMatch && srcMatch[1]) {
                    const iframeSrc = srcMatch[1];
                    const absoluteUrl = iframeSrc.startsWith("http") ? iframeSrc : new URL(iframeSrc, new URL(url).origin).toString();
                    console.log("\u5C1D\u8BD5\u4ECEiframe\u83B7\u53D6\u5185\u5BB9:", absoluteUrl);
                    if (absoluteUrl.includes("qidian.com") || absoluteUrl.includes("chapter")) {
                      content = `<p>\u68C0\u6D4B\u5230\u5185\u5BB9\u53EF\u80FD\u5728iframe\u4E2D\uFF0C\u8BF7\u76F4\u63A5\u8BBF\u95EE: <a href="${absoluteUrl}" target="_blank">${absoluteUrl}</a></p>`;
                      break;
                    }
                  }
                }
              }
            } catch (e) {
              console.error("\u4ECEiframe\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
            }
          }
          if (!content && isNewQidianChapter) {
            try {
              const commentMatches = html.match(/<!--([\s\S]*?)-->/g);
              if (commentMatches && commentMatches.length > 0) {
                let longestComment = "";
                for (const comment of commentMatches) {
                  const cleanComment = comment.replace(/<!--/, "").replace(/-->/, "").trim();
                  if (cleanComment.length > longestComment.length && cleanComment.includes("<p>") && !cleanComment.includes("\u5E7F\u544A") && !cleanComment.includes("\u8D77\u70B9")) {
                    longestComment = cleanComment;
                  }
                }
                if (longestComment && longestComment.length > 100) {
                  const paragraphs = longestComment.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                  if (paragraphs && paragraphs.length > 0) {
                    const filteredParagraphs = paragraphs.map((p) => {
                      const text = p.replace(/<[^>]+>/g, "").trim();
                      if (text && text.length > 5 && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A")) {
                        return `<p>${text}</p>`;
                      }
                      return "";
                    }).filter((p) => p);
                    if (filteredParagraphs.length > 0) {
                      content = filteredParagraphs.join("\n");
                    }
                  }
                }
              }
            } catch (e) {
              console.error("\u4ECEHTML\u6CE8\u91CA\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
            }
          }
          if (!content && isNewQidianChapter) {
            try {
              const hiddenElements = html.match(/<div[^>]*style="[^"]*display:\s*none[^"]*"[^>]*>([\s\S]*?)<\/div>/gi) || html.match(/<div[^>]*class="[^"]*hidden[^"]*"[^>]*>([\s\S]*?)<\/div>/gi);
              if (hiddenElements && hiddenElements.length > 0) {
                let longestHiddenContent = "";
                for (const element of hiddenElements) {
                  const contentMatch = element.match(/>([\s\S]*?)<\/div>/i);
                  if (contentMatch && contentMatch[1]) {
                    const hiddenContent = contentMatch[1].trim();
                    if (hiddenContent.length > longestHiddenContent.length && hiddenContent.length > 200 && hiddenContent.includes("<p>") && !hiddenContent.includes("\u5E7F\u544A") && !hiddenContent.includes("\u8D77\u70B9")) {
                      longestHiddenContent = hiddenContent;
                    }
                  }
                }
                if (longestHiddenContent) {
                  const paragraphs = longestHiddenContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                  if (paragraphs && paragraphs.length > 0) {
                    const filteredParagraphs = paragraphs.map((p) => {
                      const text = p.replace(/<[^>]+>/g, "").trim();
                      if (text && text.length > 5) {
                        return `<p>${text}</p>`;
                      }
                      return "";
                    }).filter((p) => p);
                    if (filteredParagraphs.length > 0) {
                      content = filteredParagraphs.join("\n");
                    }
                  }
                }
              }
            } catch (e) {
              console.error("\u4ECE\u9690\u85CF\u5143\u7D20\u63D0\u53D6\u5185\u5BB9\u5931\u8D25:", e);
            }
          }
          if (!content && isNewQidianChapter) {
            content = `<p>\u65E0\u6CD5\u63D0\u53D6\u7AE0\u8282\u5185\u5BB9\uFF0C\u53EF\u80FD\u539F\u56E0\uFF1A</p>
<p>1. \u8BE5\u7AE0\u8282\u9700\u8981\u767B\u5F55\u540E\u624D\u80FD\u9605\u8BFB</p>
<p>2. \u8BE5\u7AE0\u8282\u53EF\u80FD\u662F\u4ED8\u8D39\u5185\u5BB9</p>
<p>3. \u7F51\u7AD9\u7ED3\u6784\u5DF2\u66F4\u65B0\uFF0C\u63D0\u53D6\u65B9\u6CD5\u9700\u8981\u66F4\u65B0</p>
<p>\u8BF7\u5C1D\u8BD5\u76F4\u63A5\u8BBF\u95EE\u539F\u7F51\u9875: <a href="${url}" target="_blank">${url}</a></p>`;
            try {
              let cleanHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "").replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "").replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, "").replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, "").replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, "").replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, "").replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, "").replace(/<button\b[^<]*(?:(?!<\/button>)<[^<]*)*<\/button>/gi, "").replace(/<input\b[^<]*(?:(?!<\/input>)<[^<]*)*<\/input>/gi, "").replace(/<select\b[^<]*(?:(?!<\/select>)<[^<]*)*<\/select>/gi, "").replace(/<textarea\b[^<]*(?:(?!<\/textarea>)<[^<]*)*<\/textarea>/gi, "");
              const plainText = cleanHtml.replace(/<[^>]+>/g, " ").replace(/\s+/g, " ").trim();
              if (plainText && plainText.length > 100) {
                const sentences = plainText.split(/\.\s+|。\s*|!\s*|！\s*|\?\s*|？\s*/);
                const paragraphs = [];
                let currentParagraph = "";
                for (const sentence of sentences) {
                  const trimmedSentence = sentence.trim();
                  if (trimmedSentence.length > 10) {
                    if (currentParagraph.length < 100) {
                      currentParagraph += trimmedSentence + "\u3002";
                    } else {
                      paragraphs.push(`<p>${currentParagraph}</p>`);
                      currentParagraph = trimmedSentence + "\u3002";
                    }
                  }
                }
                if (currentParagraph.length > 0) {
                  paragraphs.push(`<p>${currentParagraph}</p>`);
                }
                if (paragraphs.length > 0) {
                  content = paragraphs.join("\n");
                }
              }
            } catch (e) {
              console.error("\u6700\u7EC8\u6587\u672C\u63D0\u53D6\u65B9\u6CD5\u5931\u8D25:", e);
            }
          }
        } else {
          try {
            let chapterContent = "";
            let chapterTitle = "";
            const qidianSelectors = [
              // 主要内容选择器
              { regex: /<div[^>]*class="[^"]*read-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: "div" },
              { regex: /<div[^>]*id="content"[^>]*>([\s\S]*?)<\/div>/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*content-text[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: "div" },
              { regex: /<div[^>]*class="[^"]*chapter-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: "div" },
              // 2023年新版起点网站结构
              { regex: /<div[^>]*class="[^"]*content-wrap[^"]*"[^>]*>([\s\S]*?)<\/div>/i, type: "div" },
              // 老版本结构
              { regex: /<div[^>]*class="[^"]*box-center[^"]*"[^>]*>([\s\S]*?)<div[^>]*class="[^"]*chapter-control[^"]*"[^>]*>/i, type: "content" }
            ];
            const titleSelectors = [
              /<h[1-3][^>]*class="[^"]*j_chapterName[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<h[1-3][^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/h[1-3]>/i,
              /<h[1-3][^>]*>([^<]+)<\/h[1-3]>/i,
              /<div[^>]*class="[^"]*chapter-name[^"]*"[^>]*>([^<]+)<\/div>/i,
              /<div[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)<\/div>/i
            ];
            for (const selector of titleSelectors) {
              const match = html.match(selector);
              if (match && match[1]) {
                chapterTitle = match[1].trim();
                if (chapterTitle) break;
              }
            }
            if (chapterTitle) {
              title = chapterTitle;
            }
            for (const selector of qidianSelectors) {
              const match = html.match(selector.regex);
              if (match && match[1]) {
                chapterContent = match[1];
                if (selector.type === "div") {
                  const paragraphs = chapterContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                  if (paragraphs && paragraphs.length > 0) {
                    const processedParagraphs = paragraphs.map((p) => {
                      const text = p.replace(/<[^>]+>/g, "").trim();
                      if (text && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")) {
                        return `<p>${text}</p>`;
                      }
                      return "";
                    }).filter((p) => p);
                    if (processedParagraphs.length > 0) {
                      content = processedParagraphs.join("\n");
                      break;
                    }
                  }
                } else if (selector.type === "content") {
                  let processedContent = chapterContent.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "").replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "").replace(/<div[^>]*class="[^"]*chapter-control[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*ad-content[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*banner[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*footer[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "");
                  const paragraphs = processedContent.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
                  if (paragraphs && paragraphs.length > 0) {
                    const processedParagraphs = paragraphs.map((p) => {
                      const text = p.replace(/<[^>]+>/g, "").trim();
                      if (text && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")) {
                        return `<p>${text}</p>`;
                      }
                      return "";
                    }).filter((p) => p);
                    if (processedParagraphs.length > 0) {
                      content = processedParagraphs.join("\n");
                      break;
                    }
                  } else {
                    content = processedContent.replace(/<[^>]+>/g, "\n").replace(/\n+/g, "\n\n").replace(/\s{2,}/g, " ").trim();
                    if (content) {
                      const paragraphs2 = content.split("\n\n").filter((p) => p.trim() && p.trim().length > 5).map((p) => `<p>${p.trim()}</p>`);
                      if (paragraphs2.length > 0) {
                        content = paragraphs2.join("\n");
                        break;
                      }
                    }
                  }
                }
              }
            }
            if (!content) {
              const allParagraphs = html.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
              if (allParagraphs && allParagraphs.length > 0) {
                const filteredParagraphs = allParagraphs.map((p) => {
                  const text = p.replace(/<[^>]+>/g, "").trim();
                  if (text && text.length > 5 && // 过滤掉太短的段落
                  !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")) {
                    return `<p>${text}</p>`;
                  }
                  return "";
                }).filter((p) => p);
                if (filteredParagraphs.length > 0) {
                  content = filteredParagraphs.join("\n");
                }
              }
            }
            if (!content) {
              let cleanHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "").replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "").replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, "").replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, "").replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, "").replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, "").replace(/<div[^>]*class="[^"]*header[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*footer[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*nav[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*menu[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*ad[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "");
              const textNodes = cleanHtml.match(/>[^<]{10,}</g);
              if (textNodes && textNodes.length > 0) {
                const paragraphs = textNodes.map((node) => node.slice(1, -1).trim()).filter(
                  (text) => text && text.length > 10 && !text.includes("\u8D77\u70B9") && !text.includes("\u5E7F\u544A") && !text.includes("\u63A8\u8350") && !text.includes("\u624B\u673A") && !text.includes("APP") && !text.includes("\u8BF7\u8BB0\u4F4F\u672C\u7AD9") && !text.includes("\u7F51\u5740") && !text.includes("\u6536\u85CF\u672C\u7AD9")
                ).map((text) => `<p>${text}</p>`);
                if (paragraphs.length > 0) {
                  content = paragraphs.join("\n");
                }
              }
            }
          } catch (e) {
            console.error("\u63D0\u53D6\u8D77\u70B9\u5185\u5BB9\u5931\u8D25:", e);
          }
        }
      }
      if (!content) {
        const cleanHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "").replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "").replace(/<head\b[^<]*(?:(?!<\/head>)<[^<]*)*<\/head>/gi, "").replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, "").replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, "").replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, "").replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, "").replace(/<div[^>]*class="[^"]*header[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*footer[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*nav[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*ad[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "").replace(/<div[^>]*class="[^"]*comment[^"]*"[^>]*>[\s\S]*?<\/div>/gi, "");
        const paragraphs = cleanHtml.match(/<p[^>]*>([\s\S]*?)<\/p>/gi);
        if (paragraphs && paragraphs.length > 0) {
          const processedParagraphs = paragraphs.map((p) => {
            const text = p.replace(/<[^>]+>/g, "").trim();
            if (text && text.length > 5) {
              return `<p>${text}</p>`;
            }
            return "";
          }).filter((p) => p);
          if (processedParagraphs.length > 0) {
            content = processedParagraphs.join("\n");
          }
        }
        if (!content) {
          const textNodes = cleanHtml.match(/>[^<]{10,}</g);
          if (textNodes && textNodes.length > 0) {
            const paragraphs2 = textNodes.map((node) => node.slice(1, -1).trim()).filter((text) => text && text.length > 10).map((text) => `<p>${text}</p>`);
            if (paragraphs2.length > 0) {
              content = paragraphs2.join("\n");
            }
          }
        }
        if (!content) {
          const plainText = cleanHtml.replace(/<[^>]+>/g, " ").replace(/\s+/g, " ").trim();
          if (plainText) {
            const paragraphs2 = plainText.split(/\.\s+|。\s*|!\s*|！\s*|\?\s*|？\s*/).filter((p) => p.trim().length > 10).map((p) => `<p>${p.trim()}.</p>`);
            if (paragraphs2.length > 0) {
              content = paragraphs2.join("\n");
            } else {
              content = `<p>${plainText}</p>`;
            }
          }
        }
      }
    }
    return {
      code: 200,
      data: {
        title,
        content,
        url
      }
    };
  } catch (error) {
    console.error("\u4EE3\u7406\u8BF7\u6C42\u5931\u8D25:", error);
    return {
      code: 500,
      message: `\u8BF7\u6C42\u5931\u8D25: ${error.message || "\u672A\u77E5\u9519\u8BEF"}`
    };
  }
});

const proxy$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: proxy
});

const upload_post = defineEventHandler(async (event) => {
  var _a;
  const formData = await readMultipartFormData(event);
  const newId = Math.floor(Math.random() * 1e6).toString();
  const uploadedFile = formData.find((item) => item.name === "file");
  const fileName = uploadedFile ? uploadedFile.filename : "default.txt";
  const path = ((_a = formData.find((item) => item.name === "path")) == null ? void 0 : _a.data.toString()) || "";
  const url = `http://mock.cos.com/${path}/${fileName}`;
  const data = {
    ossId: newId,
    url,
    fileName,
    originalName: fileName,
    fileSuffix: ".txt",
    createTime: (/* @__PURE__ */ new Date()).toISOString(),
    service: "cos"
  };
  return { code: 200, data, msg: "ok" };
});

const upload_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: upload_post
});

const status = eventHandler((event) => {
  const { status } = getQuery$1(event);
  setResponseStatus(event, Number(status));
  return useResponseError(`${status}`);
});

const status$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: status
});

const _post = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  await sleep(600);
  return useResponseSuccess(null);
});

const _post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _post
});

const _id__delete = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  await sleep(1e3);
  return useResponseSuccess(null);
});

const _id__delete$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__delete
});

const _id__put = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  await sleep(2e3);
  return useResponseSuccess(null);
});

const _id__put$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _id__put
});

const formatterCN$1 = new Intl.DateTimeFormat("zh-CN", {
  timeZone: "Asia/Shanghai",
  year: "numeric",
  month: "2-digit",
  day: "2-digit",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit"
});
function generateMockDataList$2(count) {
  const dataList = [];
  for (let i = 0; i < count; i++) {
    const dataItem = {
      id: faker.string.uuid(),
      pid: 0,
      name: faker.commerce.department(),
      status: faker.helpers.arrayElement([0, 1]),
      createTime: formatterCN$1.format(
        faker.date.between({ from: "2021-01-01", to: "2022-12-31" })
      ),
      remark: faker.lorem.sentence()
    };
    if (faker.datatype.boolean()) {
      dataItem.children = Array.from(
        { length: faker.number.int({ min: 1, max: 5 }) },
        () => ({
          id: faker.string.uuid(),
          pid: dataItem.id,
          name: faker.commerce.department(),
          status: faker.helpers.arrayElement([0, 1]),
          createTime: formatterCN$1.format(
            faker.date.between({ from: "2023-01-01", to: "2023-12-31" })
          ),
          remark: faker.lorem.sentence()
        })
      );
    }
    dataList.push(dataItem);
  }
  return dataList;
}
const mockData$2 = generateMockDataList$2(10);
const list$8 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const listData = structuredClone(mockData$2);
  return useResponseSuccess(listData);
});

const list$9 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list$8
});

const new_type = eventHandler(async (event) => {
  return useResponseSuccess([
    {
      dictCode: 1,
      dictLabel: "\u7BA1\u7406\u5458\u4FE1\u606F",
      dictValue: "0",
      dictType: "new_type",
      dictSort: 1,
      status: "0",
      remark: "\u7BA1\u7406\u5458\u53D1\u9001\u7684\u7CFB\u7EDF\u4FE1\u606F",
      createTime: "2024-01-01 00:00:00"
    },
    {
      dictCode: 2,
      dictLabel: "\u8BBA\u575B\u4FE1\u606F",
      dictValue: "1",
      dictType: "new_type",
      dictSort: 2,
      status: "0",
      remark: "\u8BBA\u575B\u76F8\u5173\u7684\u901A\u77E5\u4FE1\u606F",
      createTime: "2024-01-01 00:00:00"
    },
    {
      dictCode: 3,
      dictLabel: "\u79C1\u804A\u4FE1\u606F",
      dictValue: "2",
      dictType: "new_type",
      dictSort: 3,
      status: "0",
      remark: "\u7528\u6237\u4E4B\u95F4\u7684\u79C1\u804A\u6D88\u606F",
      createTime: "2024-01-01 00:00:00"
    }
  ]);
});

const new_type$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: new_type
});

const sys_notice_type = eventHandler(async (event) => {
  return useResponseSuccess([
    {
      dictCode: 1,
      dictLabel: "\u901A\u77E5",
      dictValue: "1",
      dictType: "sys_notice_type",
      dictSort: 1,
      status: "0",
      remark: "\u7CFB\u7EDF\u901A\u77E5",
      createTime: "2024-01-01 00:00:00"
    },
    {
      dictCode: 2,
      dictLabel: "\u516C\u544A",
      dictValue: "2",
      dictType: "sys_notice_type",
      dictSort: 2,
      status: "0",
      remark: "\u7CFB\u7EDF\u516C\u544A",
      createTime: "2024-01-01 00:00:00"
    }
  ]);
});

const sys_notice_type$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: sys_notice_type
});

const sys_noval_state = eventHandler(async (event) => {
  return useResponseSuccess([
    {
      dictCode: 1,
      dictLabel: "\u8349\u7A3F",
      dictValue: "0",
      dictType: "sys_noval_state",
      dictSort: 1,
      status: "0",
      remark: "\u8349\u7A3F\u72B6\u6001",
      createTime: "2024-01-01 00:00:00"
    },
    {
      dictCode: 2,
      dictLabel: "\u5DF2\u53D1\u5E03",
      dictValue: "1",
      dictType: "sys_noval_state",
      dictSort: 2,
      status: "0",
      remark: "\u5DF2\u53D1\u5E03\u72B6\u6001",
      createTime: "2024-01-01 00:00:00"
    },
    {
      dictCode: 3,
      dictLabel: "\u5DF2\u4E0B\u67B6",
      dictValue: "2",
      dictType: "sys_noval_state",
      dictSort: 3,
      status: "0",
      remark: "\u5DF2\u4E0B\u67B6\u72B6\u6001",
      createTime: "2024-01-01 00:00:00"
    }
  ]);
});

const sys_noval_state$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: sys_noval_state
});

const ROUTER_MENUS = [
  {
    id: 1,
    name: "Workspace",
    path: "/workspace",
    component: "/dashboard/workspace/index",
    hidden: false,
    meta: {
      icon: "carbon:workspace",
      title: "page.dashboard.workspace",
      noCache: false
    },
    children: []
  },
  {
    id: 2,
    name: "Analytics",
    path: "/analytics",
    component: "/dashboard/analytics/index",
    hidden: false,
    meta: {
      icon: "lucide:area-chart",
      title: "page.dashboard.analytics",
      noCache: false
    },
    children: []
  }
];
const getRouters = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  return useResponseSuccess(ROUTER_MENUS);
});

const getRouters$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: getRouters
});

const list$6 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  return useResponseSuccess(MOCK_MENU_LIST);
});

const list$7 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list$6
});

const namesMap = {};
function getNames(menus) {
  menus.forEach((menu) => {
    namesMap[menu.name] = String(menu.id);
    if (menu.children) {
      getNames(menu.children);
    }
  });
}
getNames(MOCK_MENU_LIST);
const nameExists = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const { id, name } = getQuery$1(event);
  return name in namesMap && (!id || namesMap[name] !== String(id)) ? useResponseSuccess(true) : useResponseSuccess(false);
});

const nameExists$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: nameExists
});

const pathMap = { "/": 0 };
function getPaths(menus) {
  menus.forEach((menu) => {
    pathMap[menu.path] = String(menu.id);
    if (menu.children) {
      getPaths(menu.children);
    }
  });
}
getPaths(MOCK_MENU_LIST);
const pathExists = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const { id, path } = getQuery$1(event);
  return path in pathMap && (!id || pathMap[path] !== String(id)) ? useResponseSuccess(true) : useResponseSuccess(false);
});

const pathExists$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: pathExists
});

let noticeData = [
  {
    noticeId: 1,
    noticeTitle: "\u7CFB\u7EDF\u7EF4\u62A4\u901A\u77E5",
    noticeType: "1",
    // 通知
    noticeContent: "\u7CFB\u7EDF\u5C06\u4E8E2024\u5E742\u67081\u65E5\u51CC\u66682\u70B9\u81F34\u70B9\u8FDB\u884C\u4F8B\u884C\u7EF4\u62A4\uFF0C\u671F\u95F4\u7CFB\u7EDF\u5C06\u4E0D\u53EF\u7528\u3002",
    status: "0",
    // 正常状态
    remark: "\u7CFB\u7EDF\u7EF4\u62A4",
    createBy: "admin",
    createByName: "\u7BA1\u7406\u5458",
    createTime: "2024-01-20 10:00:00"
  },
  {
    noticeId: 2,
    noticeTitle: "\u65B0\u529F\u80FD\u4E0A\u7EBF\u516C\u544A",
    noticeType: "2",
    // 公告
    noticeContent: "\u6211\u4EEC\u5F88\u9AD8\u5174\u5730\u5BA3\u5E03\uFF0C\u65B0\u7684\u5C0F\u8BF4\u63A8\u8350\u529F\u80FD\u5DF2\u7ECF\u4E0A\u7EBF\uFF0C\u6B22\u8FCE\u5927\u5BB6\u4F53\u9A8C\uFF01",
    status: "0",
    // 正常状态
    remark: "\u529F\u80FD\u66F4\u65B0",
    createBy: "admin",
    createByName: "\u7BA1\u7406\u5458",
    createTime: "2024-01-22 14:30:00"
  },
  {
    noticeId: 3,
    noticeTitle: "\u7528\u6237\u884C\u4E3A\u89C4\u8303\u901A\u77E5",
    noticeType: "1",
    // 通知
    noticeContent: "\u8BF7\u6240\u6709\u7528\u6237\u9075\u5B88\u793E\u533A\u89C4\u5219\uFF0C\u6587\u660E\u53D1\u8A00\uFF0C\u5171\u540C\u7EF4\u62A4\u826F\u597D\u7684\u793E\u533A\u73AF\u5883\u3002",
    status: "0",
    // 正常状态
    remark: "\u884C\u4E3A\u89C4\u8303",
    createBy: "admin",
    createByName: "\u7BA1\u7406\u5458",
    createTime: "2024-01-25 09:15:00"
  }
];
function getNextNoticeId() {
  return Math.max(...noticeData.map((item) => Number(item.noticeId))) + 1;
}
function addNotice(notice) {
  noticeData.push(notice);
}
function updateNotice(noticeId, updates) {
  const index = noticeData.findIndex((item) => Number(item.noticeId) === noticeId);
  if (index === -1) {
    return false;
  }
  noticeData[index] = { ...noticeData[index], ...updates };
  return true;
}
function deleteNotice(noticeId) {
  const index = noticeData.findIndex((item) => Number(item.noticeId) === noticeId);
  if (index === -1) {
    return false;
  }
  noticeData.splice(index, 1);
  return true;
}
function deleteNoticeMultiple(ids) {
  let success = true;
  for (const id of ids) {
    if (!deleteNotice(id)) {
      success = false;
    }
  }
  return success;
}
function findNotice(noticeId) {
  return noticeData.find((item) => Number(item.noticeId) === noticeId);
}
function findNoticeByType(noticeType) {
  return noticeData.filter((item) => item.noticeType === noticeType);
}

const data = /*#__PURE__*/Object.freeze({
  __proto__: null,
  addNotice: addNotice,
  deleteNotice: deleteNotice,
  deleteNoticeMultiple: deleteNoticeMultiple,
  findNotice: findNotice,
  findNoticeByType: findNoticeByType,
  getNextNoticeId: getNextNoticeId,
  noticeData: noticeData,
  updateNotice: updateNotice
});

const _noticeId__delete = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const noticeId = Number(getRouterParam(event, "noticeId"));
  const success = deleteNotice(noticeId);
  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: "\u901A\u77E5\u4E0D\u5B58\u5728"
    });
  }
  return useResponseSuccess(null);
});

const _noticeId__delete$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _noticeId__delete
});

const _noticeId__get = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const noticeId = Number(getRouterParam(event, "noticeId"));
  const notice = findNotice(noticeId);
  if (!notice) {
    throw createError({
      statusCode: 404,
      statusMessage: "\u901A\u77E5\u4E0D\u5B58\u5728"
    });
  }
  return useResponseSuccess(notice);
});

const _noticeId__get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _noticeId__get
});

const index_post = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const body = await readBody(event);
  const newId = getNextNoticeId();
  const newNotice = {
    noticeId: newId,
    noticeTitle: body.noticeTitle,
    noticeType: body.noticeType,
    noticeContent: body.noticeContent,
    status: body.status || "0",
    // 默认为正常状态
    remark: body.remark || "",
    createBy: userinfo.username,
    createByName: userinfo.realName,
    createTime: (/* @__PURE__ */ new Date()).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
  };
  addNotice(newNotice);
  return useResponseSuccess(newNotice);
});

const index_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_post
});

const index_put = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const body = await readBody(event);
  const success = updateNotice(body.noticeId, {
    noticeTitle: body.noticeTitle,
    noticeType: body.noticeType,
    noticeContent: body.noticeContent,
    status: body.status,
    remark: body.remark
  });
  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: "\u901A\u77E5\u4E0D\u5B58\u5728"
    });
  }
  return useResponseSuccess(null);
});

const index_put$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_put
});

const list$4 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const {
    pageNum = 1,
    pageSize = 20,
    noticeTitle,
    createBy,
    noticeType
  } = getQuery$1(event);
  let listData = structuredClone(noticeData);
  if (noticeTitle) {
    listData = listData.filter(
      (item) => item.noticeTitle.includes(noticeTitle)
    );
  }
  if (createBy) {
    listData = listData.filter(
      (item) => item.createBy.includes(createBy)
    );
  }
  if (noticeType) {
    listData = listData.filter((item) => item.noticeType === noticeType);
  }
  listData.sort((a, b) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
  });
  const total = listData.length;
  const pageNumInt = parseInt(pageNum);
  const pageSizeInt = parseInt(pageSize);
  const startIndex = (pageNumInt - 1) * pageSizeInt;
  const endIndex = startIndex + pageSizeInt;
  const rows = listData.slice(startIndex, endIndex);
  return useResponseSuccess({
    total,
    rows,
    pageNum: pageNumInt,
    pageSize: pageSizeInt
  });
});

const list$5 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list$4
});

const formatterCN = new Intl.DateTimeFormat("zh-CN", {
  timeZone: "Asia/Shanghai",
  year: "numeric",
  month: "2-digit",
  day: "2-digit",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit"
});
const menuIds = getMenuIds(MOCK_MENU_LIST);
function generateMockDataList$1(count) {
  const dataList = [];
  for (let i = 0; i < count; i++) {
    const dataItem = {
      id: faker.string.uuid(),
      name: faker.commerce.product(),
      status: faker.helpers.arrayElement([0, 1]),
      createTime: formatterCN.format(
        faker.date.between({ from: "2022-01-01", to: "2025-01-01" })
      ),
      permissions: faker.helpers.arrayElements(menuIds),
      remark: faker.lorem.sentence()
    };
    dataList.push(dataItem);
  }
  return dataList;
}
const mockData$1 = generateMockDataList$1(100);
const list$2 = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const {
    page = 1,
    pageSize = 20,
    name,
    id,
    remark,
    startTime,
    endTime,
    status
  } = getQuery$1(event);
  let listData = structuredClone(mockData$1);
  if (name) {
    listData = listData.filter(
      (item) => item.name.toLowerCase().includes(String(name).toLowerCase())
    );
  }
  if (id) {
    listData = listData.filter(
      (item) => item.id.toLowerCase().includes(String(id).toLowerCase())
    );
  }
  if (remark) {
    listData = listData.filter(
      (item) => {
        var _a, _b;
        return (_b = (_a = item.remark) == null ? void 0 : _a.toLowerCase()) == null ? void 0 : _b.includes(String(remark).toLowerCase());
      }
    );
  }
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  if (["0", "1"].includes(status)) {
    listData = listData.filter((item) => item.status === Number(status));
  }
  return usePageResponseSuccess(page, pageSize, listData);
});

const list$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list$2
});

const _userId__get = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const userId = getRouterParam(event, "userId");
  const user = MOCK_USERS.find((u) => u.id === Number(userId));
  if (!user) {
    setResponseStatus(event, 404);
    return useResponseError("UserNotFoundException", `User with id ${userId} not found`);
  }
  const userVo = {
    userId: user.id,
    userName: user.username,
    nickName: user.realName,
    email: `${user.username}@example.com`,
    phonenumber: "13800138000",
    sex: "0",
    avatar: "",
    status: "0",
    delFlag: "0",
    loginIp: "127.0.0.1",
    loginDate: (/* @__PURE__ */ new Date()).toISOString(),
    createBy: "admin",
    createTime: "2023-01-01 00:00:00",
    updateBy: "admin",
    updateTime: (/* @__PURE__ */ new Date()).toISOString().replace("T", " ").substring(0, 19),
    remark: "",
    dept: {
      deptId: 100,
      parentId: 0,
      deptName: "\u82E5\u4F9D\u79D1\u6280",
      orderNum: 0,
      leader: user.realName,
      phone: "15888888888",
      email: "<EMAIL>",
      status: "0",
      delFlag: "0"
    },
    roles: user.roles.map((role, index) => ({
      roleId: index + 1,
      roleName: role,
      roleKey: role,
      roleSort: index + 1,
      dataScope: "1",
      menuCheckStrictly: false,
      deptCheckStrictly: false,
      status: "0",
      delFlag: "0",
      flag: false,
      admin: role === "super"
    }))
  };
  return useResponseSuccess(userVo);
});

const _userId__get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _userId__get
});

function generateMockDataList(count) {
  const dataList = [];
  for (let i = 0; i < count; i++) {
    const dataItem = {
      id: faker.string.uuid(),
      imageUrl: faker.image.avatar(),
      imageUrl2: faker.image.avatar(),
      open: faker.datatype.boolean(),
      status: faker.helpers.arrayElement(["success", "error", "warning"]),
      productName: faker.commerce.productName(),
      price: faker.commerce.price(),
      currency: faker.finance.currencyCode(),
      quantity: faker.number.int({ min: 1, max: 100 }),
      available: faker.datatype.boolean(),
      category: faker.commerce.department(),
      releaseDate: faker.date.past(),
      rating: faker.number.float({ min: 1, max: 5 }),
      description: faker.commerce.productDescription(),
      weight: faker.number.float({ min: 0.1, max: 10 }),
      color: faker.color.human(),
      inProduction: faker.datatype.boolean(),
      tags: Array.from({ length: 3 }, () => faker.commerce.productAdjective())
    };
    dataList.push(dataItem);
  }
  return dataList;
}
const mockData = generateMockDataList(100);
const list = eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  await sleep(600);
  const { page, pageSize, sortBy, sortOrder } = getQuery$1(event);
  const listData = structuredClone(mockData);
  if (sortBy && Reflect.has(listData[0], sortBy)) {
    listData.sort((a, b) => {
      if (sortOrder === "asc") {
        if (sortBy === "price") {
          return Number.parseFloat(a[sortBy]) - Number.parseFloat(b[sortBy]);
        } else {
          return a[sortBy] > b[sortBy] ? 1 : -1;
        }
      } else {
        if (sortBy === "price") {
          return Number.parseFloat(b[sortBy]) - Number.parseFloat(a[sortBy]);
        } else {
          return a[sortBy] < b[sortBy] ? 1 : -1;
        }
      }
    });
  }
  return usePageResponseSuccess(page, pageSize, listData);
});

const list$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: list
});

const test_get = defineEventHandler(() => "Test get handler");

const test_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: test_get
});

const test_post = defineEventHandler(() => "Test post handler");

const test_post$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: test_post
});

const info = eventHandler((event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  return useResponseSuccess(userinfo);
});

const info$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: info
});

const _____$2 = defineEventHandler(() => {
  return `
<h1>Hello Vben Admin</h1>
<h2>Mock service is starting</h2>
<ul>
<li><a href="/api/user">/api/user/info</a></li>
<li><a href="/api/menu">/api/menu/all</a></li>
<li><a href="/api/auth/codes">/api/auth/codes</a></li>
<li><a href="/api/auth/login">/api/auth/login</a></li>
</ul>
`;
});

const _____$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _____$2
});

let novels = Array.from({ length: 10 }, (_, id) => ({
  id: id + 1,
  name: faker.lorem.words(3),
  author: faker.person.fullName(),
  platform: faker.helpers.arrayElement(["PlatformA", "PlatformB"]),
  type: faker.helpers.arrayElement(["Fantasy", "Sci-Fi"]),
  status: faker.helpers.arrayElement(["Ongoing", "Completed"])
}));
let nextId = novels.length + 1;
const _____ = defineEventHandler(async (event) => {
  const method = event.method;
  let path = event.path;
  const basePath = "/noval/NovalForYh";
  if (path.startsWith(basePath)) {
    path = path.substring(basePath.length);
  }
  if (method === "GET" && path.match(/^\/getInfo\/(.+)$/)) {
    const match = path.match(/^\/getInfo\/(.+)$/);
    if (match && match[1]) {
      const idsStr = match[1];
      const ids = idsStr.split(",").map(Number);
      const result = novels.filter((n) => ids.includes(n.id));
      return { code: 200, rows: result, total: result.length };
    }
  }
  if (method === "GET" && path === "/list") {
    const query = getQuery$1(event);
    const pageNum = parseInt(query.pageNum) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    const name = query.name || "";
    const author = query.author || "";
    const platform = query.platform || "";
    const type = query.type || "";
    const filtered = novels.filter(
      (n) => n.name.toLowerCase().includes(name.toLowerCase()) && n.author.toLowerCase().includes(author.toLowerCase()) && (!platform || n.platform === platform) && (!type || n.type === type)
    );
    const start = (pageNum - 1) * pageSize;
    const paginated = filtered.slice(start, start + pageSize);
    return { code: 200, rows: paginated, total: filtered.length };
  }
  if (method === "GET" && path.match(/^\/(\d+(,\d+)*)$/)) {
    const idsStr = path.slice(1);
    const ids = idsStr.split(",").map(Number);
    const result = novels.filter((n) => ids.includes(n.id));
    return { code: 200, data: result };
  }
  if (method === "GET" && path.match(/^\/(\d+)$/)) {
    const id = parseInt(path.slice(1));
    const novel = novels.find((n) => n.id === id);
    return novel ? { code: 200, data: novel } : { code: 404, msg: "Not found" };
  }
  if (method === "POST" && path === "") {
    const data = await readBody(event);
    const newNovel = { id: nextId++, ...data };
    novels.push(newNovel);
    return { code: 200, data: newNovel };
  }
  if (method === "PUT" && path === "") {
    const data = await readBody(event);
    const index = novels.findIndex((n) => n.id === data.id);
    if (index !== -1) {
      novels[index] = { ...novels[index], ...data };
      return { code: 200, data: novels[index] };
    }
    return { code: 404, msg: "Not found" };
  }
  if (method === "POST" && path.endsWith("/remove")) {
    const { ids } = await readBody(event);
    novels = novels.filter((n) => !ids.includes(n.id));
    return { code: 200, msg: "Deleted" };
  }
  if (method === "POST" && path.endsWith("/export")) {
    return { code: 200, data: "Mock export data" };
  }
  return { code: 404, msg: "Endpoint not found" };
});

const _____$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _____
});
//# sourceMappingURL=index.mjs.map
