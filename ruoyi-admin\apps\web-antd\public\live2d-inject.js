/**
 * Live2D 模型注入脚本
 * 用于在外链页面中注入Live2D模型
 */

(function() {
  // 获取当前页面的referrer，检查是否来自我们的应用
  const referrer = document.referrer;
  const allowedDomains = [
    'localhost:5666',
    '127.0.0.1:5666',
    'localhost',
    '127.0.0.1',
    'qidian.com',
    'www.qidian.com',
    'book.qidian.com',
    'read.qidian.com'
    // 可以添加更多允许的域名
  ];

  console.log('当前页面URL:', window.location.href);
  console.log('当前页面referrer:', referrer);

  // 检查是否来自我们的应用
  function isFromOurApp() {
    // 检查是否在起点中文网
    const isQidian = window.location.hostname.includes('qidian.com');
    if (isQidian) {
      console.log('检测到起点中文网环境，允许注入Live2D模型');
      // 在起点中文网环境下，设置标记以便后续使用
      sessionStorage.setItem('from_novel_app', 'true');
      return true;
    }

    // 首先检查sessionStorage中的标记
    const fromNovelApp = sessionStorage.getItem('from_novel_app');
    if (fromNovelApp === 'true') {
      console.log('检测到来自我们应用的标记');
      return true;
    } else {
      console.log('未检测到来自我们应用的标记，from_novel_app =', fromNovelApp);
    }

    // 如果没有标记，则检查referrer
    if (!referrer) {
      console.log('未检测到referrer');
      return false;
    }

    try {
      const referrerUrl = new URL(referrer);
      const referrerHost = referrerUrl.host;
      console.log('检测到referrer:', referrerHost);

      const isAllowed = allowedDomains.some(domain => referrerHost.includes(domain));
      if (isAllowed) {
        console.log('检测到来自允许域名的referrer:', referrerHost);
      } else {
        console.log('referrer不在允许的域名列表中');
      }
      return isAllowed;
    } catch (e) {
      console.error('解析referrer URL时出错:', e);
      return false;
    }
  }

  // 从URL中获取参数
  function getUrlParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  }

  // 从localStorage获取Live2D配置
  function getLive2DConfig() {
    console.log('尝试从localStorage获取Live2D配置');
    try {
      // 尝试从live2dConfig键获取
      let configStr = localStorage.getItem('live2dConfig');
      if (configStr) {
        const config = JSON.parse(configStr);
        console.log('从live2dConfig键获取到配置:', config);
        return config;
      } else {
        console.log('live2dConfig键中没有找到配置');
      }

      // 尝试从live2d_config键获取（兼容旧版本）
      configStr = localStorage.getItem('live2d_config');
      if (configStr) {
        const config = JSON.parse(configStr);
        console.log('从live2d_config键获取到配置:', config);
        // 同时更新到新的键名
        localStorage.setItem('live2dConfig', configStr);
        return config;
      } else {
        console.log('live2d_config键中也没有找到配置');
      }
    } catch (e) {
      console.error('解析Live2D配置时出错:', e);
    }
    console.log('未找到任何Live2D配置，将使用默认配置');
    return {};
  }

  // 创建并注入Live2D脚本
  function injectLive2DScript() {
    console.log('开始检查是否需要注入Live2D模型');

    // 如果不是来自我们的应用，则不注入
    if (!isFromOurApp()) {
      console.log('页面不是从我们的应用跳转而来，不注入Live2D模型');
      return;
    }

    console.log('页面来自我们的应用，准备注入Live2D模型');

    // 获取Live2D配置
    const config = getLive2DConfig() || {};
    console.log('最终使用的Live2D配置:', config);

    // 检查是否在起点中文网
    const isQidian = window.location.hostname.includes('qidian.com');
    console.log('是否在起点中文网:', isQidian);

    // 创建script元素
    const script = document.createElement('script');

    // 设置script的src，添加参数
    // 对于起点中文网，使用完整的URL
    let scriptSrc = '';
    // 获取当前应用的实际域名，而不是硬编码
    const appDomain = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
      ? window.location.hostname + ':' + (window.location.port || '5666')
      : window.location.host; // 应用域名，自动适应当前环境

    // 根据当前页面的协议选择相同的协议，避免混合内容问题
    const protocol = window.location.protocol;

    if (isQidian) {
      // 起点中文网需要使用完整URL
      scriptSrc = `${protocol}//${appDomain}/live2d-external.js`;
      console.log('检测到起点中文网，使用完整URL:', scriptSrc);
    } else if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      // 开发环境可能需要完整的URL
      const port = window.location.port || '80';
      scriptSrc = `${protocol}//${window.location.hostname}:${port}/live2d-external.js`;
      console.log('检测到开发环境，使用完整URL:', scriptSrc);
    } else {
      // 其他情况使用相对路径
      scriptSrc = '/live2d-external.js';
      console.log('使用相对路径:', scriptSrc);
    }

    // 添加模型ID参数
    const modelId = getUrlParam('modelId') || config.modelId || 'haru';
    scriptSrc += `?modelId=${encodeURIComponent(modelId)}`;
    console.log('使用模型ID:', modelId);

    // 添加其他配置参数
    if (config.width) scriptSrc += `&width=${config.width}`;
    if (config.height) scriptSrc += `&height=${config.height}`;
    if (config.right) scriptSrc += `&right=${config.right}`;
    if (config.bottom) scriptSrc += `&bottom=${config.bottom}`;
    if (config.zIndex) scriptSrc += `&zIndex=${config.zIndex}`;
    if (config.showToolbar !== undefined) scriptSrc += `&showToolbar=${config.showToolbar}`;

    script.src = scriptSrc;
    script.async = true;
    script.crossOrigin = 'anonymous'; // 添加跨域支持

    console.log('最终脚本URL:', scriptSrc);

    // 添加脚本加载事件监听
    script.onload = function() {
      console.log('Live2D外部脚本加载成功');
    };

    script.onerror = function(error) {
      console.error('Live2D外部脚本加载失败:', error);

      // 如果加载失败，尝试使用备用方法
      if (isQidian) {
        console.log('在起点中文网加载失败，尝试使用备用方法');
        tryAlternativeMethod();
      }
    };

    // 将script添加到页面
    document.body.appendChild(script);

    console.log('Live2D模型注入脚本已添加到页面');
  }

  // 备用方法：直接在页面中创建Live2D模型
  function tryAlternativeMethod() {
    console.log('使用备用方法创建Live2D模型');

    // 获取Live2D配置
    const config = getLive2DConfig() || {};

    // 创建一个div容器
    const container = document.createElement('div');
    container.id = 'live2d-container';
    Object.assign(container.style, {
      position: 'fixed',
      right: config.right ? config.right + 'px' : '0',
      bottom: config.bottom ? config.bottom + 'px' : '0',
      width: config.width ? config.width + 'px' : '280px',
      height: config.height ? config.height + 'px' : '300px',
      zIndex: config.zIndex || '1000',
      pointerEvents: 'none'
    });

    // 添加一个消息框
    const messageBox = document.createElement('div');
    messageBox.id = 'live2d-message';
    Object.assign(messageBox.style, {
      position: 'absolute',
      top: '10px',
      left: '50%',
      transform: 'translateX(-50%)',
      padding: '10px',
      background: 'rgba(255, 255, 255, 0.8)',
      borderRadius: '5px',
      boxShadow: '0 0 5px rgba(0, 0, 0, 0.2)',
      maxWidth: '80%',
      display: 'none',
      zIndex: '1001'
    });

    container.appendChild(messageBox);
    document.body.appendChild(container);

    // 显示一条消息
    const showMessage = function(text, timeout) {
      messageBox.textContent = text;
      messageBox.style.display = 'block';

      setTimeout(() => {
        messageBox.style.display = 'none';
      }, timeout || 3000);
    };

    // 检查是否在起点中文网
    const isQidian = window.location.hostname.includes('qidian.com');

    // 显示欢迎消息
    if (config.bookName && config.chapterName) {
      setTimeout(() => {
        showMessage(`正在阅读《${config.bookName}》${config.chapterName}`, 5000);
      }, 1000);
    } else if (isQidian) {
      // 在起点中文网显示特定消息
      const bookTitle = document.querySelector('.book-info-title') ?
                       document.querySelector('.book-info-title').textContent.trim() : '';
      const chapterTitle = document.querySelector('.j_chapterName') ?
                          document.querySelector('.j_chapterName').textContent.trim() : '';

      if (bookTitle && chapterTitle) {
        setTimeout(() => {
          showMessage(`正在阅读《${bookTitle}》${chapterTitle}`, 5000);
        }, 1000);
      } else {
        showMessage('Live2D模型加载失败，但我们仍然在这里陪你阅读！', 5000);
      }
    } else {
      showMessage('Live2D模型加载失败，但我们仍然在这里！', 5000);
    }

    console.log('备用方法创建完成');
  }

  // 当页面加载完成后注入Live2D脚本
  if (document.readyState === 'complete') {
    injectLive2DScript();
  } else {
    window.addEventListener('load', injectLive2DScript);
  }
})();
