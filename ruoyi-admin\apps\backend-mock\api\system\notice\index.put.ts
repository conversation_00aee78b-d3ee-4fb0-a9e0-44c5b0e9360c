import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { updateNotice } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);

  // 更新通知
  const success = updateNotice(body.noticeId, {
    noticeTitle: body.noticeTitle,
    noticeType: body.noticeType,
    noticeContent: body.noticeContent,
    status: body.status,
    remark: body.remark,
  });

  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: '通知不存在',
    });
  }

  return useResponseSuccess(null);
});
