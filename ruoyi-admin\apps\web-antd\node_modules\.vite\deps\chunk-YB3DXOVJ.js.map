{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/ui/src/dom.js", "../../../../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/util.js", "../../../../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/columnInfo.js", "../../../../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/table/src/cell.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nconst reClsMap = {};\nexport const browse = XEUtils.browse();\nlet tpImgEl;\nexport function initTpImg() {\n    if (!tpImgEl) {\n        tpImgEl = new Image();\n        tpImgEl.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';\n    }\n    return tpImgEl;\n}\nexport function getTpImg() {\n    if (!tpImgEl) {\n        return initTpImg();\n    }\n    return tpImgEl;\n}\nexport function getPropClass(property, params) {\n    return property ? XEUtils.isFunction(property) ? property(params) : property : '';\n}\nfunction getClsRE(cls) {\n    if (!reClsMap[cls]) {\n        reClsMap[cls] = new RegExp(`(?:^|\\\\s)${cls}(?!\\\\S)`, 'g');\n    }\n    return reClsMap[cls];\n}\nfunction getNodeOffset(elem, container, rest) {\n    if (elem) {\n        const parentElem = elem.parentNode;\n        rest.top += elem.offsetTop;\n        rest.left += elem.offsetLeft;\n        if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {\n            rest.top -= parentElem.scrollTop;\n            rest.left -= parentElem.scrollLeft;\n        }\n        if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {\n            return getNodeOffset(elem.offsetParent, container, rest);\n        }\n    }\n    return rest;\n}\nexport function isPx(val) {\n    return val && /^\\d+(px)?$/.test(val);\n}\nexport function isScale(val) {\n    return val && /^\\d+%$/.test(val);\n}\nexport function hasClass(elem, cls) {\n    return elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls));\n}\nexport function removeClass(elem, cls) {\n    if (elem && hasClass(elem, cls)) {\n        elem.className = elem.className.replace(getClsRE(cls), '');\n    }\n}\nexport function addClass(elem, cls) {\n    if (elem && !hasClass(elem, cls)) {\n        removeClass(elem, cls);\n        elem.className = `${elem.className} ${cls}`;\n    }\n}\nexport function getDomNode() {\n    const documentElement = document.documentElement;\n    const bodyElem = document.body;\n    return {\n        scrollTop: documentElement.scrollTop || bodyElem.scrollTop,\n        scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,\n        visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,\n        visibleWidth: documentElement.clientWidth || bodyElem.clientWidth\n    };\n}\nexport function getOffsetHeight(elem) {\n    return elem ? elem.offsetHeight : 0;\n}\nexport function getPaddingTopBottomSize(elem) {\n    if (elem) {\n        const computedStyle = getComputedStyle(elem);\n        const paddingTop = XEUtils.toNumber(computedStyle.paddingTop);\n        const paddingBottom = XEUtils.toNumber(computedStyle.paddingBottom);\n        return paddingTop + paddingBottom;\n    }\n    return 0;\n}\nexport function setScrollTop(elem, scrollTop) {\n    if (elem) {\n        elem.scrollTop = scrollTop;\n    }\n}\nexport function setScrollLeft(elem, scrollLeft) {\n    if (elem) {\n        elem.scrollLeft = scrollLeft;\n    }\n}\n// export function setScrollLeftAndTop (elem: HTMLElement | null, scrollLeft: number, scrollTop: number) {\n//   if (elem) {\n//     elem.scrollLeft = scrollLeft\n//     elem.scrollTop = scrollTop\n//   }\n// }\nexport function updateCellTitle(overflowElem, column) {\n    const content = column.type === 'html' ? overflowElem.innerText : overflowElem.textContent;\n    if (overflowElem.getAttribute('title') !== content) {\n        overflowElem.setAttribute('title', content);\n    }\n}\n/**\n * 检查触发源是否属于目标节点\n */\nexport function getEventTargetNode(evnt, container, queryCls, queryMethod) {\n    let targetElem;\n    let target = (evnt.target.shadowRoot && evnt.composed) ? (evnt.composedPath()[0] || evnt.target) : evnt.target;\n    while (target && target.nodeType && target !== document) {\n        if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {\n            targetElem = target;\n        }\n        else if (target === container) {\n            return { flag: queryCls ? !!targetElem : true, container, targetElem: targetElem };\n        }\n        target = target.parentNode;\n    }\n    return { flag: false };\n}\n/**\n * 获取元素相对于 document 的位置\n */\nexport function getOffsetPos(elem, container) {\n    return getNodeOffset(elem, container, { left: 0, top: 0 });\n}\nexport function getAbsolutePos(elem) {\n    const bounding = elem.getBoundingClientRect();\n    const boundingTop = bounding.top;\n    const boundingLeft = bounding.left;\n    const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();\n    return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };\n}\nconst scrollIntoViewIfNeeded = 'scrollIntoViewIfNeeded';\nconst scrollIntoView = 'scrollIntoView';\nexport function scrollToView(elem) {\n    if (elem) {\n        if (elem[scrollIntoViewIfNeeded]) {\n            elem[scrollIntoViewIfNeeded]();\n        }\n        else if (elem[scrollIntoView]) {\n            elem[scrollIntoView]();\n        }\n    }\n}\nexport function triggerEvent(targetElem, type) {\n    if (targetElem) {\n        targetElem.dispatchEvent(new Event(type));\n    }\n}\nexport function isNodeElement(elem) {\n    return elem && elem.nodeType === 1;\n}\n", "import { watch, reactive } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { ColumnInfo } from './columnInfo';\nimport { isPx, isScale } from '../../ui/src/dom';\nconst getAllConvertColumns = (columns, parentColumn) => {\n    const result = [];\n    columns.forEach((column) => {\n        column.parentId = parentColumn ? parentColumn.id : null;\n        if (column.visible) {\n            if (column.children && column.children.length && column.children.some((column) => column.visible)) {\n                result.push(column);\n                result.push(...getAllConvertColumns(column.children, column));\n            }\n            else {\n                result.push(column);\n            }\n        }\n    });\n    return result;\n};\nexport const convertHeaderColumnToRows = (originColumns) => {\n    let maxLevel = 1;\n    const traverse = (column, parent) => {\n        if (parent) {\n            column.level = parent.level + 1;\n            if (maxLevel < column.level) {\n                maxLevel = column.level;\n            }\n        }\n        if (column.children && column.children.length && column.children.some((column) => column.visible)) {\n            let colSpan = 0;\n            column.children.forEach((subColumn) => {\n                if (subColumn.visible) {\n                    traverse(subColumn, column);\n                    colSpan += subColumn.colSpan;\n                }\n            });\n            column.colSpan = colSpan;\n        }\n        else {\n            column.colSpan = 1;\n        }\n    };\n    originColumns.forEach((column) => {\n        column.level = 1;\n        traverse(column);\n    });\n    const rows = [];\n    for (let i = 0; i < maxLevel; i++) {\n        rows.push([]);\n    }\n    const allColumns = getAllConvertColumns(originColumns);\n    allColumns.forEach((column) => {\n        if (column.children && column.children.length && column.children.some((column) => column.visible)) {\n            column.rowSpan = 1;\n        }\n        else {\n            column.rowSpan = maxLevel - column.level + 1;\n        }\n        rows[column.level - 1].push(column);\n    });\n    return rows;\n};\nexport function restoreScrollLocation($xeTable, scrollLeft, scrollTop) {\n    const internalData = $xeTable.internalData;\n    return $xeTable.clearScroll().then(() => {\n        if (scrollLeft || scrollTop) {\n            // 重置最后滚动状态\n            internalData.lastScrollLeft = 0;\n            internalData.lastScrollTop = 0;\n            internalData.inVirtualScroll = false;\n            internalData.inBodyScroll = false;\n            internalData.inFooterScroll = false;\n            internalData.bodyScrollType = '';\n            // 还原滚动状态\n            return $xeTable.scrollTo(scrollLeft, scrollTop);\n        }\n    });\n}\n/**\n * 生成行的唯一主键\n */\nexport function getRowUniqueId() {\n    return XEUtils.uniqueId('row_');\n}\n// 行主键 key\nexport function getRowkey($xeTable) {\n    const { props } = $xeTable;\n    const { computeRowOpts } = $xeTable.getComputeMaps();\n    const { rowId } = props;\n    const rowOpts = computeRowOpts.value;\n    return rowId || rowOpts.keyField || '_X_ROW_KEY';\n}\n// 行主键 value\nexport function getRowid($xeTable, row) {\n    const rowid = XEUtils.get(row, getRowkey($xeTable));\n    return XEUtils.eqNull(rowid) ? '' : encodeURIComponent(rowid);\n}\nexport const handleFieldOrColumn = ($xeTable, fieldOrColumn) => {\n    if (fieldOrColumn) {\n        return XEUtils.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;\n    }\n    return null;\n};\nfunction getPaddingLeftRightSize(elem) {\n    if (elem) {\n        const computedStyle = getComputedStyle(elem);\n        const paddingLeft = XEUtils.toNumber(computedStyle.paddingLeft);\n        const paddingRight = XEUtils.toNumber(computedStyle.paddingRight);\n        return paddingLeft + paddingRight;\n    }\n    return 0;\n}\nfunction getElementMarginWidth(elem) {\n    if (elem) {\n        const computedStyle = getComputedStyle(elem);\n        const marginLeft = XEUtils.toNumber(computedStyle.marginLeft);\n        const marginRight = XEUtils.toNumber(computedStyle.marginRight);\n        return elem.offsetWidth + marginLeft + marginRight;\n    }\n    return 0;\n}\nfunction queryCellElement(cell, selector) {\n    return cell.querySelector('.vxe-cell' + selector);\n}\nexport function toFilters(filters) {\n    if (filters && XEUtils.isArray(filters)) {\n        return filters.map(({ label, value, data, resetValue, checked }) => {\n            return { label, value, data, resetValue, checked: !!checked, _checked: !!checked };\n        });\n    }\n    return filters;\n}\nexport function toTreePathSeq(path) {\n    return path.map((num, i) => i % 2 === 0 ? (Number(num) + 1) : '.').join('');\n}\nexport function getCellValue(row, column) {\n    return XEUtils.get(row, column.field);\n}\nexport function setCellValue(row, column, value) {\n    return XEUtils.set(row, column.field, value);\n}\nexport function getRefElem(refEl) {\n    if (refEl) {\n        const rest = refEl.value;\n        if (rest) {\n            return (rest.$el || rest);\n        }\n    }\n    return null;\n}\n/**\n * 列宽拖动最大宽度\n * @param params\n * @returns\n */\nexport function getColReMaxWidth(params) {\n    const { $table } = params;\n    const { computeResizableOpts } = $table.getComputeMaps();\n    const resizableOpts = computeResizableOpts.value;\n    const { maxWidth: reMaxWidth } = resizableOpts;\n    // 如果自定义调整宽度逻辑\n    if (reMaxWidth) {\n        const customMaxWidth = XEUtils.isFunction(reMaxWidth) ? reMaxWidth(params) : reMaxWidth;\n        if (customMaxWidth !== 'auto') {\n            return Math.max(1, XEUtils.toNumber(customMaxWidth));\n        }\n    }\n    return -1;\n}\n/**\n * 列宽拖动最小宽度\n * @param params\n * @returns\n */\nexport function getColReMinWidth(params) {\n    const { $table, column, cell } = params;\n    const { props: tableProps } = $table;\n    const { computeResizableOpts } = $table.getComputeMaps();\n    const resizableOpts = computeResizableOpts.value;\n    const { minWidth: reMinWidth } = resizableOpts;\n    // 如果自定义调整宽度逻辑\n    if (reMinWidth) {\n        const customMinWidth = XEUtils.isFunction(reMinWidth) ? reMinWidth(params) : reMinWidth;\n        if (customMinWidth !== 'auto') {\n            return Math.max(1, XEUtils.toNumber(customMinWidth));\n        }\n    }\n    const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;\n    const { showHeaderOverflow, minWidth: colMinWidth } = column;\n    const headOverflow = XEUtils.isUndefined(showHeaderOverflow) || XEUtils.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;\n    const showEllipsis = headOverflow === 'ellipsis';\n    const showTitle = headOverflow === 'title';\n    const showTooltip = headOverflow === true || headOverflow === 'tooltip';\n    const hasEllipsis = showTitle || showTooltip || showEllipsis;\n    const minTitleWidth = XEUtils.floor((XEUtils.toNumber(getComputedStyle(cell).fontSize) || 14) * 1.6);\n    const paddingLeftRight = getPaddingLeftRightSize(cell) + getPaddingLeftRightSize(queryCellElement(cell, ''));\n    let mWidth = minTitleWidth + paddingLeftRight;\n    // 默认最小宽处理\n    if (hasEllipsis) {\n        const dragIconWidth = getPaddingLeftRightSize(queryCellElement(cell, '>.vxe-cell--drag-handle'));\n        const checkboxIconWidth = getPaddingLeftRightSize(queryCellElement(cell, '>.vxe-cell--checkbox'));\n        const requiredIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--required-icon'));\n        const editIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--edit-icon'));\n        const prefixIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell-title-prefix-icon'));\n        const suffixIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell-title-suffix-icon'));\n        const sortIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--sort'));\n        const filterIconWidth = getElementMarginWidth(queryCellElement(cell, '>.vxe-cell--filter'));\n        mWidth += dragIconWidth + checkboxIconWidth + requiredIconWidth + editIconWidth + prefixIconWidth + suffixIconWidth + filterIconWidth + sortIconWidth;\n    }\n    // 如果设置最小宽\n    if (colMinWidth) {\n        const { refTableBody } = $table.getRefMaps();\n        const tableBody = refTableBody.value;\n        const bodyElem = tableBody ? tableBody.$el : null;\n        if (bodyElem) {\n            if (isScale(colMinWidth)) {\n                const bodyWidth = bodyElem.clientWidth - 1;\n                const meanWidth = bodyWidth / 100;\n                return Math.max(mWidth, Math.floor(XEUtils.toInteger(colMinWidth) * meanWidth));\n            }\n            else if (isPx(colMinWidth)) {\n                return Math.max(mWidth, XEUtils.toInteger(colMinWidth));\n            }\n        }\n    }\n    return mWidth;\n}\nexport function isColumnInfo(column) {\n    return column && (column.constructor === ColumnInfo || column instanceof ColumnInfo);\n}\nexport function createColumn($xeTable, options, renderOptions) {\n    return isColumnInfo(options) ? options : reactive(new ColumnInfo($xeTable, options, renderOptions));\n}\nexport function watchColumn($xeTable, props, column) {\n    Object.keys(props).forEach(name => {\n        watch(() => props[name], (value) => {\n            column.update(name, value);\n            if ($xeTable) {\n                if (name === 'filters') {\n                    $xeTable.setFilter(column, value);\n                    $xeTable.handleUpdateDataQueue();\n                }\n                else if (['visible', 'fixed', 'width', 'minWidth', 'maxWidth'].includes(name)) {\n                    $xeTable.handleRefreshColumnQueue();\n                }\n            }\n        });\n    });\n}\nexport function assembleColumn($xeTable, elem, column, colgroup) {\n    const { reactData } = $xeTable;\n    const { staticColumns } = reactData;\n    const parentElem = elem.parentNode;\n    const parentColumn = colgroup ? colgroup.columnConfig : null;\n    const parentCols = parentColumn ? parentColumn.children : staticColumns;\n    if (parentElem && parentCols) {\n        parentCols.splice(XEUtils.arrayIndexOf(parentElem.children, elem), 0, column);\n        reactData.staticColumns = staticColumns.slice(0);\n    }\n}\nexport function destroyColumn($xeTable, column) {\n    const { reactData } = $xeTable;\n    const { staticColumns } = reactData;\n    const matchObj = XEUtils.findTree(staticColumns, item => item.id === column.id, { children: 'children' });\n    if (matchObj) {\n        matchObj.items.splice(matchObj.index, 1);\n    }\n    reactData.staticColumns = staticColumns.slice(0);\n}\nexport function getRootColumn($xeTable, column) {\n    const { internalData } = $xeTable;\n    const { fullColumnIdData } = internalData;\n    if (!column) {\n        return null;\n    }\n    let parentColId = column.parentId;\n    while (fullColumnIdData[parentColId]) {\n        const column = fullColumnIdData[parentColId].column;\n        parentColId = column.parentId;\n        if (!parentColId) {\n            return column;\n        }\n    }\n    return column;\n}\nexport function mergeBodyMethod(mergeList, _rowIndex, _columnIndex) {\n    for (let mIndex = 0; mIndex < mergeList.length; mIndex++) {\n        const { row: mergeRowIndex, col: mergeColIndex, rowspan: mergeRowspan, colspan: mergeColspan } = mergeList[mIndex];\n        if (mergeColIndex > -1 && mergeRowIndex > -1 && mergeRowspan && mergeColspan) {\n            if (mergeRowIndex === _rowIndex && mergeColIndex === _columnIndex) {\n                return { rowspan: mergeRowspan, colspan: mergeColspan };\n            }\n            if (_rowIndex >= mergeRowIndex && _rowIndex < mergeRowIndex + mergeRowspan && _columnIndex >= mergeColIndex && _columnIndex < mergeColIndex + mergeColspan) {\n                return { rowspan: 0, colspan: 0 };\n            }\n        }\n    }\n}\nexport function clearTableDefaultStatus($xeTable) {\n    const { props, internalData } = $xeTable;\n    internalData.initStatus = false;\n    $xeTable.clearSort();\n    $xeTable.clearCurrentRow();\n    $xeTable.clearCurrentColumn();\n    $xeTable.clearRadioRow();\n    $xeTable.clearRadioReserve();\n    $xeTable.clearCheckboxRow();\n    $xeTable.clearCheckboxReserve();\n    $xeTable.clearRowExpand();\n    $xeTable.clearTreeExpand();\n    $xeTable.clearTreeExpandReserve();\n    $xeTable.clearPendingRow();\n    if ($xeTable.clearFilter) {\n        $xeTable.clearFilter();\n    }\n    if ($xeTable.clearSelected && (props.keyboardConfig || props.mouseConfig)) {\n        $xeTable.clearSelected();\n    }\n    if ($xeTable.clearCellAreas && props.mouseConfig) {\n        $xeTable.clearCellAreas();\n        $xeTable.clearCopyCellArea();\n    }\n    return $xeTable.clearScroll();\n}\nexport function clearTableAllStatus($xeTable) {\n    if ($xeTable.clearFilter) {\n        $xeTable.clearFilter();\n    }\n    return clearTableDefaultStatus($xeTable);\n}\nexport function rowToVisible($xeTable, row) {\n    const { reactData, internalData } = $xeTable;\n    const tableProps = $xeTable.props;\n    const { showOverflow } = tableProps;\n    const { refTableBody } = $xeTable.getRefMaps();\n    const { columnStore, scrollYLoad } = reactData;\n    const { afterFullData, scrollYStore, fullAllDataRowIdData } = internalData;\n    const tableBody = refTableBody.value;\n    const { leftList, rightList } = columnStore;\n    const bodyElem = tableBody ? tableBody.$el : null;\n    const rowid = getRowid($xeTable, row);\n    let offsetFixedLeft = 0;\n    leftList.forEach(item => {\n        offsetFixedLeft += item.renderWidth;\n    });\n    let offsetFixedRight = 0;\n    rightList.forEach(item => {\n        offsetFixedRight += item.renderWidth;\n    });\n    if (bodyElem) {\n        const bodyHeight = bodyElem.clientHeight;\n        const bodyScrollTop = bodyElem.scrollTop;\n        const trElem = bodyElem.querySelector(`[rowid=\"${rowid}\"]`);\n        if (trElem) {\n            const trOffsetParent = trElem.offsetParent;\n            const trOffsetTop = trElem.offsetTop + (trOffsetParent ? trOffsetParent.offsetTop : 0);\n            const trHeight = trElem.clientHeight;\n            // 检测行是否在可视区中\n            if (trOffsetTop < bodyScrollTop || trOffsetTop > bodyScrollTop + bodyHeight) {\n                return $xeTable.scrollTo(null, trOffsetTop);\n            }\n            else if (trOffsetTop + trHeight >= bodyHeight + bodyScrollTop) {\n                return $xeTable.scrollTo(null, bodyScrollTop + trHeight);\n            }\n        }\n        else {\n            // 如果是虚拟渲染滚动\n            if (scrollYLoad) {\n                if (showOverflow) {\n                    return $xeTable.scrollTo(null, ($xeTable.findRowIndexOf(afterFullData, row) - 1) * scrollYStore.rowHeight);\n                }\n                let scrollTop = 0;\n                const rest = fullAllDataRowIdData[rowid];\n                const rHeight = rest ? rest.height : 0;\n                for (let i = 0; i < afterFullData.length; i++) {\n                    const currRow = afterFullData[i];\n                    const currRowid = getRowid($xeTable, currRow);\n                    if (currRow === row || currRowid === rowid) {\n                        break;\n                    }\n                    const rest = fullAllDataRowIdData[currRowid];\n                    scrollTop += rest ? rest.height : 0;\n                }\n                if (scrollTop < bodyScrollTop) {\n                    return $xeTable.scrollTo(null, scrollTop - offsetFixedLeft - 1);\n                }\n                return $xeTable.scrollTo(null, (scrollTop + rHeight) - (bodyHeight - offsetFixedRight - 1));\n            }\n        }\n    }\n    return Promise.resolve();\n}\nexport function colToVisible($xeTable, column, row) {\n    const { reactData, internalData } = $xeTable;\n    const { refTableBody } = $xeTable.getRefMaps();\n    const { columnStore, scrollXLoad } = reactData;\n    const { visibleColumn } = internalData;\n    const { leftList, rightList } = columnStore;\n    const tableBody = refTableBody.value;\n    const bodyElem = tableBody ? tableBody.$el : null;\n    if (column.fixed) {\n        return Promise.resolve();\n    }\n    let offsetFixedLeft = 0;\n    leftList.forEach(item => {\n        offsetFixedLeft += item.renderWidth;\n    });\n    let offsetFixedRight = 0;\n    rightList.forEach(item => {\n        offsetFixedRight += item.renderWidth;\n    });\n    if (bodyElem) {\n        const bodyWidth = bodyElem.clientWidth;\n        const bodyScrollLeft = bodyElem.scrollLeft;\n        let tdElem = null;\n        if (row) {\n            const rowid = getRowid($xeTable, row);\n            tdElem = bodyElem.querySelector(`[rowid=\"${rowid}\"] .${column.id}`);\n        }\n        if (!tdElem) {\n            tdElem = bodyElem.querySelector(`.${column.id}`);\n        }\n        if (tdElem) {\n            const tdOffsetParent = tdElem.offsetParent;\n            const tdOffsetLeft = tdElem.offsetLeft + (tdOffsetParent ? tdOffsetParent.offsetLeft : 0);\n            const cellWidth = tdElem.clientWidth;\n            // 检测是否在可视区中\n            if (tdOffsetLeft < (bodyScrollLeft + offsetFixedLeft)) {\n                return $xeTable.scrollTo(tdOffsetLeft - offsetFixedLeft - 1);\n            }\n            else if ((tdOffsetLeft + cellWidth - bodyScrollLeft) > (bodyWidth - offsetFixedRight)) {\n                return $xeTable.scrollTo((tdOffsetLeft + cellWidth) - (bodyWidth - offsetFixedRight - 1));\n            }\n        }\n        else {\n            // 检测是否在虚拟渲染可视区中\n            if (scrollXLoad) {\n                let scrollLeft = 0;\n                const cellWidth = column.renderWidth;\n                for (let i = 0; i < visibleColumn.length; i++) {\n                    const currCol = visibleColumn[i];\n                    if (currCol === column || currCol.id === column.id) {\n                        break;\n                    }\n                    scrollLeft += currCol.renderWidth;\n                }\n                if (scrollLeft < bodyScrollLeft) {\n                    return $xeTable.scrollTo(scrollLeft - offsetFixedLeft - 1);\n                }\n                return $xeTable.scrollTo((scrollLeft + cellWidth) - (bodyWidth - offsetFixedRight - 1));\n            }\n        }\n    }\n    return Promise.resolve();\n}\n", "import XEUtils from 'xe-utils';\nimport { VxeUI } from '../../ui';\nimport { toFilters } from './util';\nimport { getFuncText } from '../../ui/src/utils';\nimport { warnLog, errLog } from '../../ui/src/log';\nconst { getI18n, formats } = VxeUI;\nexport class ColumnInfo {\n    /* eslint-disable @typescript-eslint/no-use-before-define */\n    constructor($xeTable, _vm, { renderHeader, renderCell, renderFooter, renderData } = {}) {\n        const $xeGrid = $xeTable.xegrid;\n        const formatter = _vm.formatter;\n        const visible = XEUtils.isBoolean(_vm.visible) ? _vm.visible : true;\n        const { props: tableProps } = $xeTable;\n        if (process.env.NODE_ENV === 'development') {\n            const types = ['seq', 'checkbox', 'radio', 'expand', 'html'];\n            if (_vm.type && types.indexOf(_vm.type) === -1) {\n                warnLog('vxe.error.errProp', [`type=${_vm.type}`, types.join(', ')]);\n            }\n            if (XEUtils.isBoolean(_vm.cellRender) || (_vm.cellRender && !XEUtils.isObject(_vm.cellRender))) {\n                warnLog('vxe.error.errProp', [`column.cell-render=${_vm.cellRender}`, 'column.cell-render={}']);\n            }\n            if (XEUtils.isBoolean(_vm.editRender) || (_vm.editRender && !XEUtils.isObject(_vm.editRender))) {\n                warnLog('vxe.error.errProp', [`column.edit-render=${_vm.editRender}`, 'column.edit-render={}']);\n            }\n            if (_vm.cellRender && _vm.editRender) {\n                warnLog('vxe.error.errConflicts', ['column.cell-render', 'column.edit-render']);\n            }\n            if (_vm.type === 'expand') {\n                const { treeConfig } = tableProps;\n                const { computeTreeOpts } = $xeTable.getComputeMaps();\n                const treeOpts = computeTreeOpts.value;\n                if (treeConfig && (treeOpts.showLine || treeOpts.line)) {\n                    errLog('vxe.error.errConflicts', ['tree-config.showLine', 'column.type=expand']);\n                }\n            }\n            if (formatter) {\n                if (XEUtils.isString(formatter)) {\n                    const gFormatOpts = formats.get(formatter) || XEUtils[formatter];\n                    if (!gFormatOpts || !XEUtils.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {\n                        errLog('vxe.error.notFormats', [formatter]);\n                    }\n                }\n                else if (XEUtils.isArray(formatter)) {\n                    const gFormatOpts = formats.get(formatter[0]) || XEUtils[formatter[0]];\n                    if (!gFormatOpts || !XEUtils.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {\n                        errLog('vxe.error.notFormats', [formatter[0]]);\n                    }\n                }\n            }\n        }\n        Object.assign(this, {\n            // 基本属性\n            type: _vm.type,\n            property: _vm.field,\n            field: _vm.field,\n            title: _vm.title,\n            width: _vm.width,\n            minWidth: _vm.minWidth,\n            maxWidth: _vm.maxWidth,\n            resizable: _vm.resizable,\n            fixed: _vm.fixed,\n            align: _vm.align,\n            headerAlign: _vm.headerAlign,\n            footerAlign: _vm.footerAlign,\n            showOverflow: _vm.showOverflow,\n            showHeaderOverflow: _vm.showHeaderOverflow,\n            showFooterOverflow: _vm.showFooterOverflow,\n            className: _vm.className,\n            headerClassName: _vm.headerClassName,\n            footerClassName: _vm.footerClassName,\n            formatter: formatter,\n            footerFormatter: _vm.footerFormatter,\n            sortable: _vm.sortable,\n            sortBy: _vm.sortBy,\n            sortType: _vm.sortType,\n            filters: toFilters(_vm.filters),\n            filterMultiple: XEUtils.isBoolean(_vm.filterMultiple) ? _vm.filterMultiple : true,\n            filterMethod: _vm.filterMethod,\n            filterResetMethod: _vm.filterResetMethod,\n            filterRecoverMethod: _vm.filterRecoverMethod,\n            filterRender: _vm.filterRender,\n            treeNode: _vm.treeNode,\n            dragSort: _vm.dragSort,\n            cellType: _vm.cellType,\n            cellRender: _vm.cellRender,\n            editRender: _vm.editRender,\n            contentRender: _vm.contentRender,\n            headerExportMethod: _vm.headerExportMethod,\n            exportMethod: _vm.exportMethod,\n            footerExportMethod: _vm.footerExportMethod,\n            titleHelp: _vm.titleHelp,\n            titlePrefix: _vm.titlePrefix,\n            titleSuffix: _vm.titleSuffix,\n            // 自定义参数\n            params: _vm.params,\n            // 渲染属性\n            id: _vm.colId || XEUtils.uniqueId('col_'),\n            parentId: null,\n            visible,\n            // 内部属性（一旦被使用，将导致不可升级版本）\n            halfVisible: false,\n            defaultVisible: visible,\n            defaultFixed: _vm.fixed,\n            checked: false,\n            halfChecked: false,\n            disabled: false,\n            // 分组层级\n            level: 1,\n            // 跨行\n            rowSpan: 1,\n            // 跨列\n            colSpan: 1,\n            // 数据排序\n            order: null,\n            sortTime: 0,\n            // 列排序\n            sortNumber: 0,\n            renderSortNumber: 0,\n            renderFixed: '',\n            renderVisible: false,\n            renderWidth: 0,\n            renderHeight: 0,\n            renderResizeWidth: 0,\n            renderAutoWidth: 0,\n            resizeWidth: 0,\n            renderLeft: 0,\n            renderArgs: [],\n            model: {},\n            renderHeader: renderHeader || _vm.renderHeader,\n            renderCell: renderCell || _vm.renderCell,\n            renderFooter: renderFooter || _vm.renderFooter,\n            renderData: renderData,\n            // 单元格插槽，只对 grid 有效\n            slots: _vm.slots\n        });\n        if ($xeGrid) {\n            const { computeProxyOpts } = $xeGrid.getComputeMaps();\n            const proxyOpts = computeProxyOpts.value;\n            if (proxyOpts.beforeColumn) {\n                proxyOpts.beforeColumn({ $grid: $xeGrid, column: this });\n            }\n        }\n    }\n    getTitle() {\n        return getFuncText(this.title || (this.type === 'seq' ? getI18n('vxe.table.seqTitle') : ''));\n    }\n    getKey() {\n        const { type } = this;\n        return this.field || (type ? `type=${type}` : null);\n    }\n    update(name, value) {\n        // 不支持直接修改的属性\n        if (name !== 'filters') {\n            if (name === 'field') {\n                // 兼容旧属性\n                this.property = value;\n            }\n            this[name] = value;\n        }\n    }\n}\n", "import { h } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { VxeUI } from '../../ui';\nimport { getFuncText, isEnableConf, formatText, eqEmptyValue } from '../../ui/src/utils';\nimport { updateCellTitle } from '../../ui/src/dom';\nimport { createColumn, getRowid } from './util';\nimport { getSlotVNs } from '../../ui/src/vn';\nconst { getI18n, getIcon, renderer, formats, renderEmptyElement } = VxeUI;\nfunction renderTitlePrefixIcon(params) {\n    const { $table, column } = params;\n    const titlePrefix = column.titlePrefix || column.titleHelp;\n    if (titlePrefix) {\n        return h('i', {\n            class: ['vxe-cell-title-prefix-icon', titlePrefix.icon || getIcon().TABLE_TITLE_PREFIX],\n            onMouseenter(evnt) {\n                $table.triggerHeaderTitleEvent(evnt, titlePrefix, params);\n            },\n            onMouseleave(evnt) {\n                $table.handleTargetLeaveEvent(evnt);\n            }\n        });\n    }\n    return renderEmptyElement($table);\n}\nfunction renderTitleSuffixIcon(params) {\n    const { $table, column } = params;\n    const titleSuffix = column.titleSuffix;\n    if (titleSuffix) {\n        return h('i', {\n            class: ['vxe-cell-title-suffix-icon', titleSuffix.icon || getIcon().TABLE_TITLE_SUFFIX],\n            onMouseenter(evnt) {\n                $table.triggerHeaderTitleEvent(evnt, titleSuffix, params);\n            },\n            onMouseleave(evnt) {\n                $table.handleTargetLeaveEvent(evnt);\n            }\n        });\n    }\n    return renderEmptyElement($table);\n}\nfunction renderCellDragIcon(params) {\n    const { $table } = params;\n    const tableProps = $table.props;\n    const { dragConfig } = tableProps;\n    const { computeRowDragOpts } = $table.getComputeMaps();\n    const rowDragOpts = computeRowDragOpts.value;\n    const { icon, trigger, disabledMethod } = rowDragOpts;\n    const rDisabledMethod = disabledMethod || (dragConfig ? dragConfig.rowDisabledMethod : null);\n    const isDisabled = rDisabledMethod && rDisabledMethod(params);\n    const ons = {};\n    if (trigger !== 'cell') {\n        ons.onMousedown = (evnt) => {\n            if (!isDisabled) {\n                $table.handleCellDragMousedownEvent(evnt, params);\n            }\n        };\n        ons.onMouseup = $table.handleCellDragMouseupEvent;\n    }\n    return h('span', Object.assign({ key: 'dg', class: ['vxe-cell--drag-handle', {\n                'is--disabled': isDisabled\n            }] }, ons), [\n        h('i', {\n            class: icon || (dragConfig ? dragConfig.rowIcon : '') || getIcon().TABLE_DRAG_ROW\n        })\n    ]);\n}\nfunction renderCellBaseVNs(params, content) {\n    const { $table, column, level } = params;\n    const { dragSort } = column;\n    const tableProps = $table.props;\n    const { treeConfig, dragConfig } = tableProps;\n    const { computeRowOpts, computeRowDragOpts } = $table.getComputeMaps();\n    const rowOpts = computeRowOpts.value;\n    const rowDragOpts = computeRowDragOpts.value;\n    const { showIcon, isPeerDrag, isCrossDrag, visibleMethod } = rowDragOpts;\n    const rVisibleMethod = visibleMethod || (dragConfig ? dragConfig.rowVisibleMethod : null);\n    const vns = XEUtils.isArray(content) ? content : [content];\n    if (dragSort && rowOpts.drag && ((showIcon || (dragConfig ? dragConfig.showRowIcon : false)) && (!rVisibleMethod || rVisibleMethod(params)))) {\n        if (treeConfig) {\n            if (isPeerDrag || isCrossDrag || !level) {\n                vns.unshift(renderCellDragIcon(params));\n            }\n        }\n        else {\n            vns.unshift(renderCellDragIcon(params));\n        }\n    }\n    return vns;\n}\nfunction renderHeaderCellDragIcon(params) {\n    const { $table, column } = params;\n    const { computeColumnOpts, computeColumnDragOpts } = $table.getComputeMaps();\n    const columnOpts = computeColumnOpts.value;\n    const columnDragOpts = computeColumnDragOpts.value;\n    const { showIcon, icon, trigger, isPeerDrag, isCrossDrag, visibleMethod, disabledMethod } = columnDragOpts;\n    if (columnOpts.drag && showIcon && (!visibleMethod || visibleMethod(params))) {\n        if (!column.fixed && (isPeerDrag || isCrossDrag || !column.parentId)) {\n            const isDisabled = disabledMethod && disabledMethod(params);\n            const ons = {};\n            if (trigger !== 'cell') {\n                ons.onMousedown = (evnt) => {\n                    if (!isDisabled) {\n                        $table.handleHeaderCellDragMousedownEvent(evnt, params);\n                    }\n                };\n                ons.onMouseup = $table.handleHeaderCellDragMouseupEvent;\n            }\n            return h('span', Object.assign({ key: 'dg', class: ['vxe-cell--drag-handle', {\n                        'is--disabled': isDisabled\n                    }] }, ons), [\n                h('i', {\n                    class: icon || getIcon().TABLE_DRAG_COLUMN\n                })\n            ]);\n        }\n    }\n    return renderEmptyElement($table);\n}\nfunction renderHeaderCellBaseVNs(params, content) {\n    const vns = [\n        renderTitlePrefixIcon(params),\n        renderHeaderCellDragIcon(params),\n        ...(XEUtils.isArray(content) ? content : [content]),\n        renderTitleSuffixIcon(params)\n    ];\n    return vns;\n}\nfunction renderTitleContent(params, content) {\n    const { $table, column } = params;\n    const tableProps = $table.props;\n    const tableReactData = $table.reactData;\n    const { computeTooltipOpts } = $table.getComputeMaps();\n    const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;\n    const { type, showHeaderOverflow } = column;\n    const tooltipOpts = computeTooltipOpts.value;\n    const showAllTip = tooltipOpts.showAll;\n    const headOverflow = XEUtils.isUndefined(showHeaderOverflow) || XEUtils.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;\n    const showTitle = headOverflow === 'title';\n    const showTooltip = headOverflow === true || headOverflow === 'tooltip';\n    const ons = {};\n    if (showTitle || showTooltip || showAllTip) {\n        ons.onMouseenter = (evnt) => {\n            if (tableReactData._isResize) {\n                return;\n            }\n            if (showTitle) {\n                updateCellTitle(evnt.currentTarget, column);\n            }\n            else if (showTooltip || showAllTip) {\n                $table.triggerHeaderTooltipEvent(evnt, params);\n            }\n        };\n    }\n    if (showTooltip || showAllTip) {\n        ons.onMouseleave = (evnt) => {\n            if (tableReactData._isResize) {\n                return;\n            }\n            if (showTooltip || showAllTip) {\n                $table.handleTargetLeaveEvent(evnt);\n            }\n        };\n    }\n    return [\n        type === 'html' && XEUtils.isString(content)\n            ? h('span', Object.assign({ class: 'vxe-cell--title', innerHTML: content }, ons))\n            : h('span', Object.assign({ class: 'vxe-cell--title' }, ons), getSlotVNs(content))\n    ];\n}\nfunction formatFooterLabel(footerFormatter, params) {\n    if (XEUtils.isFunction(footerFormatter)) {\n        return `${footerFormatter(params)}`;\n    }\n    const isArr = XEUtils.isArray(footerFormatter);\n    const gFormatOpts = isArr ? formats.get(footerFormatter[0]) : formats.get(footerFormatter);\n    const footerFormatMethod = gFormatOpts ? gFormatOpts.tableFooterCellFormatMethod : null;\n    if (footerFormatMethod) {\n        return `${isArr ? footerFormatMethod(params, ...footerFormatter.slice(1)) : footerFormatMethod(params)}`;\n    }\n    return '';\n}\nfunction getFooterContent(params) {\n    const { $table, column, _columnIndex, items, row } = params;\n    const { slots, editRender, cellRender, footerFormatter } = column;\n    const renderOpts = editRender || cellRender;\n    const footerSlot = slots ? slots.footer : null;\n    if (footerSlot) {\n        return $table.callSlot(footerSlot, params);\n    }\n    if (renderOpts) {\n        const compConf = renderer.get(renderOpts.name);\n        if (compConf) {\n            const rtFooter = compConf.renderTableFooter || compConf.renderFooter;\n            if (rtFooter) {\n                return getSlotVNs(rtFooter(renderOpts, params));\n            }\n        }\n    }\n    let itemValue = '';\n    // 兼容老模式\n    if (XEUtils.isArray(items)) {\n        itemValue = items[_columnIndex];\n        return [\n            footerFormatter\n                ? formatFooterLabel(footerFormatter, {\n                    itemValue,\n                    column,\n                    row,\n                    items,\n                    _columnIndex\n                })\n                : formatText(itemValue, 1)\n        ];\n    }\n    itemValue = XEUtils.get(row, column.field);\n    return [\n        footerFormatter\n            ? formatFooterLabel(footerFormatter, {\n                itemValue,\n                column,\n                row,\n                items,\n                _columnIndex\n            })\n            : formatText(itemValue, 1)\n    ];\n}\nfunction getDefaultCellLabel(params) {\n    const { $table, row, column } = params;\n    return formatText($table.getCellLabel(row, column), 1);\n}\nexport const Cell = {\n    createColumn($xeTable, columnOpts) {\n        const { type, sortable, filters, editRender, treeNode } = columnOpts;\n        const { props } = $xeTable;\n        const { editConfig } = props;\n        const { computeEditOpts, computeCheckboxOpts } = $xeTable.getComputeMaps();\n        const checkboxOpts = computeCheckboxOpts.value;\n        const editOpts = computeEditOpts.value;\n        const renConfs = {\n            renderHeader: Cell.renderDefaultHeader,\n            renderCell: treeNode ? Cell.renderTreeCell : Cell.renderDefaultCell,\n            renderFooter: Cell.renderDefaultFooter\n        };\n        switch (type) {\n            case 'seq':\n                renConfs.renderHeader = Cell.renderSeqHeader;\n                renConfs.renderCell = treeNode ? Cell.renderTreeIndexCell : Cell.renderSeqCell;\n                break;\n            case 'radio':\n                renConfs.renderHeader = Cell.renderRadioHeader;\n                renConfs.renderCell = treeNode ? Cell.renderTreeRadioCell : Cell.renderRadioCell;\n                break;\n            case 'checkbox':\n                renConfs.renderHeader = Cell.renderCheckboxHeader;\n                renConfs.renderCell = checkboxOpts.checkField ? (treeNode ? Cell.renderTreeSelectionCellByProp : Cell.renderCheckboxCellByProp) : (treeNode ? Cell.renderTreeSelectionCell : Cell.renderCheckboxCell);\n                break;\n            case 'expand':\n                renConfs.renderCell = Cell.renderExpandCell;\n                renConfs.renderData = Cell.renderExpandData;\n                break;\n            case 'html':\n                renConfs.renderCell = treeNode ? Cell.renderTreeHTMLCell : Cell.renderHTMLCell;\n                if (filters && sortable) {\n                    renConfs.renderHeader = Cell.renderSortAndFilterHeader;\n                }\n                else if (sortable) {\n                    renConfs.renderHeader = Cell.renderSortHeader;\n                }\n                else if (filters) {\n                    renConfs.renderHeader = Cell.renderFilterHeader;\n                }\n                break;\n            default:\n                if (editConfig && editRender) {\n                    renConfs.renderHeader = Cell.renderEditHeader;\n                    renConfs.renderCell = editOpts.mode === 'cell' ? (treeNode ? Cell.renderTreeCellEdit : Cell.renderCellEdit) : (treeNode ? Cell.renderTreeRowEdit : Cell.renderRowEdit);\n                }\n                else if (filters && sortable) {\n                    renConfs.renderHeader = Cell.renderSortAndFilterHeader;\n                }\n                else if (sortable) {\n                    renConfs.renderHeader = Cell.renderSortHeader;\n                }\n                else if (filters) {\n                    renConfs.renderHeader = Cell.renderFilterHeader;\n                }\n        }\n        return createColumn($xeTable, columnOpts, renConfs);\n    },\n    /**\n     * 列头标题\n     */\n    renderHeaderTitle(params) {\n        const { $table, column } = params;\n        const { slots, editRender, cellRender } = column;\n        const renderOpts = editRender || cellRender;\n        const headerSlot = slots ? slots.header : null;\n        if (headerSlot) {\n            return renderTitleContent(params, $table.callSlot(headerSlot, params));\n        }\n        if (renderOpts) {\n            const compConf = renderer.get(renderOpts.name);\n            if (compConf) {\n                const rtHeader = compConf.renderTableHeader || compConf.renderHeader;\n                if (rtHeader) {\n                    return renderTitleContent(params, getSlotVNs(rtHeader(renderOpts, params)));\n                }\n            }\n        }\n        return renderTitleContent(params, formatText(column.getTitle(), 1));\n    },\n    renderDefaultHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params));\n    },\n    renderDefaultCell(params) {\n        const { $table, row, column } = params;\n        const { slots, editRender, cellRender } = column;\n        const renderOpts = editRender || cellRender;\n        const defaultSlot = slots ? slots.default : null;\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));\n        }\n        if (renderOpts) {\n            const compConf = renderer.get(renderOpts.name);\n            if (compConf) {\n                const rtCell = compConf.renderTableCell || compConf.renderCell;\n                const rtDefault = compConf.renderTableDefault || compConf.renderDefault;\n                const renderFn = editRender ? rtCell : rtDefault;\n                if (renderFn) {\n                    return renderCellBaseVNs(params, getSlotVNs(renderFn(renderOpts, Object.assign({ $type: editRender ? 'edit' : 'cell' }, params))));\n                }\n            }\n        }\n        const cellValue = $table.getCellLabel(row, column);\n        const cellPlaceholder = editRender ? editRender.placeholder : '';\n        return renderCellBaseVNs(params, [\n            h('span', {\n                class: 'vxe-cell--label'\n            }, [\n                // 如果设置占位符\n                editRender && eqEmptyValue(cellValue)\n                    ? h('span', {\n                        class: 'vxe-cell--placeholder'\n                    }, formatText(getFuncText(cellPlaceholder), 1))\n                    : h('span', formatText(cellValue, 1))\n            ])\n        ]);\n    },\n    renderTreeCell(params) {\n        return Cell.renderTreeIcon(params, Cell.renderDefaultCell(params));\n    },\n    renderDefaultFooter(params) {\n        return [\n            h('span', {\n                class: 'vxe-cell--item'\n            }, getFooterContent(params))\n        ];\n    },\n    /**\n     * 树节点\n     */\n    renderTreeIcon(params, cellVNodes) {\n        const { $table, isHidden } = params;\n        const tableReactData = $table.reactData;\n        const tableInternalData = $table.internalData;\n        const { computeTreeOpts } = $table.getComputeMaps();\n        const { treeExpandedMaps, treeExpandLazyLoadedMaps } = tableReactData;\n        const { fullAllDataRowIdData } = tableInternalData;\n        const treeOpts = computeTreeOpts.value;\n        const { row, column, level } = params;\n        const { slots } = column;\n        const { indent, lazy, trigger, iconLoaded, showIcon, iconOpen, iconClose } = treeOpts;\n        const childrenField = treeOpts.children || treeOpts.childrenField;\n        const hasChildField = treeOpts.hasChild || treeOpts.hasChildField;\n        const rowChilds = row[childrenField];\n        const hasChild = rowChilds && rowChilds.length;\n        const iconSlot = slots ? slots.icon : null;\n        let hasLazyChilds = false;\n        let isActive = false;\n        let isLazyLoading = false;\n        let isLazyLoaded = false;\n        const ons = {};\n        if (iconSlot) {\n            return $table.callSlot(iconSlot, params);\n        }\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isActive = !!treeExpandedMaps[rowid];\n            if (lazy) {\n                const rest = fullAllDataRowIdData[rowid];\n                isLazyLoading = !!treeExpandLazyLoadedMaps[rowid];\n                hasLazyChilds = row[hasChildField];\n                isLazyLoaded = !!rest.treeLoaded;\n            }\n        }\n        if (!trigger || trigger === 'default') {\n            ons.onClick = (evnt) => {\n                $table.triggerTreeExpandEvent(evnt, params);\n            };\n        }\n        return [\n            h('div', {\n                class: ['vxe-cell--tree-node', {\n                        'is--active': isActive\n                    }],\n                style: {\n                    paddingLeft: `${level * indent}px`\n                }\n            }, [\n                showIcon && (lazy ? (isLazyLoaded ? hasChild : hasLazyChilds) : hasChild)\n                    ? [\n                        h('div', Object.assign({ class: 'vxe-tree--btn-wrapper' }, ons), [\n                            h('i', {\n                                class: ['vxe-tree--node-btn', isLazyLoading ? (iconLoaded || getIcon().TABLE_TREE_LOADED) : (isActive ? (iconOpen || getIcon().TABLE_TREE_OPEN) : (iconClose || getIcon().TABLE_TREE_CLOSE))]\n                            })\n                        ])\n                    ]\n                    : null,\n                h('div', {\n                    class: 'vxe-tree-cell'\n                }, cellVNodes)\n            ])\n        ];\n    },\n    /**\n     * 序号\n     */\n    renderSeqHeader(params) {\n        const { $table, column } = params;\n        const { slots } = column;\n        const headerSlot = slots ? slots.header : null;\n        return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot ? $table.callSlot(headerSlot, params) : formatText(column.getTitle(), 1)));\n    },\n    renderSeqCell(params) {\n        const { $table, column } = params;\n        const tableProps = $table.props;\n        const { treeConfig } = tableProps;\n        const { computeSeqOpts } = $table.getComputeMaps();\n        const seqOpts = computeSeqOpts.value;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));\n        }\n        const { seq } = params;\n        const seqMethod = seqOpts.seqMethod;\n        return renderCellBaseVNs(params, [\n            h('span', `${formatText(seqMethod ? seqMethod(params) : treeConfig ? seq : (seqOpts.startIndex || 0) + seq, 1)}`)\n        ]);\n    },\n    renderTreeIndexCell(params) {\n        return Cell.renderTreeIcon(params, Cell.renderSeqCell(params));\n    },\n    /**\n     * 单选\n     */\n    renderRadioHeader(params) {\n        const { $table, column } = params;\n        const { slots } = column;\n        const headerSlot = slots ? slots.header : null;\n        const titleSlot = slots ? slots.title : null;\n        return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot\n            ? $table.callSlot(headerSlot, params)\n            : [\n                h('span', {\n                    class: 'vxe-radio--label'\n                }, titleSlot ? $table.callSlot(titleSlot, params) : formatText(column.getTitle(), 1))\n            ]));\n    },\n    renderRadioCell(params) {\n        const { $table, column, isHidden } = params;\n        const tableReactData = $table.reactData;\n        const { computeRadioOpts } = $table.getComputeMaps();\n        const { selectRadioRow } = tableReactData;\n        const radioOpts = computeRadioOpts.value;\n        const { slots } = column;\n        const { labelField, checkMethod, visibleMethod } = radioOpts;\n        const { row } = params;\n        const defaultSlot = slots ? slots.default : null;\n        const radioSlot = slots ? slots.radio : null;\n        const isChecked = $table.eqRow(row, selectRadioRow);\n        const isVisible = !visibleMethod || visibleMethod({ row });\n        let isDisabled = !!checkMethod;\n        let ons;\n        if (!isHidden) {\n            ons = {\n                onClick(evnt) {\n                    if (!isDisabled && isVisible) {\n                        $table.triggerRadioRowEvent(evnt, params);\n                    }\n                }\n            };\n            if (checkMethod) {\n                isDisabled = !checkMethod({ row });\n            }\n        }\n        const radioParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible });\n        if (radioSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(radioSlot, radioParams));\n        }\n        const radioVNs = [];\n        if (isVisible) {\n            radioVNs.push(h('span', {\n                class: ['vxe-radio--icon', isChecked ? getIcon().TABLE_RADIO_CHECKED : getIcon().TABLE_RADIO_UNCHECKED]\n            }));\n        }\n        if (defaultSlot || labelField) {\n            radioVNs.push(h('span', {\n                class: 'vxe-radio--label'\n            }, defaultSlot ? $table.callSlot(defaultSlot, radioParams) : XEUtils.get(row, labelField)));\n        }\n        return renderCellBaseVNs(params, [\n            h('span', Object.assign({ class: ['vxe-cell--radio', {\n                        'is--checked': isChecked,\n                        'is--disabled': isDisabled\n                    }] }, ons), radioVNs)\n        ]);\n    },\n    renderTreeRadioCell(params) {\n        return Cell.renderTreeIcon(params, Cell.renderRadioCell(params));\n    },\n    /**\n     * 多选\n     */\n    renderCheckboxHeader(params) {\n        const { $table, column, isHidden } = params;\n        const tableReactData = $table.reactData;\n        const { computeIsAllCheckboxDisabled, computeCheckboxOpts } = $table.getComputeMaps();\n        const { isAllSelected: isAllCheckboxSelected, isIndeterminate: isAllCheckboxIndeterminate } = tableReactData;\n        const isAllCheckboxDisabled = computeIsAllCheckboxDisabled.value;\n        const { slots } = column;\n        const headerSlot = slots ? slots.header : null;\n        const titleSlot = slots ? slots.title : null;\n        const checkboxOpts = computeCheckboxOpts.value;\n        const headerTitle = column.getTitle();\n        const ons = {};\n        if (!isHidden) {\n            ons.onClick = (evnt) => {\n                if (!isAllCheckboxDisabled) {\n                    $table.triggerCheckAllEvent(evnt, !isAllCheckboxSelected);\n                }\n            };\n        }\n        const checkboxParams = Object.assign(Object.assign({}, params), { checked: isAllCheckboxSelected, disabled: isAllCheckboxDisabled, indeterminate: isAllCheckboxIndeterminate });\n        if (headerSlot) {\n            return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, $table.callSlot(headerSlot, checkboxParams)));\n        }\n        if (checkboxOpts.checkStrictly ? !checkboxOpts.showHeader : checkboxOpts.showHeader === false) {\n            return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [\n                h('span', {\n                    class: 'vxe-checkbox--label'\n                }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : headerTitle)\n            ]));\n        }\n        return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [\n            h('span', Object.assign({ class: ['vxe-cell--checkbox', {\n                        'is--checked': isAllCheckboxSelected,\n                        'is--disabled': isAllCheckboxDisabled,\n                        'is--indeterminate': isAllCheckboxIndeterminate\n                    }], title: getI18n('vxe.table.allTitle') }, ons), [\n                h('span', {\n                    class: ['vxe-checkbox--icon', isAllCheckboxIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : (isAllCheckboxSelected ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED)]\n                })\n            ].concat(titleSlot || headerTitle\n                ? [\n                    h('span', {\n                        class: 'vxe-checkbox--label'\n                    }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : headerTitle)\n                ]\n                : []))\n        ]));\n    },\n    renderCheckboxCell(params) {\n        const { $table, row, column, isHidden } = params;\n        const tableProps = $table.props;\n        const tableReactData = $table.reactData;\n        const { treeConfig } = tableProps;\n        const { selectCheckboxMaps, treeIndeterminateMaps } = tableReactData;\n        const { computeCheckboxOpts } = $table.getComputeMaps();\n        const checkboxOpts = computeCheckboxOpts.value;\n        const { labelField, checkMethod, visibleMethod } = checkboxOpts;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const checkboxSlot = slots ? slots.checkbox : null;\n        let indeterminate = false;\n        let isChecked = false;\n        const isVisible = !visibleMethod || visibleMethod({ row });\n        let isDisabled = !!checkMethod;\n        const ons = {};\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isChecked = !!selectCheckboxMaps[rowid];\n            ons.onClick = (evnt) => {\n                if (!isDisabled && isVisible) {\n                    $table.triggerCheckRowEvent(evnt, params, !isChecked);\n                }\n            };\n            if (checkMethod) {\n                isDisabled = !checkMethod({ row });\n            }\n            if (treeConfig) {\n                indeterminate = !!treeIndeterminateMaps[rowid];\n            }\n        }\n        const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate });\n        if (checkboxSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));\n        }\n        const checkVNs = [];\n        if (isVisible) {\n            checkVNs.push(h('span', {\n                class: ['vxe-checkbox--icon', indeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : (isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED)]\n            }));\n        }\n        if (defaultSlot || labelField) {\n            checkVNs.push(h('span', {\n                class: 'vxe-checkbox--label'\n            }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : XEUtils.get(row, labelField)));\n        }\n        return renderCellBaseVNs(params, [\n            h('span', Object.assign({ class: ['vxe-cell--checkbox', {\n                        'is--checked': isChecked,\n                        'is--disabled': isDisabled,\n                        'is--indeterminate': indeterminate,\n                        'is--hidden': !isVisible\n                    }] }, ons), checkVNs)\n        ]);\n    },\n    renderTreeSelectionCell(params) {\n        return Cell.renderTreeIcon(params, Cell.renderCheckboxCell(params));\n    },\n    renderCheckboxCellByProp(params) {\n        const { $table, row, column, isHidden } = params;\n        const tableProps = $table.props;\n        const tableReactData = $table.reactData;\n        const { treeConfig } = tableProps;\n        const { treeIndeterminateMaps } = tableReactData;\n        const { computeCheckboxOpts } = $table.getComputeMaps();\n        const checkboxOpts = computeCheckboxOpts.value;\n        const { labelField, checkField, checkMethod, visibleMethod } = checkboxOpts;\n        const indeterminateField = checkboxOpts.indeterminateField || checkboxOpts.halfField;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const checkboxSlot = slots ? slots.checkbox : null;\n        let isIndeterminate = false;\n        let isChecked = false;\n        const isVisible = !visibleMethod || visibleMethod({ row });\n        let isDisabled = !!checkMethod;\n        const ons = {};\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isChecked = XEUtils.get(row, checkField);\n            ons.onClick = (evnt) => {\n                if (!isDisabled && isVisible) {\n                    $table.triggerCheckRowEvent(evnt, params, !isChecked);\n                }\n            };\n            if (checkMethod) {\n                isDisabled = !checkMethod({ row });\n            }\n            if (treeConfig) {\n                isIndeterminate = !!treeIndeterminateMaps[rowid];\n            }\n        }\n        const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate: isIndeterminate });\n        if (checkboxSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));\n        }\n        const checkVNs = [];\n        if (isVisible) {\n            checkVNs.push(h('span', {\n                class: ['vxe-checkbox--icon', isIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : (isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED)]\n            }));\n            if (defaultSlot || labelField) {\n                checkVNs.push(h('span', {\n                    class: 'vxe-checkbox--label'\n                }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : XEUtils.get(row, labelField)));\n            }\n        }\n        return renderCellBaseVNs(params, [\n            h('span', Object.assign({ class: ['vxe-cell--checkbox', {\n                        'is--checked': isChecked,\n                        'is--disabled': isDisabled,\n                        'is--indeterminate': indeterminateField && !isChecked ? row[indeterminateField] : isIndeterminate,\n                        'is--hidden': !isVisible\n                    }] }, ons), checkVNs)\n        ]);\n    },\n    renderTreeSelectionCellByProp(params) {\n        return Cell.renderTreeIcon(params, Cell.renderCheckboxCellByProp(params));\n    },\n    /**\n     * 展开行\n     */\n    renderExpandCell(params) {\n        const { $table, isHidden, row, column } = params;\n        const tableReactData = $table.reactData;\n        const { rowExpandedMaps, rowExpandLazyLoadedMaps } = tableReactData;\n        const { computeExpandOpts } = $table.getComputeMaps();\n        const expandOpts = computeExpandOpts.value;\n        const { lazy, labelField, iconLoaded, showIcon, iconOpen, iconClose, visibleMethod } = expandOpts;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const iconSlot = slots ? slots.icon : null;\n        let isActive = false;\n        let isLazyLoading = false;\n        if (iconSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(iconSlot, params));\n        }\n        if (!isHidden) {\n            const rowid = getRowid($table, row);\n            isActive = !!rowExpandedMaps[rowid];\n            if (lazy) {\n                isLazyLoading = !!rowExpandLazyLoadedMaps[rowid];\n            }\n        }\n        return renderCellBaseVNs(params, [\n            showIcon && (!visibleMethod || visibleMethod(params))\n                ? h('span', {\n                    class: ['vxe-table--expanded', {\n                            'is--active': isActive\n                        }],\n                    onClick(evnt) {\n                        $table.triggerRowExpandEvent(evnt, params);\n                    }\n                }, [\n                    h('i', {\n                        class: ['vxe-table--expand-btn', isLazyLoading ? (iconLoaded || getIcon().TABLE_EXPAND_LOADED) : (isActive ? (iconOpen || getIcon().TABLE_EXPAND_OPEN) : (iconClose || getIcon().TABLE_EXPAND_CLOSE))]\n                    })\n                ])\n                : renderEmptyElement($table),\n            defaultSlot || labelField\n                ? h('span', {\n                    class: 'vxe-table--expand-label'\n                }, defaultSlot ? $table.callSlot(defaultSlot, params) : XEUtils.get(row, labelField))\n                : renderEmptyElement($table)\n        ]);\n    },\n    renderExpandData(params) {\n        const { $table, column } = params;\n        const { slots, contentRender } = column;\n        const contentSlot = slots ? slots.content : null;\n        if (contentSlot) {\n            return $table.callSlot(contentSlot, params);\n        }\n        if (contentRender) {\n            const compConf = renderer.get(contentRender.name);\n            if (compConf) {\n                const rtExpand = compConf.renderTableExpand || compConf.renderExpand;\n                if (rtExpand) {\n                    return getSlotVNs(rtExpand(contentRender, params));\n                }\n            }\n        }\n        return [];\n    },\n    /**\n     * HTML 标签\n     */\n    renderHTMLCell(params) {\n        const { $table, column } = params;\n        const { slots } = column;\n        const defaultSlot = slots ? slots.default : null;\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));\n        }\n        return renderCellBaseVNs(params, [\n            h('span', {\n                class: 'vxe-cell--html',\n                innerHTML: getDefaultCellLabel(params)\n            })\n        ]);\n    },\n    renderTreeHTMLCell(params) {\n        return Cell.renderTreeIcon(params, Cell.renderHTMLCell(params));\n    },\n    /**\n     * 排序和筛选\n     */\n    renderSortAndFilterHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params).concat(Cell.renderFilterIcon(params))));\n    },\n    /**\n     * 排序\n     */\n    renderSortHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params)));\n    },\n    renderSortIcon(params) {\n        const { $table, column } = params;\n        const { computeSortOpts } = $table.getComputeMaps();\n        const sortOpts = computeSortOpts.value;\n        const { showIcon, iconLayout, iconAsc, iconDesc } = sortOpts;\n        const { order } = column;\n        if (showIcon) {\n            return [\n                h('span', {\n                    class: ['vxe-cell--sort', `vxe-cell--sort-${iconLayout}-layout`]\n                }, [\n                    h('i', {\n                        class: ['vxe-sort--asc-btn', iconAsc || getIcon().TABLE_SORT_ASC, {\n                                'sort--active': order === 'asc'\n                            }],\n                        title: getI18n('vxe.table.sortAsc'),\n                        onClick(evnt) {\n                            evnt.stopPropagation();\n                            $table.triggerSortEvent(evnt, column, 'asc');\n                        }\n                    }),\n                    h('i', {\n                        class: ['vxe-sort--desc-btn', iconDesc || getIcon().TABLE_SORT_DESC, {\n                                'sort--active': order === 'desc'\n                            }],\n                        title: getI18n('vxe.table.sortDesc'),\n                        onClick(evnt) {\n                            evnt.stopPropagation();\n                            $table.triggerSortEvent(evnt, column, 'desc');\n                        }\n                    })\n                ])\n            ];\n        }\n        return [];\n    },\n    /**\n     * 筛选\n     */\n    renderFilterHeader(params) {\n        return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderFilterIcon(params)));\n    },\n    renderFilterIcon(params) {\n        const { $table, column, hasFilter } = params;\n        const tableReactData = $table.reactData;\n        const { filterStore } = tableReactData;\n        const { computeFilterOpts } = $table.getComputeMaps();\n        const filterOpts = computeFilterOpts.value;\n        const { showIcon, iconNone, iconMatch } = filterOpts;\n        return showIcon\n            ? [\n                h('span', {\n                    class: ['vxe-cell--filter', {\n                            'is--active': filterStore.visible && filterStore.column === column\n                        }]\n                }, [\n                    h('i', {\n                        class: ['vxe-filter--btn', hasFilter ? (iconMatch || getIcon().TABLE_FILTER_MATCH) : (iconNone || getIcon().TABLE_FILTER_NONE)],\n                        title: getI18n('vxe.table.filter'),\n                        onClick(evnt) {\n                            if ($table.triggerFilterEvent) {\n                                $table.triggerFilterEvent(evnt, params.column, params);\n                            }\n                        }\n                    })\n                ])\n            ]\n            : [];\n    },\n    /**\n     * 可编辑\n     */\n    renderEditHeader(params) {\n        const { $table, column } = params;\n        const tableProps = $table.props;\n        const { computeEditOpts } = $table.getComputeMaps();\n        const { editConfig, editRules } = tableProps;\n        const editOpts = computeEditOpts.value;\n        const { sortable, filters, editRender } = column;\n        let isRequired = false;\n        if (editRules) {\n            const columnRules = XEUtils.get(editRules, column.field);\n            if (columnRules) {\n                isRequired = columnRules.some((rule) => rule.required);\n            }\n        }\n        let editIconVNs = [];\n        if (isEnableConf(editConfig)) {\n            editIconVNs = [\n                isRequired && editOpts.showAsterisk\n                    ? h('i', {\n                        class: 'vxe-cell--required-icon'\n                    })\n                    : renderEmptyElement($table),\n                isEnableConf(editRender) && editOpts.showIcon\n                    ? h('i', {\n                        class: ['vxe-cell--edit-icon', editOpts.icon || getIcon().TABLE_EDIT]\n                    })\n                    : renderEmptyElement($table)\n            ];\n        }\n        return renderHeaderCellBaseVNs(params, editIconVNs.concat(Cell.renderHeaderTitle(params))\n            .concat(sortable ? Cell.renderSortIcon(params) : [])\n            .concat(filters ? Cell.renderFilterIcon(params) : []));\n    },\n    // 行格编辑模式\n    renderRowEdit(params) {\n        const { $table, column } = params;\n        const tableReactData = $table.reactData;\n        const { editStore } = tableReactData;\n        const { actived } = editStore;\n        const { editRender } = column;\n        return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row);\n    },\n    renderTreeRowEdit(params) {\n        return Cell.renderTreeIcon(params, Cell.renderRowEdit(params));\n    },\n    // 单元格编辑模式\n    renderCellEdit(params) {\n        const { $table, column } = params;\n        const tableReactData = $table.reactData;\n        const { editStore } = tableReactData;\n        const { actived } = editStore;\n        const { editRender } = column;\n        return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row && actived.column === params.column);\n    },\n    renderTreeCellEdit(params) {\n        return Cell.renderTreeIcon(params, Cell.renderCellEdit(params));\n    },\n    runRenderer(params, isEdit) {\n        const { $table, column } = params;\n        const { slots, editRender, formatter } = column;\n        const defaultSlot = slots ? slots.default : null;\n        const editSlot = slots ? slots.edit : null;\n        const compConf = renderer.get(editRender.name);\n        const rtEdit = compConf ? (compConf.renderTableEdit || compConf.renderEdit) : null;\n        const cellParams = Object.assign({ $type: '', isEdit }, params);\n        if (isEdit) {\n            cellParams.$type = 'edit';\n            if (editSlot) {\n                return $table.callSlot(editSlot, cellParams);\n            }\n            if (rtEdit) {\n                return getSlotVNs(rtEdit(editRender, cellParams));\n            }\n            return [];\n        }\n        if (defaultSlot) {\n            return renderCellBaseVNs(params, $table.callSlot(defaultSlot, cellParams));\n        }\n        if (formatter) {\n            return renderCellBaseVNs(params, [\n                h('span', {\n                    class: 'vxe-cell--label'\n                }, getDefaultCellLabel(cellParams))\n            ]);\n        }\n        return Cell.renderDefaultCell(cellParams);\n    }\n};\nexport default Cell;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sBAAoB;AACpB,IAAM,WAAW,CAAC;AACX,IAAM,SAAS,gBAAAA,QAAQ,OAAO;AACrC,IAAI;AACG,SAAS,YAAY;AACxB,MAAI,CAAC,SAAS;AACV,cAAU,IAAI,MAAM;AACpB,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO;AACX;AACO,SAAS,WAAW;AACvB,MAAI,CAAC,SAAS;AACV,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AACX;AACO,SAAS,aAAa,UAAU,QAAQ;AAC3C,SAAO,WAAW,gBAAAA,QAAQ,WAAW,QAAQ,IAAI,SAAS,MAAM,IAAI,WAAW;AACnF;AACA,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,aAAS,GAAG,IAAI,IAAI,OAAO,YAAY,GAAG,WAAW,GAAG;AAAA,EAC5D;AACA,SAAO,SAAS,GAAG;AACvB;AACA,SAAS,cAAc,MAAM,WAAW,MAAM;AAC1C,MAAI,MAAM;AACN,UAAM,aAAa,KAAK;AACxB,SAAK,OAAO,KAAK;AACjB,SAAK,QAAQ,KAAK;AAClB,QAAI,cAAc,eAAe,SAAS,mBAAmB,eAAe,SAAS,MAAM;AACvF,WAAK,OAAO,WAAW;AACvB,WAAK,QAAQ,WAAW;AAAA,IAC5B;AACA,QAAI,cAAc,SAAS,aAAa,KAAK,iBAAiB,aAAa,IAAI,KAAK,cAAc;AAC9F,aAAO,cAAc,KAAK,cAAc,WAAW,IAAI;AAAA,IAC3D;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,KAAK,KAAK;AACtB,SAAO,OAAO,aAAa,KAAK,GAAG;AACvC;AACO,SAAS,QAAQ,KAAK;AACzB,SAAO,OAAO,SAAS,KAAK,GAAG;AACnC;AACO,SAAS,SAAS,MAAM,KAAK;AAChC,SAAO,QAAQ,KAAK,aAAa,KAAK,UAAU,SAAS,KAAK,UAAU,MAAM,SAAS,GAAG,CAAC;AAC/F;AACO,SAAS,YAAY,MAAM,KAAK;AACnC,MAAI,QAAQ,SAAS,MAAM,GAAG,GAAG;AAC7B,SAAK,YAAY,KAAK,UAAU,QAAQ,SAAS,GAAG,GAAG,EAAE;AAAA,EAC7D;AACJ;AACO,SAAS,SAAS,MAAM,KAAK;AAChC,MAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,GAAG;AAC9B,gBAAY,MAAM,GAAG;AACrB,SAAK,YAAY,GAAG,KAAK,SAAS,IAAI,GAAG;AAAA,EAC7C;AACJ;AACO,SAAS,aAAa;AACzB,QAAM,kBAAkB,SAAS;AACjC,QAAM,WAAW,SAAS;AAC1B,SAAO;AAAA,IACH,WAAW,gBAAgB,aAAa,SAAS;AAAA,IACjD,YAAY,gBAAgB,cAAc,SAAS;AAAA,IACnD,eAAe,gBAAgB,gBAAgB,SAAS;AAAA,IACxD,cAAc,gBAAgB,eAAe,SAAS;AAAA,EAC1D;AACJ;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,OAAO,KAAK,eAAe;AACtC;AACO,SAAS,wBAAwB,MAAM;AAC1C,MAAI,MAAM;AACN,UAAM,gBAAgB,iBAAiB,IAAI;AAC3C,UAAM,aAAa,gBAAAA,QAAQ,SAAS,cAAc,UAAU;AAC5D,UAAM,gBAAgB,gBAAAA,QAAQ,SAAS,cAAc,aAAa;AAClE,WAAO,aAAa;AAAA,EACxB;AACA,SAAO;AACX;AACO,SAAS,aAAa,MAAM,WAAW;AAC1C,MAAI,MAAM;AACN,SAAK,YAAY;AAAA,EACrB;AACJ;AACO,SAAS,cAAc,MAAM,YAAY;AAC5C,MAAI,MAAM;AACN,SAAK,aAAa;AAAA,EACtB;AACJ;AAOO,SAAS,gBAAgB,cAAc,QAAQ;AAClD,QAAM,UAAU,OAAO,SAAS,SAAS,aAAa,YAAY,aAAa;AAC/E,MAAI,aAAa,aAAa,OAAO,MAAM,SAAS;AAChD,iBAAa,aAAa,SAAS,OAAO;AAAA,EAC9C;AACJ;AAIO,SAAS,mBAAmB,MAAM,WAAW,UAAU,aAAa;AACvE,MAAI;AACJ,MAAI,SAAU,KAAK,OAAO,cAAc,KAAK,WAAa,KAAK,aAAa,EAAE,CAAC,KAAK,KAAK,SAAU,KAAK;AACxG,SAAO,UAAU,OAAO,YAAY,WAAW,UAAU;AACrD,QAAI,YAAY,SAAS,QAAQ,QAAQ,MAAM,CAAC,eAAe,YAAY,MAAM,IAAI;AACjF,mBAAa;AAAA,IACjB,WACS,WAAW,WAAW;AAC3B,aAAO,EAAE,MAAM,WAAW,CAAC,CAAC,aAAa,MAAM,WAAW,WAAuB;AAAA,IACrF;AACA,aAAS,OAAO;AAAA,EACpB;AACA,SAAO,EAAE,MAAM,MAAM;AACzB;AAIO,SAAS,aAAa,MAAM,WAAW;AAC1C,SAAO,cAAc,MAAM,WAAW,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;AAC7D;AACO,SAAS,eAAe,MAAM;AACjC,QAAM,WAAW,KAAK,sBAAsB;AAC5C,QAAM,cAAc,SAAS;AAC7B,QAAM,eAAe,SAAS;AAC9B,QAAM,EAAE,WAAW,YAAY,eAAe,aAAa,IAAI,WAAW;AAC1E,SAAO,EAAE,aAAa,KAAK,YAAY,aAAa,cAAc,MAAM,aAAa,cAAc,eAAe,aAAa;AACnI;AACA,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AAChB,SAAS,aAAa,MAAM;AAC/B,MAAI,MAAM;AACN,QAAI,KAAK,sBAAsB,GAAG;AAC9B,WAAK,sBAAsB,EAAE;AAAA,IACjC,WACS,KAAK,cAAc,GAAG;AAC3B,WAAK,cAAc,EAAE;AAAA,IACzB;AAAA,EACJ;AACJ;AACO,SAAS,aAAa,YAAY,MAAM;AAC3C,MAAI,YAAY;AACZ,eAAW,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,EAC5C;AACJ;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,QAAQ,KAAK,aAAa;AACrC;;;ACzJA,IAAAC,mBAAoB;;;ACDpB,IAAAC,mBAAoB;AAKpB,IAAM,EAAE,SAAS,QAAQ,IAAI;AACtB,IAAM,aAAN,MAAiB;AAAA;AAAA,EAEpB,YAAY,UAAU,KAAK,EAAE,cAAc,YAAY,cAAc,WAAW,IAAI,CAAC,GAAG;AACpF,UAAM,UAAU,SAAS;AACzB,UAAM,YAAY,IAAI;AACtB,UAAM,UAAU,iBAAAC,QAAQ,UAAU,IAAI,OAAO,IAAI,IAAI,UAAU;AAC/D,UAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,QAAI,MAAwC;AACxC,YAAM,QAAQ,CAAC,OAAO,YAAY,SAAS,UAAU,MAAM;AAC3D,UAAI,IAAI,QAAQ,MAAM,QAAQ,IAAI,IAAI,MAAM,IAAI;AAC5C,gBAAQ,qBAAqB,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,MACvE;AACA,UAAI,iBAAAA,QAAQ,UAAU,IAAI,UAAU,KAAM,IAAI,cAAc,CAAC,iBAAAA,QAAQ,SAAS,IAAI,UAAU,GAAI;AAC5F,gBAAQ,qBAAqB,CAAC,sBAAsB,IAAI,UAAU,IAAI,uBAAuB,CAAC;AAAA,MAClG;AACA,UAAI,iBAAAA,QAAQ,UAAU,IAAI,UAAU,KAAM,IAAI,cAAc,CAAC,iBAAAA,QAAQ,SAAS,IAAI,UAAU,GAAI;AAC5F,gBAAQ,qBAAqB,CAAC,sBAAsB,IAAI,UAAU,IAAI,uBAAuB,CAAC;AAAA,MAClG;AACA,UAAI,IAAI,cAAc,IAAI,YAAY;AAClC,gBAAQ,0BAA0B,CAAC,sBAAsB,oBAAoB,CAAC;AAAA,MAClF;AACA,UAAI,IAAI,SAAS,UAAU;AACvB,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,gBAAgB,IAAI,SAAS,eAAe;AACpD,cAAM,WAAW,gBAAgB;AACjC,YAAI,eAAe,SAAS,YAAY,SAAS,OAAO;AACpD,iBAAO,0BAA0B,CAAC,wBAAwB,oBAAoB,CAAC;AAAA,QACnF;AAAA,MACJ;AACA,UAAI,WAAW;AACX,YAAI,iBAAAA,QAAQ,SAAS,SAAS,GAAG;AAC7B,gBAAM,cAAc,QAAQ,IAAI,SAAS,KAAK,iBAAAA,QAAQ,SAAS;AAC/D,cAAI,CAAC,eAAe,CAAC,iBAAAA,QAAQ,WAAW,YAAY,yBAAyB,YAAY,gBAAgB,GAAG;AACxG,mBAAO,wBAAwB,CAAC,SAAS,CAAC;AAAA,UAC9C;AAAA,QACJ,WACS,iBAAAA,QAAQ,QAAQ,SAAS,GAAG;AACjC,gBAAM,cAAc,QAAQ,IAAI,UAAU,CAAC,CAAC,KAAK,iBAAAA,QAAQ,UAAU,CAAC,CAAC;AACrE,cAAI,CAAC,eAAe,CAAC,iBAAAA,QAAQ,WAAW,YAAY,yBAAyB,YAAY,gBAAgB,GAAG;AACxG,mBAAO,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC;AAAA,UACjD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,OAAO,MAAM;AAAA;AAAA,MAEhB,MAAM,IAAI;AAAA,MACV,UAAU,IAAI;AAAA,MACd,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,MACd,WAAW,IAAI;AAAA,MACf,OAAO,IAAI;AAAA,MACX,OAAO,IAAI;AAAA,MACX,aAAa,IAAI;AAAA,MACjB,aAAa,IAAI;AAAA,MACjB,cAAc,IAAI;AAAA,MAClB,oBAAoB,IAAI;AAAA,MACxB,oBAAoB,IAAI;AAAA,MACxB,WAAW,IAAI;AAAA,MACf,iBAAiB,IAAI;AAAA,MACrB,iBAAiB,IAAI;AAAA,MACrB;AAAA,MACA,iBAAiB,IAAI;AAAA,MACrB,UAAU,IAAI;AAAA,MACd,QAAQ,IAAI;AAAA,MACZ,UAAU,IAAI;AAAA,MACd,SAAS,UAAU,IAAI,OAAO;AAAA,MAC9B,gBAAgB,iBAAAA,QAAQ,UAAU,IAAI,cAAc,IAAI,IAAI,iBAAiB;AAAA,MAC7E,cAAc,IAAI;AAAA,MAClB,mBAAmB,IAAI;AAAA,MACvB,qBAAqB,IAAI;AAAA,MACzB,cAAc,IAAI;AAAA,MAClB,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,MACd,YAAY,IAAI;AAAA,MAChB,YAAY,IAAI;AAAA,MAChB,eAAe,IAAI;AAAA,MACnB,oBAAoB,IAAI;AAAA,MACxB,cAAc,IAAI;AAAA,MAClB,oBAAoB,IAAI;AAAA,MACxB,WAAW,IAAI;AAAA,MACf,aAAa,IAAI;AAAA,MACjB,aAAa,IAAI;AAAA;AAAA,MAEjB,QAAQ,IAAI;AAAA;AAAA,MAEZ,IAAI,IAAI,SAAS,iBAAAA,QAAQ,SAAS,MAAM;AAAA,MACxC,UAAU;AAAA,MACV;AAAA;AAAA,MAEA,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc,IAAI;AAAA,MAClB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA;AAAA,MAEV,OAAO;AAAA;AAAA,MAEP,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;AAAA,MAET,OAAO;AAAA,MACP,UAAU;AAAA;AAAA,MAEV,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY,CAAC;AAAA,MACb,OAAO,CAAC;AAAA,MACR,cAAc,gBAAgB,IAAI;AAAA,MAClC,YAAY,cAAc,IAAI;AAAA,MAC9B,cAAc,gBAAgB,IAAI;AAAA,MAClC;AAAA;AAAA,MAEA,OAAO,IAAI;AAAA,IACf,CAAC;AACD,QAAI,SAAS;AACT,YAAM,EAAE,iBAAiB,IAAI,QAAQ,eAAe;AACpD,YAAM,YAAY,iBAAiB;AACnC,UAAI,UAAU,cAAc;AACxB,kBAAU,aAAa,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC;AAAA,MAC3D;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAO,YAAY,KAAK,UAAU,KAAK,SAAS,QAAQ,QAAQ,oBAAoB,IAAI,GAAG;AAAA,EAC/F;AAAA,EACA,SAAS;AACL,UAAM,EAAE,KAAK,IAAI;AACjB,WAAO,KAAK,UAAU,OAAO,QAAQ,IAAI,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,MAAM,OAAO;AAEhB,QAAI,SAAS,WAAW;AACpB,UAAI,SAAS,SAAS;AAElB,aAAK,WAAW;AAAA,MACpB;AACA,WAAK,IAAI,IAAI;AAAA,IACjB;AAAA,EACJ;AACJ;;;AD5JA,IAAM,uBAAuB,CAAC,SAAS,iBAAiB;AACpD,QAAM,SAAS,CAAC;AAChB,UAAQ,QAAQ,CAAC,WAAW;AACxB,WAAO,WAAW,eAAe,aAAa,KAAK;AACnD,QAAI,OAAO,SAAS;AAChB,UAAI,OAAO,YAAY,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,CAACC,YAAWA,QAAO,OAAO,GAAG;AAC/F,eAAO,KAAK,MAAM;AAClB,eAAO,KAAK,GAAG,qBAAqB,OAAO,UAAU,MAAM,CAAC;AAAA,MAChE,OACK;AACD,eAAO,KAAK,MAAM;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACO,IAAM,4BAA4B,CAAC,kBAAkB;AACxD,MAAI,WAAW;AACf,QAAM,WAAW,CAAC,QAAQ,WAAW;AACjC,QAAI,QAAQ;AACR,aAAO,QAAQ,OAAO,QAAQ;AAC9B,UAAI,WAAW,OAAO,OAAO;AACzB,mBAAW,OAAO;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,OAAO,YAAY,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,CAACA,YAAWA,QAAO,OAAO,GAAG;AAC/F,UAAI,UAAU;AACd,aAAO,SAAS,QAAQ,CAAC,cAAc;AACnC,YAAI,UAAU,SAAS;AACnB,mBAAS,WAAW,MAAM;AAC1B,qBAAW,UAAU;AAAA,QACzB;AAAA,MACJ,CAAC;AACD,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,UAAU;AAAA,IACrB;AAAA,EACJ;AACA,gBAAc,QAAQ,CAAC,WAAW;AAC9B,WAAO,QAAQ;AACf,aAAS,MAAM;AAAA,EACnB,CAAC;AACD,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,SAAK,KAAK,CAAC,CAAC;AAAA,EAChB;AACA,QAAM,aAAa,qBAAqB,aAAa;AACrD,aAAW,QAAQ,CAAC,WAAW;AAC3B,QAAI,OAAO,YAAY,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,CAACA,YAAWA,QAAO,OAAO,GAAG;AAC/F,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,UAAU,WAAW,OAAO,QAAQ;AAAA,IAC/C;AACA,SAAK,OAAO,QAAQ,CAAC,EAAE,KAAK,MAAM;AAAA,EACtC,CAAC;AACD,SAAO;AACX;AACO,SAAS,sBAAsB,UAAU,YAAY,WAAW;AACnE,QAAM,eAAe,SAAS;AAC9B,SAAO,SAAS,YAAY,EAAE,KAAK,MAAM;AACrC,QAAI,cAAc,WAAW;AAEzB,mBAAa,iBAAiB;AAC9B,mBAAa,gBAAgB;AAC7B,mBAAa,kBAAkB;AAC/B,mBAAa,eAAe;AAC5B,mBAAa,iBAAiB;AAC9B,mBAAa,iBAAiB;AAE9B,aAAO,SAAS,SAAS,YAAY,SAAS;AAAA,IAClD;AAAA,EACJ,CAAC;AACL;AAIO,SAAS,iBAAiB;AAC7B,SAAO,iBAAAC,QAAQ,SAAS,MAAM;AAClC;AAEO,SAAS,UAAU,UAAU;AAChC,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,EAAE,eAAe,IAAI,SAAS,eAAe;AACnD,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,UAAU,eAAe;AAC/B,SAAO,SAAS,QAAQ,YAAY;AACxC;AAEO,SAAS,SAAS,UAAU,KAAK;AACpC,QAAM,QAAQ,iBAAAA,QAAQ,IAAI,KAAK,UAAU,QAAQ,CAAC;AAClD,SAAO,iBAAAA,QAAQ,OAAO,KAAK,IAAI,KAAK,mBAAmB,KAAK;AAChE;AACO,IAAM,sBAAsB,CAAC,UAAU,kBAAkB;AAC5D,MAAI,eAAe;AACf,WAAO,iBAAAA,QAAQ,SAAS,aAAa,IAAI,SAAS,iBAAiB,aAAa,IAAI;AAAA,EACxF;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,MAAM;AACnC,MAAI,MAAM;AACN,UAAM,gBAAgB,iBAAiB,IAAI;AAC3C,UAAM,cAAc,iBAAAA,QAAQ,SAAS,cAAc,WAAW;AAC9D,UAAM,eAAe,iBAAAA,QAAQ,SAAS,cAAc,YAAY;AAChE,WAAO,cAAc;AAAA,EACzB;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,MAAM;AACjC,MAAI,MAAM;AACN,UAAM,gBAAgB,iBAAiB,IAAI;AAC3C,UAAM,aAAa,iBAAAA,QAAQ,SAAS,cAAc,UAAU;AAC5D,UAAM,cAAc,iBAAAA,QAAQ,SAAS,cAAc,WAAW;AAC9D,WAAO,KAAK,cAAc,aAAa;AAAA,EAC3C;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,UAAU;AACtC,SAAO,KAAK,cAAc,cAAc,QAAQ;AACpD;AACO,SAAS,UAAU,SAAS;AAC/B,MAAI,WAAW,iBAAAA,QAAQ,QAAQ,OAAO,GAAG;AACrC,WAAO,QAAQ,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,YAAY,QAAQ,MAAM;AAChE,aAAO,EAAE,OAAO,OAAO,MAAM,YAAY,SAAS,CAAC,CAAC,SAAS,UAAU,CAAC,CAAC,QAAQ;AAAA,IACrF,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,IAAK,OAAO,GAAG,IAAI,IAAK,GAAG,EAAE,KAAK,EAAE;AAC9E;AACO,SAAS,aAAa,KAAK,QAAQ;AACtC,SAAO,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AACxC;AACO,SAAS,aAAa,KAAK,QAAQ,OAAO;AAC7C,SAAO,iBAAAA,QAAQ,IAAI,KAAK,OAAO,OAAO,KAAK;AAC/C;AACO,SAAS,WAAW,OAAO;AAC9B,MAAI,OAAO;AACP,UAAM,OAAO,MAAM;AACnB,QAAI,MAAM;AACN,aAAQ,KAAK,OAAO;AAAA,IACxB;AAAA,EACJ;AACA,SAAO;AACX;AAyBO,SAAS,iBAAiB,QAAQ;AACrC,QAAM,EAAE,QAAQ,QAAQ,KAAK,IAAI;AACjC,QAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,QAAM,EAAE,qBAAqB,IAAI,OAAO,eAAe;AACvD,QAAM,gBAAgB,qBAAqB;AAC3C,QAAM,EAAE,UAAU,WAAW,IAAI;AAEjC,MAAI,YAAY;AACZ,UAAM,iBAAiB,iBAAAC,QAAQ,WAAW,UAAU,IAAI,WAAW,MAAM,IAAI;AAC7E,QAAI,mBAAmB,QAAQ;AAC3B,aAAO,KAAK,IAAI,GAAG,iBAAAA,QAAQ,SAAS,cAAc,CAAC;AAAA,IACvD;AAAA,EACJ;AACA,QAAM,EAAE,oBAAoB,wBAAwB,IAAI;AACxD,QAAM,EAAE,oBAAoB,UAAU,YAAY,IAAI;AACtD,QAAM,eAAe,iBAAAA,QAAQ,YAAY,kBAAkB,KAAK,iBAAAA,QAAQ,OAAO,kBAAkB,IAAI,0BAA0B;AAC/H,QAAM,eAAe,iBAAiB;AACtC,QAAM,YAAY,iBAAiB;AACnC,QAAM,cAAc,iBAAiB,QAAQ,iBAAiB;AAC9D,QAAM,cAAc,aAAa,eAAe;AAChD,QAAM,gBAAgB,iBAAAA,QAAQ,OAAO,iBAAAA,QAAQ,SAAS,iBAAiB,IAAI,EAAE,QAAQ,KAAK,MAAM,GAAG;AACnG,QAAM,mBAAmB,wBAAwB,IAAI,IAAI,wBAAwB,iBAAiB,MAAM,EAAE,CAAC;AAC3G,MAAI,SAAS,gBAAgB;AAE7B,MAAI,aAAa;AACb,UAAM,gBAAgB,wBAAwB,iBAAiB,MAAM,yBAAyB,CAAC;AAC/F,UAAM,oBAAoB,wBAAwB,iBAAiB,MAAM,sBAAsB,CAAC;AAChG,UAAM,oBAAoB,sBAAsB,iBAAiB,MAAM,2BAA2B,CAAC;AACnG,UAAM,gBAAgB,sBAAsB,iBAAiB,MAAM,uBAAuB,CAAC;AAC3F,UAAM,kBAAkB,sBAAsB,iBAAiB,MAAM,8BAA8B,CAAC;AACpG,UAAM,kBAAkB,sBAAsB,iBAAiB,MAAM,8BAA8B,CAAC;AACpG,UAAM,gBAAgB,sBAAsB,iBAAiB,MAAM,kBAAkB,CAAC;AACtF,UAAM,kBAAkB,sBAAsB,iBAAiB,MAAM,oBAAoB,CAAC;AAC1F,cAAU,gBAAgB,oBAAoB,oBAAoB,gBAAgB,kBAAkB,kBAAkB,kBAAkB;AAAA,EAC5I;AAEA,MAAI,aAAa;AACb,UAAM,EAAE,aAAa,IAAI,OAAO,WAAW;AAC3C,UAAM,YAAY,aAAa;AAC/B,UAAM,WAAW,YAAY,UAAU,MAAM;AAC7C,QAAI,UAAU;AACV,UAAI,QAAQ,WAAW,GAAG;AACtB,cAAM,YAAY,SAAS,cAAc;AACzC,cAAM,YAAY,YAAY;AAC9B,eAAO,KAAK,IAAI,QAAQ,KAAK,MAAM,iBAAAA,QAAQ,UAAU,WAAW,IAAI,SAAS,CAAC;AAAA,MAClF,WACS,KAAK,WAAW,GAAG;AACxB,eAAO,KAAK,IAAI,QAAQ,iBAAAA,QAAQ,UAAU,WAAW,CAAC;AAAA,MAC1D;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,aAAa,QAAQ;AACjC,SAAO,WAAW,OAAO,gBAAgB,cAAc,kBAAkB;AAC7E;AACO,SAAS,aAAa,UAAU,SAAS,eAAe;AAC3D,SAAO,aAAa,OAAO,IAAI,UAAU,SAAS,IAAI,WAAW,UAAU,SAAS,aAAa,CAAC;AACtG;AACO,SAAS,YAAY,UAAU,OAAO,QAAQ;AACjD,SAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AAC/B,UAAM,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU;AAChC,aAAO,OAAO,MAAM,KAAK;AACzB,UAAI,UAAU;AACV,YAAI,SAAS,WAAW;AACpB,mBAAS,UAAU,QAAQ,KAAK;AAChC,mBAAS,sBAAsB;AAAA,QACnC,WACS,CAAC,WAAW,SAAS,SAAS,YAAY,UAAU,EAAE,SAAS,IAAI,GAAG;AAC3E,mBAAS,yBAAyB;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACO,SAAS,eAAe,UAAU,MAAM,QAAQ,UAAU;AAC7D,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,aAAa,KAAK;AACxB,QAAM,eAAe,WAAW,SAAS,eAAe;AACxD,QAAM,aAAa,eAAe,aAAa,WAAW;AAC1D,MAAI,cAAc,YAAY;AAC1B,eAAW,OAAO,iBAAAA,QAAQ,aAAa,WAAW,UAAU,IAAI,GAAG,GAAG,MAAM;AAC5E,cAAU,gBAAgB,cAAc,MAAM,CAAC;AAAA,EACnD;AACJ;AACO,SAAS,cAAc,UAAU,QAAQ;AAC5C,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,WAAW,iBAAAA,QAAQ,SAAS,eAAe,UAAQ,KAAK,OAAO,OAAO,IAAI,EAAE,UAAU,WAAW,CAAC;AACxG,MAAI,UAAU;AACV,aAAS,MAAM,OAAO,SAAS,OAAO,CAAC;AAAA,EAC3C;AACA,YAAU,gBAAgB,cAAc,MAAM,CAAC;AACnD;AACO,SAAS,cAAc,UAAU,QAAQ;AAC5C,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,iBAAiB,IAAI;AAC7B,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,MAAI,cAAc,OAAO;AACzB,SAAO,iBAAiB,WAAW,GAAG;AAClC,UAAMC,UAAS,iBAAiB,WAAW,EAAE;AAC7C,kBAAcA,QAAO;AACrB,QAAI,CAAC,aAAa;AACd,aAAOA;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,gBAAgB,WAAW,WAAW,cAAc;AAChE,WAAS,SAAS,GAAG,SAAS,UAAU,QAAQ,UAAU;AACtD,UAAM,EAAE,KAAK,eAAe,KAAK,eAAe,SAAS,cAAc,SAAS,aAAa,IAAI,UAAU,MAAM;AACjH,QAAI,gBAAgB,MAAM,gBAAgB,MAAM,gBAAgB,cAAc;AAC1E,UAAI,kBAAkB,aAAa,kBAAkB,cAAc;AAC/D,eAAO,EAAE,SAAS,cAAc,SAAS,aAAa;AAAA,MAC1D;AACA,UAAI,aAAa,iBAAiB,YAAY,gBAAgB,gBAAgB,gBAAgB,iBAAiB,eAAe,gBAAgB,cAAc;AACxJ,eAAO,EAAE,SAAS,GAAG,SAAS,EAAE;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,wBAAwB,UAAU;AAC9C,QAAM,EAAE,OAAO,aAAa,IAAI;AAChC,eAAa,aAAa;AAC1B,WAAS,UAAU;AACnB,WAAS,gBAAgB;AACzB,WAAS,mBAAmB;AAC5B,WAAS,cAAc;AACvB,WAAS,kBAAkB;AAC3B,WAAS,iBAAiB;AAC1B,WAAS,qBAAqB;AAC9B,WAAS,eAAe;AACxB,WAAS,gBAAgB;AACzB,WAAS,uBAAuB;AAChC,WAAS,gBAAgB;AACzB,MAAI,SAAS,aAAa;AACtB,aAAS,YAAY;AAAA,EACzB;AACA,MAAI,SAAS,kBAAkB,MAAM,kBAAkB,MAAM,cAAc;AACvE,aAAS,cAAc;AAAA,EAC3B;AACA,MAAI,SAAS,kBAAkB,MAAM,aAAa;AAC9C,aAAS,eAAe;AACxB,aAAS,kBAAkB;AAAA,EAC/B;AACA,SAAO,SAAS,YAAY;AAChC;AACO,SAAS,oBAAoB,UAAU;AAC1C,MAAI,SAAS,aAAa;AACtB,aAAS,YAAY;AAAA,EACzB;AACA,SAAO,wBAAwB,QAAQ;AAC3C;AACO,SAAS,aAAa,UAAU,KAAK;AACxC,QAAM,EAAE,WAAW,aAAa,IAAI;AACpC,QAAM,aAAa,SAAS;AAC5B,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,aAAa,IAAI,SAAS,WAAW;AAC7C,QAAM,EAAE,aAAa,YAAY,IAAI;AACrC,QAAM,EAAE,eAAe,cAAc,qBAAqB,IAAI;AAC9D,QAAM,YAAY,aAAa;AAC/B,QAAM,EAAE,UAAU,UAAU,IAAI;AAChC,QAAM,WAAW,YAAY,UAAU,MAAM;AAC7C,QAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,MAAI,kBAAkB;AACtB,WAAS,QAAQ,UAAQ;AACrB,uBAAmB,KAAK;AAAA,EAC5B,CAAC;AACD,MAAI,mBAAmB;AACvB,YAAU,QAAQ,UAAQ;AACtB,wBAAoB,KAAK;AAAA,EAC7B,CAAC;AACD,MAAI,UAAU;AACV,UAAM,aAAa,SAAS;AAC5B,UAAM,gBAAgB,SAAS;AAC/B,UAAM,SAAS,SAAS,cAAc,WAAW,KAAK,IAAI;AAC1D,QAAI,QAAQ;AACR,YAAM,iBAAiB,OAAO;AAC9B,YAAM,cAAc,OAAO,aAAa,iBAAiB,eAAe,YAAY;AACpF,YAAM,WAAW,OAAO;AAExB,UAAI,cAAc,iBAAiB,cAAc,gBAAgB,YAAY;AACzE,eAAO,SAAS,SAAS,MAAM,WAAW;AAAA,MAC9C,WACS,cAAc,YAAY,aAAa,eAAe;AAC3D,eAAO,SAAS,SAAS,MAAM,gBAAgB,QAAQ;AAAA,MAC3D;AAAA,IACJ,OACK;AAED,UAAI,aAAa;AACb,YAAI,cAAc;AACd,iBAAO,SAAS,SAAS,OAAO,SAAS,eAAe,eAAe,GAAG,IAAI,KAAK,aAAa,SAAS;AAAA,QAC7G;AACA,YAAI,YAAY;AAChB,cAAM,OAAO,qBAAqB,KAAK;AACvC,cAAM,UAAU,OAAO,KAAK,SAAS;AACrC,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,gBAAM,UAAU,cAAc,CAAC;AAC/B,gBAAM,YAAY,SAAS,UAAU,OAAO;AAC5C,cAAI,YAAY,OAAO,cAAc,OAAO;AACxC;AAAA,UACJ;AACA,gBAAMC,QAAO,qBAAqB,SAAS;AAC3C,uBAAaA,QAAOA,MAAK,SAAS;AAAA,QACtC;AACA,YAAI,YAAY,eAAe;AAC3B,iBAAO,SAAS,SAAS,MAAM,YAAY,kBAAkB,CAAC;AAAA,QAClE;AACA,eAAO,SAAS,SAAS,MAAO,YAAY,WAAY,aAAa,mBAAmB,EAAE;AAAA,MAC9F;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ,QAAQ;AAC3B;AACO,SAAS,aAAa,UAAU,QAAQ,KAAK;AAChD,QAAM,EAAE,WAAW,aAAa,IAAI;AACpC,QAAM,EAAE,aAAa,IAAI,SAAS,WAAW;AAC7C,QAAM,EAAE,aAAa,YAAY,IAAI;AACrC,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,EAAE,UAAU,UAAU,IAAI;AAChC,QAAM,YAAY,aAAa;AAC/B,QAAM,WAAW,YAAY,UAAU,MAAM;AAC7C,MAAI,OAAO,OAAO;AACd,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AACA,MAAI,kBAAkB;AACtB,WAAS,QAAQ,UAAQ;AACrB,uBAAmB,KAAK;AAAA,EAC5B,CAAC;AACD,MAAI,mBAAmB;AACvB,YAAU,QAAQ,UAAQ;AACtB,wBAAoB,KAAK;AAAA,EAC7B,CAAC;AACD,MAAI,UAAU;AACV,UAAM,YAAY,SAAS;AAC3B,UAAM,iBAAiB,SAAS;AAChC,QAAI,SAAS;AACb,QAAI,KAAK;AACL,YAAM,QAAQ,SAAS,UAAU,GAAG;AACpC,eAAS,SAAS,cAAc,WAAW,KAAK,OAAO,OAAO,EAAE,EAAE;AAAA,IACtE;AACA,QAAI,CAAC,QAAQ;AACT,eAAS,SAAS,cAAc,IAAI,OAAO,EAAE,EAAE;AAAA,IACnD;AACA,QAAI,QAAQ;AACR,YAAM,iBAAiB,OAAO;AAC9B,YAAM,eAAe,OAAO,cAAc,iBAAiB,eAAe,aAAa;AACvF,YAAM,YAAY,OAAO;AAEzB,UAAI,eAAgB,iBAAiB,iBAAkB;AACnD,eAAO,SAAS,SAAS,eAAe,kBAAkB,CAAC;AAAA,MAC/D,WACU,eAAe,YAAY,iBAAmB,YAAY,kBAAmB;AACnF,eAAO,SAAS,SAAU,eAAe,aAAc,YAAY,mBAAmB,EAAE;AAAA,MAC5F;AAAA,IACJ,OACK;AAED,UAAI,aAAa;AACb,YAAI,aAAa;AACjB,cAAM,YAAY,OAAO;AACzB,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,gBAAM,UAAU,cAAc,CAAC;AAC/B,cAAI,YAAY,UAAU,QAAQ,OAAO,OAAO,IAAI;AAChD;AAAA,UACJ;AACA,wBAAc,QAAQ;AAAA,QAC1B;AACA,YAAI,aAAa,gBAAgB;AAC7B,iBAAO,SAAS,SAAS,aAAa,kBAAkB,CAAC;AAAA,QAC7D;AACA,eAAO,SAAS,SAAU,aAAa,aAAc,YAAY,mBAAmB,EAAE;AAAA,MAC1F;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ,QAAQ;AAC3B;;;AEtcA,IAAAC,mBAAoB;AAMpB,IAAM,EAAE,SAAAC,UAAS,SAAS,UAAU,SAAAC,UAAS,mBAAmB,IAAI;AACpE,SAAS,sBAAsB,QAAQ;AACnC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,cAAc,OAAO,eAAe,OAAO;AACjD,MAAI,aAAa;AACb,WAAO,EAAE,KAAK;AAAA,MACV,OAAO,CAAC,8BAA8B,YAAY,QAAQ,QAAQ,EAAE,kBAAkB;AAAA,MACtF,aAAa,MAAM;AACf,eAAO,wBAAwB,MAAM,aAAa,MAAM;AAAA,MAC5D;AAAA,MACA,aAAa,MAAM;AACf,eAAO,uBAAuB,IAAI;AAAA,MACtC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO,mBAAmB,MAAM;AACpC;AACA,SAAS,sBAAsB,QAAQ;AACnC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,cAAc,OAAO;AAC3B,MAAI,aAAa;AACb,WAAO,EAAE,KAAK;AAAA,MACV,OAAO,CAAC,8BAA8B,YAAY,QAAQ,QAAQ,EAAE,kBAAkB;AAAA,MACtF,aAAa,MAAM;AACf,eAAO,wBAAwB,MAAM,aAAa,MAAM;AAAA,MAC5D;AAAA,MACA,aAAa,MAAM;AACf,eAAO,uBAAuB,IAAI;AAAA,MACtC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO,mBAAmB,MAAM;AACpC;AACA,SAAS,mBAAmB,QAAQ;AAChC,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,aAAa,OAAO;AAC1B,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,mBAAmB,IAAI,OAAO,eAAe;AACrD,QAAM,cAAc,mBAAmB;AACvC,QAAM,EAAE,MAAM,SAAS,eAAe,IAAI;AAC1C,QAAM,kBAAkB,mBAAmB,aAAa,WAAW,oBAAoB;AACvF,QAAM,aAAa,mBAAmB,gBAAgB,MAAM;AAC5D,QAAM,MAAM,CAAC;AACb,MAAI,YAAY,QAAQ;AACpB,QAAI,cAAc,CAAC,SAAS;AACxB,UAAI,CAAC,YAAY;AACb,eAAO,6BAA6B,MAAM,MAAM;AAAA,MACpD;AAAA,IACJ;AACA,QAAI,YAAY,OAAO;AAAA,EAC3B;AACA,SAAO,EAAE,QAAQ,OAAO,OAAO,EAAE,KAAK,MAAM,OAAO,CAAC,yBAAyB;AAAA,IACjE,gBAAgB;AAAA,EACpB,CAAC,EAAE,GAAG,GAAG,GAAG;AAAA,IAChB,EAAE,KAAK;AAAA,MACH,OAAO,SAAS,aAAa,WAAW,UAAU,OAAO,QAAQ,EAAE;AAAA,IACvE,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,kBAAkB,QAAQ,SAAS;AACxC,QAAM,EAAE,QAAQ,QAAQ,MAAM,IAAI;AAClC,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,aAAa,OAAO;AAC1B,QAAM,EAAE,YAAY,WAAW,IAAI;AACnC,QAAM,EAAE,gBAAgB,mBAAmB,IAAI,OAAO,eAAe;AACrE,QAAM,UAAU,eAAe;AAC/B,QAAM,cAAc,mBAAmB;AACvC,QAAM,EAAE,UAAU,YAAY,aAAa,cAAc,IAAI;AAC7D,QAAM,iBAAiB,kBAAkB,aAAa,WAAW,mBAAmB;AACpF,QAAM,MAAM,iBAAAC,QAAQ,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACzD,MAAI,YAAY,QAAQ,UAAU,aAAa,aAAa,WAAW,cAAc,YAAY,CAAC,kBAAkB,eAAe,MAAM,KAAK;AAC1I,QAAI,YAAY;AACZ,UAAI,cAAc,eAAe,CAAC,OAAO;AACrC,YAAI,QAAQ,mBAAmB,MAAM,CAAC;AAAA,MAC1C;AAAA,IACJ,OACK;AACD,UAAI,QAAQ,mBAAmB,MAAM,CAAC;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,QAAQ;AACtC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,EAAE,mBAAmB,sBAAsB,IAAI,OAAO,eAAe;AAC3E,QAAM,aAAa,kBAAkB;AACrC,QAAM,iBAAiB,sBAAsB;AAC7C,QAAM,EAAE,UAAU,MAAM,SAAS,YAAY,aAAa,eAAe,eAAe,IAAI;AAC5F,MAAI,WAAW,QAAQ,aAAa,CAAC,iBAAiB,cAAc,MAAM,IAAI;AAC1E,QAAI,CAAC,OAAO,UAAU,cAAc,eAAe,CAAC,OAAO,WAAW;AAClE,YAAM,aAAa,kBAAkB,eAAe,MAAM;AAC1D,YAAM,MAAM,CAAC;AACb,UAAI,YAAY,QAAQ;AACpB,YAAI,cAAc,CAAC,SAAS;AACxB,cAAI,CAAC,YAAY;AACb,mBAAO,mCAAmC,MAAM,MAAM;AAAA,UAC1D;AAAA,QACJ;AACA,YAAI,YAAY,OAAO;AAAA,MAC3B;AACA,aAAO,EAAE,QAAQ,OAAO,OAAO,EAAE,KAAK,MAAM,OAAO,CAAC,yBAAyB;AAAA,QACjE,gBAAgB;AAAA,MACpB,CAAC,EAAE,GAAG,GAAG,GAAG;AAAA,QAChB,EAAE,KAAK;AAAA,UACH,OAAO,QAAQ,QAAQ,EAAE;AAAA,QAC7B,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO,mBAAmB,MAAM;AACpC;AACA,SAAS,wBAAwB,QAAQ,SAAS;AAC9C,QAAM,MAAM;AAAA,IACR,sBAAsB,MAAM;AAAA,IAC5B,yBAAyB,MAAM;AAAA,IAC/B,GAAI,iBAAAA,QAAQ,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAAA,IACjD,sBAAsB,MAAM;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,QAAQ,SAAS;AACzC,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAM,aAAa,OAAO;AAC1B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,EAAE,mBAAmB,IAAI,OAAO,eAAe;AACrD,QAAM,EAAE,oBAAoB,wBAAwB,IAAI;AACxD,QAAM,EAAE,MAAM,mBAAmB,IAAI;AACrC,QAAM,cAAc,mBAAmB;AACvC,QAAM,aAAa,YAAY;AAC/B,QAAM,eAAe,iBAAAA,QAAQ,YAAY,kBAAkB,KAAK,iBAAAA,QAAQ,OAAO,kBAAkB,IAAI,0BAA0B;AAC/H,QAAM,YAAY,iBAAiB;AACnC,QAAM,cAAc,iBAAiB,QAAQ,iBAAiB;AAC9D,QAAM,MAAM,CAAC;AACb,MAAI,aAAa,eAAe,YAAY;AACxC,QAAI,eAAe,CAAC,SAAS;AACzB,UAAI,eAAe,WAAW;AAC1B;AAAA,MACJ;AACA,UAAI,WAAW;AACX,wBAAgB,KAAK,eAAe,MAAM;AAAA,MAC9C,WACS,eAAe,YAAY;AAChC,eAAO,0BAA0B,MAAM,MAAM;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,eAAe,YAAY;AAC3B,QAAI,eAAe,CAAC,SAAS;AACzB,UAAI,eAAe,WAAW;AAC1B;AAAA,MACJ;AACA,UAAI,eAAe,YAAY;AAC3B,eAAO,uBAAuB,IAAI;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH,SAAS,UAAU,iBAAAA,QAAQ,SAAS,OAAO,IACrC,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,mBAAmB,WAAW,QAAQ,GAAG,GAAG,CAAC,IAC9E,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,kBAAkB,GAAG,GAAG,GAAG,WAAW,OAAO,CAAC;AAAA,EACzF;AACJ;AACA,SAAS,kBAAkB,iBAAiB,QAAQ;AAChD,MAAI,iBAAAA,QAAQ,WAAW,eAAe,GAAG;AACrC,WAAO,GAAG,gBAAgB,MAAM,CAAC;AAAA,EACrC;AACA,QAAM,QAAQ,iBAAAA,QAAQ,QAAQ,eAAe;AAC7C,QAAM,cAAc,QAAQD,SAAQ,IAAI,gBAAgB,CAAC,CAAC,IAAIA,SAAQ,IAAI,eAAe;AACzF,QAAM,qBAAqB,cAAc,YAAY,8BAA8B;AACnF,MAAI,oBAAoB;AACpB,WAAO,GAAG,QAAQ,mBAAmB,QAAQ,GAAG,gBAAgB,MAAM,CAAC,CAAC,IAAI,mBAAmB,MAAM,CAAC;AAAA,EAC1G;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,QAAQ;AAC9B,QAAM,EAAE,QAAQ,QAAQ,cAAc,OAAO,IAAI,IAAI;AACrD,QAAM,EAAE,OAAO,YAAY,YAAY,gBAAgB,IAAI;AAC3D,QAAM,aAAa,cAAc;AACjC,QAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,MAAI,YAAY;AACZ,WAAO,OAAO,SAAS,YAAY,MAAM;AAAA,EAC7C;AACA,MAAI,YAAY;AACZ,UAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,QAAI,UAAU;AACV,YAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,UAAI,UAAU;AACV,eAAO,WAAW,SAAS,YAAY,MAAM,CAAC;AAAA,MAClD;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,YAAY;AAEhB,MAAI,iBAAAC,QAAQ,QAAQ,KAAK,GAAG;AACxB,gBAAY,MAAM,YAAY;AAC9B,WAAO;AAAA,MACH,kBACM,kBAAkB,iBAAiB;AAAA,QACjC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC,IACC,WAAW,WAAW,CAAC;AAAA,IACjC;AAAA,EACJ;AACA,cAAY,iBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AACzC,SAAO;AAAA,IACH,kBACM,kBAAkB,iBAAiB;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,IACC,WAAW,WAAW,CAAC;AAAA,EACjC;AACJ;AACA,SAAS,oBAAoB,QAAQ;AACjC,QAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,SAAO,WAAW,OAAO,aAAa,KAAK,MAAM,GAAG,CAAC;AACzD;AACO,IAAM,OAAO;AAAA,EAChB,aAAa,UAAU,YAAY;AAC/B,UAAM,EAAE,MAAM,UAAU,SAAS,YAAY,SAAS,IAAI;AAC1D,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,iBAAiB,oBAAoB,IAAI,SAAS,eAAe;AACzE,UAAM,eAAe,oBAAoB;AACzC,UAAM,WAAW,gBAAgB;AACjC,UAAM,WAAW;AAAA,MACb,cAAc,KAAK;AAAA,MACnB,YAAY,WAAW,KAAK,iBAAiB,KAAK;AAAA,MAClD,cAAc,KAAK;AAAA,IACvB;AACA,YAAQ,MAAM;AAAA,MACV,KAAK;AACD,iBAAS,eAAe,KAAK;AAC7B,iBAAS,aAAa,WAAW,KAAK,sBAAsB,KAAK;AACjE;AAAA,MACJ,KAAK;AACD,iBAAS,eAAe,KAAK;AAC7B,iBAAS,aAAa,WAAW,KAAK,sBAAsB,KAAK;AACjE;AAAA,MACJ,KAAK;AACD,iBAAS,eAAe,KAAK;AAC7B,iBAAS,aAAa,aAAa,aAAc,WAAW,KAAK,gCAAgC,KAAK,2BAA6B,WAAW,KAAK,0BAA0B,KAAK;AAClL;AAAA,MACJ,KAAK;AACD,iBAAS,aAAa,KAAK;AAC3B,iBAAS,aAAa,KAAK;AAC3B;AAAA,MACJ,KAAK;AACD,iBAAS,aAAa,WAAW,KAAK,qBAAqB,KAAK;AAChE,YAAI,WAAW,UAAU;AACrB,mBAAS,eAAe,KAAK;AAAA,QACjC,WACS,UAAU;AACf,mBAAS,eAAe,KAAK;AAAA,QACjC,WACS,SAAS;AACd,mBAAS,eAAe,KAAK;AAAA,QACjC;AACA;AAAA,MACJ;AACI,YAAI,cAAc,YAAY;AAC1B,mBAAS,eAAe,KAAK;AAC7B,mBAAS,aAAa,SAAS,SAAS,SAAU,WAAW,KAAK,qBAAqB,KAAK,iBAAmB,WAAW,KAAK,oBAAoB,KAAK;AAAA,QAC5J,WACS,WAAW,UAAU;AAC1B,mBAAS,eAAe,KAAK;AAAA,QACjC,WACS,UAAU;AACf,mBAAS,eAAe,KAAK;AAAA,QACjC,WACS,SAAS;AACd,mBAAS,eAAe,KAAK;AAAA,QACjC;AAAA,IACR;AACA,WAAO,aAAa,UAAU,YAAY,QAAQ;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ;AACtB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,OAAO,YAAY,WAAW,IAAI;AAC1C,UAAM,aAAa,cAAc;AACjC,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,QAAI,YAAY;AACZ,aAAO,mBAAmB,QAAQ,OAAO,SAAS,YAAY,MAAM,CAAC;AAAA,IACzE;AACA,QAAI,YAAY;AACZ,YAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,UAAI,UAAU;AACV,cAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,YAAI,UAAU;AACV,iBAAO,mBAAmB,QAAQ,WAAW,SAAS,YAAY,MAAM,CAAC,CAAC;AAAA,QAC9E;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,mBAAmB,QAAQ,WAAW,OAAO,SAAS,GAAG,CAAC,CAAC;AAAA,EACtE;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,CAAC;AAAA,EACzE;AAAA,EACA,kBAAkB,QAAQ;AACtB,UAAM,EAAE,QAAQ,KAAK,OAAO,IAAI;AAChC,UAAM,EAAE,OAAO,YAAY,WAAW,IAAI;AAC1C,UAAM,aAAa,cAAc;AACjC,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,MAAM,CAAC;AAAA,IACzE;AACA,QAAI,YAAY;AACZ,YAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,UAAI,UAAU;AACV,cAAM,SAAS,SAAS,mBAAmB,SAAS;AACpD,cAAM,YAAY,SAAS,sBAAsB,SAAS;AAC1D,cAAM,WAAW,aAAa,SAAS;AACvC,YAAI,UAAU;AACV,iBAAO,kBAAkB,QAAQ,WAAW,SAAS,YAAY,OAAO,OAAO,EAAE,OAAO,aAAa,SAAS,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;AAAA,QACrI;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,YAAY,OAAO,aAAa,KAAK,MAAM;AACjD,UAAM,kBAAkB,aAAa,WAAW,cAAc;AAC9D,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG;AAAA;AAAA,QAEC,cAAc,aAAa,SAAS,IAC9B,EAAE,QAAQ;AAAA,UACR,OAAO;AAAA,QACX,GAAG,WAAW,YAAY,eAAe,GAAG,CAAC,CAAC,IAC5C,EAAE,QAAQ,WAAW,WAAW,CAAC,CAAC;AAAA,MAC5C,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,eAAe,QAAQ;AACnB,WAAO,KAAK,eAAe,QAAQ,KAAK,kBAAkB,MAAM,CAAC;AAAA,EACrE;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO;AAAA,MACH,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG,iBAAiB,MAAM,CAAC;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,QAAQ,YAAY;AAC/B,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,oBAAoB,OAAO;AACjC,UAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,UAAM,EAAE,kBAAkB,yBAAyB,IAAI;AACvD,UAAM,EAAE,qBAAqB,IAAI;AACjC,UAAM,WAAW,gBAAgB;AACjC,UAAM,EAAE,KAAK,QAAQ,MAAM,IAAI;AAC/B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,EAAE,QAAQ,MAAM,SAAS,YAAY,UAAU,UAAU,UAAU,IAAI;AAC7E,UAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,UAAM,gBAAgB,SAAS,YAAY,SAAS;AACpD,UAAM,YAAY,IAAI,aAAa;AACnC,UAAM,WAAW,aAAa,UAAU;AACxC,UAAM,WAAW,QAAQ,MAAM,OAAO;AACtC,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,QAAI,gBAAgB;AACpB,QAAI,eAAe;AACnB,UAAM,MAAM,CAAC;AACb,QAAI,UAAU;AACV,aAAO,OAAO,SAAS,UAAU,MAAM;AAAA,IAC3C;AACA,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,iBAAW,CAAC,CAAC,iBAAiB,KAAK;AACnC,UAAI,MAAM;AACN,cAAM,OAAO,qBAAqB,KAAK;AACvC,wBAAgB,CAAC,CAAC,yBAAyB,KAAK;AAChD,wBAAgB,IAAI,aAAa;AACjC,uBAAe,CAAC,CAAC,KAAK;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,CAAC,WAAW,YAAY,WAAW;AACnC,UAAI,UAAU,CAAC,SAAS;AACpB,eAAO,uBAAuB,MAAM,MAAM;AAAA,MAC9C;AAAA,IACJ;AACA,WAAO;AAAA,MACH,EAAE,OAAO;AAAA,QACL,OAAO,CAAC,uBAAuB;AAAA,UACvB,cAAc;AAAA,QAClB,CAAC;AAAA,QACL,OAAO;AAAA,UACH,aAAa,GAAG,QAAQ,MAAM;AAAA,QAClC;AAAA,MACJ,GAAG;AAAA,QACC,aAAa,OAAQ,eAAe,WAAW,gBAAiB,YAC1D;AAAA,UACE,EAAE,OAAO,OAAO,OAAO,EAAE,OAAO,wBAAwB,GAAG,GAAG,GAAG;AAAA,YAC7D,EAAE,KAAK;AAAA,cACH,OAAO,CAAC,sBAAsB,gBAAiB,cAAc,QAAQ,EAAE,oBAAsB,WAAY,YAAY,QAAQ,EAAE,kBAAoB,aAAa,QAAQ,EAAE,gBAAkB;AAAA,YAChM,CAAC;AAAA,UACL,CAAC;AAAA,QACL,IACE;AAAA,QACN,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,UAAU;AAAA,MACjB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,QAAQ;AACpB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,WAAO,wBAAwB,QAAQ,mBAAmB,QAAQ,aAAa,OAAO,SAAS,YAAY,MAAM,IAAI,WAAW,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC;AAAA,EAC1J;AAAA,EACA,cAAc,QAAQ;AAClB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,aAAa,OAAO;AAC1B,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,eAAe,IAAI,OAAO,eAAe;AACjD,UAAM,UAAU,eAAe;AAC/B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,MAAM,CAAC;AAAA,IACzE;AACA,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,YAAY,QAAQ;AAC1B,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,GAAG,WAAW,YAAY,UAAU,MAAM,IAAI,aAAa,OAAO,QAAQ,cAAc,KAAK,KAAK,CAAC,CAAC,EAAE;AAAA,IACpH,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,KAAK,eAAe,QAAQ,KAAK,cAAc,MAAM,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ;AACtB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,UAAM,YAAY,QAAQ,MAAM,QAAQ;AACxC,WAAO,wBAAwB,QAAQ,mBAAmB,QAAQ,aAC5D,OAAO,SAAS,YAAY,MAAM,IAClC;AAAA,MACE,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,MACX,GAAG,YAAY,OAAO,SAAS,WAAW,MAAM,IAAI,WAAW,OAAO,SAAS,GAAG,CAAC,CAAC;AAAA,IACxF,CAAC,CAAC;AAAA,EACV;AAAA,EACA,gBAAgB,QAAQ;AACpB,UAAM,EAAE,QAAQ,QAAQ,SAAS,IAAI;AACrC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,iBAAiB,IAAI,OAAO,eAAe;AACnD,UAAM,EAAE,eAAe,IAAI;AAC3B,UAAM,YAAY,iBAAiB;AACnC,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,EAAE,YAAY,aAAa,cAAc,IAAI;AACnD,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,YAAY,QAAQ,MAAM,QAAQ;AACxC,UAAM,YAAY,OAAO,MAAM,KAAK,cAAc;AAClD,UAAM,YAAY,CAAC,iBAAiB,cAAc,EAAE,IAAI,CAAC;AACzD,QAAI,aAAa,CAAC,CAAC;AACnB,QAAI;AACJ,QAAI,CAAC,UAAU;AACX,YAAM;AAAA,QACF,QAAQ,MAAM;AACV,cAAI,CAAC,cAAc,WAAW;AAC1B,mBAAO,qBAAqB,MAAM,MAAM;AAAA,UAC5C;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,aAAa;AACb,qBAAa,CAAC,YAAY,EAAE,IAAI,CAAC;AAAA,MACrC;AAAA,IACJ;AACA,UAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,WAAW,UAAU,YAAY,SAAS,UAAU,CAAC;AAC7H,QAAI,WAAW;AACX,aAAO,kBAAkB,QAAQ,OAAO,SAAS,WAAW,WAAW,CAAC;AAAA,IAC5E;AACA,UAAM,WAAW,CAAC;AAClB,QAAI,WAAW;AACX,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,mBAAmB,YAAY,QAAQ,EAAE,sBAAsB,QAAQ,EAAE,qBAAqB;AAAA,MAC1G,CAAC,CAAC;AAAA,IACN;AACA,QAAI,eAAe,YAAY;AAC3B,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO;AAAA,MACX,GAAG,cAAc,OAAO,SAAS,aAAa,WAAW,IAAI,iBAAAA,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,IAC9F;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,mBAAmB;AAAA,QACzC,eAAe;AAAA,QACf,gBAAgB;AAAA,MACpB,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,QAAQ;AACxB,WAAO,KAAK,eAAe,QAAQ,KAAK,gBAAgB,MAAM,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,QAAQ;AACzB,UAAM,EAAE,QAAQ,QAAQ,SAAS,IAAI;AACrC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,8BAA8B,oBAAoB,IAAI,OAAO,eAAe;AACpF,UAAM,EAAE,eAAe,uBAAuB,iBAAiB,2BAA2B,IAAI;AAC9F,UAAM,wBAAwB,6BAA6B;AAC3D,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,aAAa,QAAQ,MAAM,SAAS;AAC1C,UAAM,YAAY,QAAQ,MAAM,QAAQ;AACxC,UAAM,eAAe,oBAAoB;AACzC,UAAM,cAAc,OAAO,SAAS;AACpC,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,UAAI,UAAU,CAAC,SAAS;AACpB,YAAI,CAAC,uBAAuB;AACxB,iBAAO,qBAAqB,MAAM,CAAC,qBAAqB;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,uBAAuB,UAAU,uBAAuB,eAAe,2BAA2B,CAAC;AAC9K,QAAI,YAAY;AACZ,aAAO,wBAAwB,QAAQ,mBAAmB,gBAAgB,OAAO,SAAS,YAAY,cAAc,CAAC,CAAC;AAAA,IAC1H;AACA,QAAI,aAAa,gBAAgB,CAAC,aAAa,aAAa,aAAa,eAAe,OAAO;AAC3F,aAAO,wBAAwB,QAAQ,mBAAmB,gBAAgB;AAAA,QACtE,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,YAAY,OAAO,SAAS,WAAW,cAAc,IAAI,WAAW;AAAA,MAC3E,CAAC,CAAC;AAAA,IACN;AACA,WAAO,wBAAwB,QAAQ,mBAAmB,gBAAgB;AAAA,MACtE,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,sBAAsB;AAAA,QAC5C,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,MACzB,CAAC,GAAG,OAAOF,SAAQ,oBAAoB,EAAE,GAAG,GAAG,GAAG;AAAA,QACtD,EAAE,QAAQ;AAAA,UACN,OAAO,CAAC,sBAAsB,6BAA6B,QAAQ,EAAE,+BAAgC,wBAAwB,QAAQ,EAAE,yBAAyB,QAAQ,EAAE,wBAAyB;AAAA,QACvM,CAAC;AAAA,MACL,EAAE,OAAO,aAAa,cAChB;AAAA,QACE,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,YAAY,OAAO,SAAS,WAAW,cAAc,IAAI,WAAW;AAAA,MAC3E,IACE,CAAC,CAAC,CAAC;AAAA,IACb,CAAC,CAAC;AAAA,EACN;AAAA,EACA,mBAAmB,QAAQ;AACvB,UAAM,EAAE,QAAQ,KAAK,QAAQ,SAAS,IAAI;AAC1C,UAAM,aAAa,OAAO;AAC1B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,oBAAoB,sBAAsB,IAAI;AACtD,UAAM,EAAE,oBAAoB,IAAI,OAAO,eAAe;AACtD,UAAM,eAAe,oBAAoB;AACzC,UAAM,EAAE,YAAY,aAAa,cAAc,IAAI;AACnD,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,eAAe,QAAQ,MAAM,WAAW;AAC9C,QAAI,gBAAgB;AACpB,QAAI,YAAY;AAChB,UAAM,YAAY,CAAC,iBAAiB,cAAc,EAAE,IAAI,CAAC;AACzD,QAAI,aAAa,CAAC,CAAC;AACnB,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,kBAAY,CAAC,CAAC,mBAAmB,KAAK;AACtC,UAAI,UAAU,CAAC,SAAS;AACpB,YAAI,CAAC,cAAc,WAAW;AAC1B,iBAAO,qBAAqB,MAAM,QAAQ,CAAC,SAAS;AAAA,QACxD;AAAA,MACJ;AACA,UAAI,aAAa;AACb,qBAAa,CAAC,YAAY,EAAE,IAAI,CAAC;AAAA,MACrC;AACA,UAAI,YAAY;AACZ,wBAAgB,CAAC,CAAC,sBAAsB,KAAK;AAAA,MACjD;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,WAAW,UAAU,YAAY,SAAS,WAAW,cAAc,CAAC;AAC/I,QAAI,cAAc;AACd,aAAO,kBAAkB,QAAQ,OAAO,SAAS,cAAc,cAAc,CAAC;AAAA,IAClF;AACA,UAAM,WAAW,CAAC;AAClB,QAAI,WAAW;AACX,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,sBAAsB,gBAAgB,QAAQ,EAAE,+BAAgC,YAAY,QAAQ,EAAE,yBAAyB,QAAQ,EAAE,wBAAyB;AAAA,MAC9K,CAAC,CAAC;AAAA,IACN;AACA,QAAI,eAAe,YAAY;AAC3B,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO;AAAA,MACX,GAAG,cAAc,OAAO,SAAS,aAAa,cAAc,IAAI,iBAAAE,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,IACjG;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,sBAAsB;AAAA,QAC5C,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,cAAc,CAAC;AAAA,MACnB,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EACA,wBAAwB,QAAQ;AAC5B,WAAO,KAAK,eAAe,QAAQ,KAAK,mBAAmB,MAAM,CAAC;AAAA,EACtE;AAAA,EACA,yBAAyB,QAAQ;AAC7B,UAAM,EAAE,QAAQ,KAAK,QAAQ,SAAS,IAAI;AAC1C,UAAM,aAAa,OAAO;AAC1B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,sBAAsB,IAAI;AAClC,UAAM,EAAE,oBAAoB,IAAI,OAAO,eAAe;AACtD,UAAM,eAAe,oBAAoB;AACzC,UAAM,EAAE,YAAY,YAAY,aAAa,cAAc,IAAI;AAC/D,UAAM,qBAAqB,aAAa,sBAAsB,aAAa;AAC3E,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,eAAe,QAAQ,MAAM,WAAW;AAC9C,QAAI,kBAAkB;AACtB,QAAI,YAAY;AAChB,UAAM,YAAY,CAAC,iBAAiB,cAAc,EAAE,IAAI,CAAC;AACzD,QAAI,aAAa,CAAC,CAAC;AACnB,UAAM,MAAM,CAAC;AACb,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,kBAAY,iBAAAA,QAAQ,IAAI,KAAK,UAAU;AACvC,UAAI,UAAU,CAAC,SAAS;AACpB,YAAI,CAAC,cAAc,WAAW;AAC1B,iBAAO,qBAAqB,MAAM,QAAQ,CAAC,SAAS;AAAA,QACxD;AAAA,MACJ;AACA,UAAI,aAAa;AACb,qBAAa,CAAC,YAAY,EAAE,IAAI,CAAC;AAAA,MACrC;AACA,UAAI,YAAY;AACZ,0BAAkB,CAAC,CAAC,sBAAsB,KAAK;AAAA,MACnD;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,WAAW,UAAU,YAAY,SAAS,WAAW,eAAe,gBAAgB,CAAC;AAChK,QAAI,cAAc;AACd,aAAO,kBAAkB,QAAQ,OAAO,SAAS,cAAc,cAAc,CAAC;AAAA,IAClF;AACA,UAAM,WAAW,CAAC;AAClB,QAAI,WAAW;AACX,eAAS,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,sBAAsB,kBAAkB,QAAQ,EAAE,+BAAgC,YAAY,QAAQ,EAAE,yBAAyB,QAAQ,EAAE,wBAAyB;AAAA,MAChL,CAAC,CAAC;AACF,UAAI,eAAe,YAAY;AAC3B,iBAAS,KAAK,EAAE,QAAQ;AAAA,UACpB,OAAO;AAAA,QACX,GAAG,cAAc,OAAO,SAAS,aAAa,cAAc,IAAI,iBAAAA,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,MACjG;AAAA,IACJ;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ,OAAO,OAAO,EAAE,OAAO,CAAC,sBAAsB;AAAA,QAC5C,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,qBAAqB,sBAAsB,CAAC,YAAY,IAAI,kBAAkB,IAAI;AAAA,QAClF,cAAc,CAAC;AAAA,MACnB,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ;AAAA,IAChC,CAAC;AAAA,EACL;AAAA,EACA,8BAA8B,QAAQ;AAClC,WAAO,KAAK,eAAe,QAAQ,KAAK,yBAAyB,MAAM,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,UAAU,KAAK,OAAO,IAAI;AAC1C,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,iBAAiB,wBAAwB,IAAI;AACrD,UAAM,EAAE,kBAAkB,IAAI,OAAO,eAAe;AACpD,UAAM,aAAa,kBAAkB;AACrC,UAAM,EAAE,MAAM,YAAY,YAAY,UAAU,UAAU,WAAW,cAAc,IAAI;AACvF,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,WAAW,QAAQ,MAAM,OAAO;AACtC,QAAI,WAAW;AACf,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACV,aAAO,kBAAkB,QAAQ,OAAO,SAAS,UAAU,MAAM,CAAC;AAAA,IACtE;AACA,QAAI,CAAC,UAAU;AACX,YAAM,QAAQ,SAAS,QAAQ,GAAG;AAClC,iBAAW,CAAC,CAAC,gBAAgB,KAAK;AAClC,UAAI,MAAM;AACN,wBAAgB,CAAC,CAAC,wBAAwB,KAAK;AAAA,MACnD;AAAA,IACJ;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,aAAa,CAAC,iBAAiB,cAAc,MAAM,KAC7C,EAAE,QAAQ;AAAA,QACR,OAAO,CAAC,uBAAuB;AAAA,UACvB,cAAc;AAAA,QAClB,CAAC;AAAA,QACL,QAAQ,MAAM;AACV,iBAAO,sBAAsB,MAAM,MAAM;AAAA,QAC7C;AAAA,MACJ,GAAG;AAAA,QACC,EAAE,KAAK;AAAA,UACH,OAAO,CAAC,yBAAyB,gBAAiB,cAAc,QAAQ,EAAE,sBAAwB,WAAY,YAAY,QAAQ,EAAE,oBAAsB,aAAa,QAAQ,EAAE,kBAAoB;AAAA,QACzM,CAAC;AAAA,MACL,CAAC,IACC,mBAAmB,MAAM;AAAA,MAC/B,eAAe,aACT,EAAE,QAAQ;AAAA,QACR,OAAO;AAAA,MACX,GAAG,cAAc,OAAO,SAAS,aAAa,MAAM,IAAI,iBAAAA,QAAQ,IAAI,KAAK,UAAU,CAAC,IAClF,mBAAmB,MAAM;AAAA,IACnC,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,OAAO,cAAc,IAAI;AACjC,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,OAAO,SAAS,aAAa,MAAM;AAAA,IAC9C;AACA,QAAI,eAAe;AACf,YAAM,WAAW,SAAS,IAAI,cAAc,IAAI;AAChD,UAAI,UAAU;AACV,cAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,YAAI,UAAU;AACV,iBAAO,WAAW,SAAS,eAAe,MAAM,CAAC;AAAA,QACrD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,QAAQ;AACnB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,MAAM,CAAC;AAAA,IACzE;AACA,WAAO,kBAAkB,QAAQ;AAAA,MAC7B,EAAE,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,WAAW,oBAAoB,MAAM;AAAA,MACzC,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,mBAAmB,QAAQ;AACvB,WAAO,KAAK,eAAe,QAAQ,KAAK,eAAe,MAAM,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B,QAAQ;AAC9B,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK,eAAe,MAAM,EAAE,OAAO,KAAK,iBAAiB,MAAM,CAAC,CAAC,CAAC;AAAA,EACnJ;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACrB,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK,eAAe,MAAM,CAAC,CAAC;AAAA,EAC7G;AAAA,EACA,eAAe,QAAQ;AACnB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,UAAM,WAAW,gBAAgB;AACjC,UAAM,EAAE,UAAU,YAAY,SAAS,SAAS,IAAI;AACpD,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,UAAU;AACV,aAAO;AAAA,QACH,EAAE,QAAQ;AAAA,UACN,OAAO,CAAC,kBAAkB,kBAAkB,UAAU,SAAS;AAAA,QACnE,GAAG;AAAA,UACC,EAAE,KAAK;AAAA,YACH,OAAO,CAAC,qBAAqB,WAAW,QAAQ,EAAE,gBAAgB;AAAA,cAC1D,gBAAgB,UAAU;AAAA,YAC9B,CAAC;AAAA,YACL,OAAOF,SAAQ,mBAAmB;AAAA,YAClC,QAAQ,MAAM;AACV,mBAAK,gBAAgB;AACrB,qBAAO,iBAAiB,MAAM,QAAQ,KAAK;AAAA,YAC/C;AAAA,UACJ,CAAC;AAAA,UACD,EAAE,KAAK;AAAA,YACH,OAAO,CAAC,sBAAsB,YAAY,QAAQ,EAAE,iBAAiB;AAAA,cAC7D,gBAAgB,UAAU;AAAA,YAC9B,CAAC;AAAA,YACL,OAAOA,SAAQ,oBAAoB;AAAA,YACnC,QAAQ,MAAM;AACV,mBAAK,gBAAgB;AACrB,qBAAO,iBAAiB,MAAM,QAAQ,MAAM;AAAA,YAChD;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,QAAQ;AACvB,WAAO,wBAAwB,QAAQ,KAAK,kBAAkB,MAAM,EAAE,OAAO,KAAK,iBAAiB,MAAM,CAAC,CAAC;AAAA,EAC/G;AAAA,EACA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,QAAQ,UAAU,IAAI;AACtC,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,YAAY,IAAI;AACxB,UAAM,EAAE,kBAAkB,IAAI,OAAO,eAAe;AACpD,UAAM,aAAa,kBAAkB;AACrC,UAAM,EAAE,UAAU,UAAU,UAAU,IAAI;AAC1C,WAAO,WACD;AAAA,MACE,EAAE,QAAQ;AAAA,QACN,OAAO,CAAC,oBAAoB;AAAA,UACpB,cAAc,YAAY,WAAW,YAAY,WAAW;AAAA,QAChE,CAAC;AAAA,MACT,GAAG;AAAA,QACC,EAAE,KAAK;AAAA,UACH,OAAO,CAAC,mBAAmB,YAAa,aAAa,QAAQ,EAAE,qBAAuB,YAAY,QAAQ,EAAE,iBAAkB;AAAA,UAC9H,OAAOA,SAAQ,kBAAkB;AAAA,UACjC,QAAQ,MAAM;AACV,gBAAI,OAAO,oBAAoB;AAC3B,qBAAO,mBAAmB,MAAM,OAAO,QAAQ,MAAM;AAAA,YACzD;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL,IACE,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACrB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,aAAa,OAAO;AAC1B,UAAM,EAAE,gBAAgB,IAAI,OAAO,eAAe;AAClD,UAAM,EAAE,YAAY,UAAU,IAAI;AAClC,UAAM,WAAW,gBAAgB;AACjC,UAAM,EAAE,UAAU,SAAS,WAAW,IAAI;AAC1C,QAAI,aAAa;AACjB,QAAI,WAAW;AACX,YAAM,cAAc,iBAAAE,QAAQ,IAAI,WAAW,OAAO,KAAK;AACvD,UAAI,aAAa;AACb,qBAAa,YAAY,KAAK,CAAC,SAAS,KAAK,QAAQ;AAAA,MACzD;AAAA,IACJ;AACA,QAAI,cAAc,CAAC;AACnB,QAAI,aAAa,UAAU,GAAG;AAC1B,oBAAc;AAAA,QACV,cAAc,SAAS,eACjB,EAAE,KAAK;AAAA,UACL,OAAO;AAAA,QACX,CAAC,IACC,mBAAmB,MAAM;AAAA,QAC/B,aAAa,UAAU,KAAK,SAAS,WAC/B,EAAE,KAAK;AAAA,UACL,OAAO,CAAC,uBAAuB,SAAS,QAAQ,QAAQ,EAAE,UAAU;AAAA,QACxE,CAAC,IACC,mBAAmB,MAAM;AAAA,MACnC;AAAA,IACJ;AACA,WAAO,wBAAwB,QAAQ,YAAY,OAAO,KAAK,kBAAkB,MAAM,CAAC,EACnF,OAAO,WAAW,KAAK,eAAe,MAAM,IAAI,CAAC,CAAC,EAClD,OAAO,UAAU,KAAK,iBAAiB,MAAM,IAAI,CAAC,CAAC,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,cAAc,QAAQ;AAClB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,EAAE,QAAQ,IAAI;AACpB,UAAM,EAAE,WAAW,IAAI;AACvB,WAAO,KAAK,YAAY,QAAQ,aAAa,UAAU,KAAK,WAAW,QAAQ,QAAQ,OAAO,GAAG;AAAA,EACrG;AAAA,EACA,kBAAkB,QAAQ;AACtB,WAAO,KAAK,eAAe,QAAQ,KAAK,cAAc,MAAM,CAAC;AAAA,EACjE;AAAA;AAAA,EAEA,eAAe,QAAQ;AACnB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,iBAAiB,OAAO;AAC9B,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,EAAE,QAAQ,IAAI;AACpB,UAAM,EAAE,WAAW,IAAI;AACvB,WAAO,KAAK,YAAY,QAAQ,aAAa,UAAU,KAAK,WAAW,QAAQ,QAAQ,OAAO,OAAO,QAAQ,WAAW,OAAO,MAAM;AAAA,EACzI;AAAA,EACA,mBAAmB,QAAQ;AACvB,WAAO,KAAK,eAAe,QAAQ,KAAK,eAAe,MAAM,CAAC;AAAA,EAClE;AAAA,EACA,YAAY,QAAQ,QAAQ;AACxB,UAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,UAAM,EAAE,OAAO,YAAY,UAAU,IAAI;AACzC,UAAM,cAAc,QAAQ,MAAM,UAAU;AAC5C,UAAM,WAAW,QAAQ,MAAM,OAAO;AACtC,UAAM,WAAW,SAAS,IAAI,WAAW,IAAI;AAC7C,UAAM,SAAS,WAAY,SAAS,mBAAmB,SAAS,aAAc;AAC9E,UAAM,aAAa,OAAO,OAAO,EAAE,OAAO,IAAI,OAAO,GAAG,MAAM;AAC9D,QAAI,QAAQ;AACR,iBAAW,QAAQ;AACnB,UAAI,UAAU;AACV,eAAO,OAAO,SAAS,UAAU,UAAU;AAAA,MAC/C;AACA,UAAI,QAAQ;AACR,eAAO,WAAW,OAAO,YAAY,UAAU,CAAC;AAAA,MACpD;AACA,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,aAAa;AACb,aAAO,kBAAkB,QAAQ,OAAO,SAAS,aAAa,UAAU,CAAC;AAAA,IAC7E;AACA,QAAI,WAAW;AACX,aAAO,kBAAkB,QAAQ;AAAA,QAC7B,EAAE,QAAQ;AAAA,UACN,OAAO;AAAA,QACX,GAAG,oBAAoB,UAAU,CAAC;AAAA,MACtC,CAAC;AAAA,IACL;AACA,WAAO,KAAK,kBAAkB,UAAU;AAAA,EAC5C;AACJ;AACA,IAAO,eAAQ;", "names": ["XEUtils", "import_xe_utils", "import_xe_utils", "XEUtils", "column", "XEUtils", "XEUtils", "column", "rest", "import_xe_utils", "getI18n", "formats", "XEUtils"]}