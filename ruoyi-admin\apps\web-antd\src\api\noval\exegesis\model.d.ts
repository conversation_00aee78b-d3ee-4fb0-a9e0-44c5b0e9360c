import type { PageQuery, BaseEntity } from '#/api/common';

export interface ExegesisVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 用户id
   */
  userid: string | number;

  /**
   * 小说名字
   */
  novalName: string;

  /**
   * 小说章节
   */
  novalChapter: string;

  /**
   * 小说内容
   */
  novalContent: string;

  /**
   * 批注
   */
  exegesis: string;

  /**
   * 小说id
   */
  novalId: string | number;

}

export interface ExegesisForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 用户id
   */
  userid?: string | number;

  /**
   * 小说名字
   */
  novalName?: string;

  /**
   * 小说章节
   */
  novalChapter?: string;

  /**
   * 小说内容
   */
  novalContent?: string;

  /**
   * 批注
   */
  exegesis?: string;

  /**
   * 小说id
   */
  novalId?: string | number;

}

export interface ExegesisQuery extends PageQuery {
  /**
   * id
   */
  id?: string | number;

  /**
   * 用户id
   */
  userid?: string | number;

  /**
   * 小说名字
   */
  novalName?: string;

  /**
   * 小说章节
   */
  novalChapter?: string;

  /**
   * 小说内容
   */
  novalContent?: string;

  /**
   * 批注
   */
  exegesis?: string;

  /**
   * 小说id
   */
  novalId?: string | number;

  /**
    * 日期范围参数
    */
  params?: any;
}
