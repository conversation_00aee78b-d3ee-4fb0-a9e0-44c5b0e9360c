import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';
import { updateNews } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);

  // 更新消息
  const success = updateNews(body.id, {
    otherId: body.otherId,
    MyId: body.MyId,
    news: body.news,
    type: body.type,
    isread: body.isread,
  });

  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: '消息不存在',
    });
  }

  return useResponseSuccess(null, '更新成功');
});
